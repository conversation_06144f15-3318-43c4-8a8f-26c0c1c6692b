<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

<!--    <resultMap id="RegistrationInfoMap" type="java.util.HashMap">-->
<!--        <result property="classNameCode" column="class_name_code"/>-->
<!--        <result property="className" column="class_name"/>-->
<!--        <result property="classOpenTime" column="class_open_time"/>-->
<!--        <result property="completionTime" column="completion_time"/>-->
<!--        <result property="campus" column="campus"/>-->
<!--        <result property="peopleNumber" column="people_number"/>-->
<!--        <result property="actualPeopleNumber" column="actualPeopleNumber"/>-->
<!--        <result property="reportNumber" column="reportNumber"/>-->
<!--    </resultMap>-->

    <resultMap id="reportMap" type="com.unicom.swdx.module.edu.controller.admin.trainee.vo.ReportPageRespVO">
        <result column="card_no" property="cardNo" typeHandler="com.unicom.swdx.framework.mybatis.core.type.SM4EncryptTypeHandler"/>
    </resultMap>

    <resultMap id="reportExcelMap" type="com.unicom.swdx.module.edu.controller.admin.trainee.vo.ReportPageRespVO">
        <result column="card_no" property="cardNo" typeHandler="com.unicom.swdx.framework.mybatis.core.type.SM4EncryptTypeHandler"/>
    </resultMap>

    <resultMap id="getAllTrainMap" type="com.unicom.swdx.module.edu.controller.admin.trainee.vo.UnitRegistrationPageRespVO">
        <result column="card_no" property="cardNo" typeHandler="com.unicom.swdx.framework.mybatis.core.type.SM4EncryptTypeHandler"/>
    </resultMap>


    <sql id="traineeInfo">
        select
        et.id,
        et."name",
        et.sex,
        et.phone,
        et.job_level,
        et."position",
        et.political_identity,
        et.create_time,
        et.status
        from
        edu_trainee et
        left join edu_sign_up_unit esuu on
        et.unit_id=esuu.id
        where
        et.deleted = 0
        and esuu.parent_id = #{reqVO.unitId}
        and et.class_id = #{reqVO.classId}
        <if test="reqVO.nameOrPhone != null and reqVO.nameOrPhone != ''">
            and (et."name" like concat('%',#{reqVO.nameOrPhone},'%') or et.phone like concat('%',#{reqVO.nameOrPhone},'%'))
        </if>
        <if test="reqVO.status != null and reqVO.status != ''">
            and et.status = #{reqVO.status}
        </if>
        order by et.create_time
    </sql>

    <sql id = "unitInfo">
        (
        select
        a.id,
        a.unit_name,
        a.capacity,
        a.class_id,
        (
        select count(*)
        from edu_trainee b
        where b.unit_id = a.id
        and b.deleted = 0
        and b.class_id = a.class_id
        ) as actualPeopleNumber,
        row_number() over() as index,
        a.is_restrict,
        case when a.is_restrict = 0 then '限制' else '无限制' end as isLimit,
        CASE
        WHEN c.completion_time &lt; NOW() and c.publish = 1 THEN 6
        WHEN c.publish = 1 and c.class_open_time &lt;= NOW() AND NOW() &lt; DATE_ADD(c.completion_time, INTERVAL '1 DAY') THEN 5
        WHEN c.publish = 1 and c.registration_end_time &lt;= NOW() AND c.class_open_time > NOW() THEN 4
        WHEN c.publish = 1 and c.registration_start_time &lt;= NOW() AND c.registration_end_time > NOW() THEN 3
        WHEN c.publish = 1 THEN 2
        WHEN c.publish = 2 THEN 1
        ELSE 7
        END AS classStatus
        from edu_sign_up_unit a
        left join edu_trainee b on a.id = b.unit_id
        left join edu_class_management c on a.class_id = c.id
        where a.deleted = 0
        <if test="reqVO.classId != null and reqVO.classId != ''">
            and a.class_id = #{reqVO.classId}
        </if>
        group by a.id, c.id
        )
        UNION ALL
        (
        select
        null as id,
        '--' as unit_name,
        0 as capacity,
        b.class_id,
        count(*) as actualPeopleNumber,
        (select max(t.rn) + 1 from (
        select row_number() over() as rn
        from edu_sign_up_unit
        where deleted = 0
        <if test="reqVO.classId != null and reqVO.classId != ''">
            and class_id = #{reqVO.classId}
        </if>
        ) t) as index,
        0 as is_restrict,
        '无限制' as isLimit,
        CASE
        WHEN c.completion_time &lt; NOW() and c.publish = 1 THEN 6
        WHEN c.publish = 1 and c.class_open_time &lt;= NOW() AND NOW() &lt; DATE_ADD(c.completion_time, INTERVAL '1 DAY') THEN 5
        WHEN c.publish = 1 and c.registration_end_time &lt;= NOW() AND c.class_open_time > NOW() THEN 4
        WHEN c.publish = 1 and c.registration_start_time &lt;= NOW() AND c.registration_end_time > NOW() THEN 3
        WHEN c.publish = 1 THEN 2
        WHEN c.publish = 2 THEN 1
        ELSE 7
        END AS classStatus
        from edu_trainee b
        left join edu_class_management c on b.class_id = c.id
        where b.unit_id is null
        and b.deleted = 0
        <if test="reqVO.classId != null and reqVO.classId != ''">
            and b.class_id = #{reqVO.classId}
        </if>
        group by b.class_id, c.id
        )
        <if test = 'reqVO.orderStatus == 1 or reqVO.orderStatus == 3'>
            order by index asc
        </if>
        <if test = 'reqVO.orderStatus == 2'>
            order by index desc
        </if>
    </sql>
    <update id="setLoginTrainee">
        update system_users
        set employee_id = #{traineeId},
            update_time = now()
        where id = #{userId}
        and deleted = 0
    </update>

    <select id="getRegistrationPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.trainee.vo.RegistrationPageRespVO">
        select
            a.id,
            a.class_name_code,
            a.class_name ,
            a.class_open_time ,
            a.completion_time ,
            a.campus ,
            a.people_number ,
            a.class_attribute,
            count(case when b.deleted = false then 1 end ) actualPeopleNumber,
            count(case when b.status =798 and b.deleted = 0 then 1 end) as reportNumber,
            CASE
            WHEN a.completion_time &lt; NOW() and a.publish = 1 THEN 6
            WHEN  a.publish = 1 and a.class_open_time &lt;= NOW() AND NOW() &lt; DATE_ADD(a.completion_time, INTERVAL '1 DAY') THEN 5
            WHEN a.publish = 1 and a.registration_end_time &lt;= NOW() AND a.class_open_time > NOW() THEN 4
            WHEN a.publish = 1 and a.registration_start_time &lt;= NOW() AND a.registration_end_time > NOW() THEN 3
            WHEN a.publish = 1 THEN 2
            WHEN a.publish = 2 THEN 1
            ELSE 7
            END AS classStatus
        from
            edu_class_management a
                left join edu_trainee b on
                a.id = b.class_id
        where
            a.deleted = 0
        <if test="reqVO.className != null and reqVO.className != ''">
            and a.class_name like concat('%',#{reqVO.className},'%')
        </if>
        <if test="reqVO.year != null and reqVO.year != ''">
            and a.year = #{reqVO.year}
        </if>
        <if test="reqVO.teacherId != null">
            AND (
            a."class_teacher_lead" = #{reqVO.teacherId}
            OR a."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR a."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR))
            OR a."coach_teacher" LIKE CONCAT(CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR a."coach_teacher" = CAST(#{reqVO.teacherId} AS VARCHAR)
            )
        </if>
        <if test="reqVO.semester != null and reqVO.semester != ''">
            and a.semester = #{reqVO.semester}
        </if>
        <if test="reqVO.classAttribute != null and reqVO.classAttribute != ''">
            and a.class_attribute = #{reqVO.classAttribute}
        </if>

        <if test="reqVO.status != null and reqVO.status != ''">
            <choose>
                <when test="reqVO.status == 1">
                    and a.publish = 2
                </when>
                <when test="reqVO.status == 2">
                    and a.publish = 1
                </when>
                <when test="reqVO.status == 3">
                    and a.publish = 1 and a.registration_start_time &lt;= now() and a.registration_end_time &gt; now()
                </when>
                <when test="reqVO.status == 4">
                    and a.publish = 1 and a.registration_end_time &lt;= now() and a.class_open_time &gt; now()
                </when>
                <when test="reqVO.status == 5">
                    and a.publish = 1 and a.class_open_time &lt;= now() and  now() &lt; DATE_ADD(a.completion_time, INTERVAL '1 DAY')
                </when>
                <when test="reqVO.status == 6">
                    and a.publish = 1 and a.completion_time &lt; now()
                </when>
                <otherwise>

                </otherwise>
            </choose>
        </if>
        group by
            a.id
        <if test="reqVO.orderStatus != null and reqVO.orderStatus != ''">
            <choose>
                <when test="reqVO.orderStatus == 1">
                    order by a.class_name_code asc
                </when>
                <when test="reqVO.orderStatus == 2">
                    order by a.class_name_code desc
                </when>
                <when test="reqVO.orderStatus == 3">
                    order by a.class_name asc
                </when>
                <when test="reqVO.orderStatus == 4">
                    order by a.class_name desc
                </when>
                <when test="reqVO.orderStatus == 5">
                    order by a.class_open_time asc
                </when>
                <when test="reqVO.orderStatus == 6">
                    order by a.class_open_time desc
                </when>
                <when test="reqVO.orderStatus == 7">
                    order by a.create_time desc
                </when>
                <otherwise>
                    order by a.create_time desc
                </otherwise>
            </choose>
        </if>
    </select>
    <select id="getRegistrationInfo"
            resultType="com.unicom.swdx.module.edu.controller.admin.trainee.vo.RegistrationDetailVO">
        select
        a.id,
        a.class_name_code,
        a.class_name ,
        a.class_open_time ,
        a.completion_time ,
        a.campus ,
        a.people_number ,
        count(case when b.deleted = false then 1 end ) actualPeopleNumber,
        count(case when b.status =798 and b.deleted = 0 then 1 end) as reportNumber,
        a.class_attribute,
        CASE
        WHEN a.completion_time &lt; NOW() and a.publish = 1 THEN 6
        WHEN  a.publish = 1 and a.class_open_time &lt;= NOW() AND NOW() &lt; DATE_ADD(a.completion_time, INTERVAL '1 DAY') THEN 5
        WHEN a.publish = 1 and a.registration_end_time &lt;= NOW() AND a.class_open_time > NOW() THEN 4
        WHEN a.publish = 1 and a.registration_start_time &lt;= NOW() AND a.registration_end_time > NOW() THEN 3
        WHEN a.publish = 1 THEN 2
        WHEN a.publish = 2 THEN 1
        ELSE 7
        END AS classStatus
        from
        edu_class_management a
        left join edu_trainee b on
        a.id = b.class_id
        where
        a.deleted = 0
        <choose>
            <when test="reqVO.idList != null and reqVO.idList.size() > 0">
                and a.id in
                <foreach collection="reqVO.idList" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                <if test="reqVO.className != null and reqVO.className != ''">
                    and a.class_name like concat('%',#{reqVO.className},'%')
                </if>
                <if test="reqVO.teacherId != null">
                    AND (
                    a."class_teacher_lead" = #{reqVO.teacherId}
                    OR a."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
                    OR a."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR))
                    OR a."coach_teacher" LIKE CONCAT(CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
                    OR a."coach_teacher" = CAST(#{reqVO.teacherId} AS VARCHAR)
                    )
                </if>
                <if test="reqVO.year != null and reqVO.year != ''">
                    and a.year = #{reqVO.year}
                </if>
                <if test="reqVO.classAttribute != null and reqVO.classAttribute != ''">
                    and a.class_attribute = #{reqVO.classAttribute}
                </if>
                <if test="reqVO.semester != null and reqVO.semester != ''">
                    and a.semester = #{reqVO.semester}
                </if>
                <if test="reqVO.status != null and reqVO.status != ''">
                    <choose>
                        <when test="reqVO.status == 1">
                            and a.publish = 2
                        </when>
                        <when test="reqVO.status == 2">
                            and a.publish = 1
                        </when>
                        <when test="reqVO.status == 3">
                            and a.publish = 1 and a.registration_start_time &lt;= now() and a.registration_end_time &gt; now()
                        </when>
                        <when test="reqVO.status == 4">
                            and a.publish = 1 and a.registration_end_time &lt;= now() and a.class_open_time &gt; now()
                        </when>
                        <when test="reqVO.status == 5">
                            and a.publish = 1 and a.class_open_time &lt;= now() and  now() &lt; DATE_ADD(a.completion_time, INTERVAL '1 DAY')
                        </when>
                        <when test="reqVO.status == 6">
                            and a.publish = 1 and a.completion_time &lt; now()
                        </when>
                        <otherwise>

                        </otherwise>
                    </choose>
                </if>
            </otherwise>
        </choose>
        group by
        a.id
        <if test="reqVO.orderStatus != null and reqVO.orderStatus != ''">
            <choose>
                <when test="reqVO.orderStatus == 1">
                    order by a.class_name_code asc
                </when>
                <when test="reqVO.orderStatus == 2">
                    order by a.class_name_code desc
                </when>
                <when test="reqVO.orderStatus == 3">
                    order by a.class_name asc
                </when>
                <when test="reqVO.orderStatus == 4">
                    order by a.class_name desc
                </when>
                <when test="reqVO.orderStatus == 5">
                    order by a.class_open_time asc
                </when>
                <when test="reqVO.orderStatus == 6">
                    order by a.class_open_time desc
                </when>
                <when test="reqVO.orderStatus == 7">
                    order by a.create_time desc
                </when>
                <otherwise>
                    order by a.create_time desc
                </otherwise>
            </choose>
        </if>
    </select>
    <select id="getRegistrationInfoPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.trainee.vo.RegistrationInfoRespVO">
        <include refid="unitInfo"/>
    </select>
<!--    <select id="getRegistrationInfo2" resultType="Map">-->
<!--        select-->
<!--        a.class_name_code classNameCode,-->
<!--        a.class_name className,-->
<!--        a.class_open_time classOpenTime,-->
<!--        a.completion_time completionTime,-->
<!--        a.campus ,-->
<!--        a.people_number peopleNumber,-->
<!--        count(b.id) actualPeopleNumber,-->
<!--        count(case when b.deleted = 1 then 1 end) as reportNumber-->
<!--        from-->
<!--        edu_class_management a-->
<!--        left join edu_trainee b on-->
<!--        a.id = b.class_id-->
<!--        where-->
<!--        a.deleted = 0-->
<!--        and b.deleted = 0-->
<!--        <choose>-->
<!--            <when test="reqVO.idList != null and reqVO.idList.size() > 0">-->
<!--                and a.id in-->
<!--                <foreach collection="reqVO.idList" item="id" separator="," open="(" close=")">-->
<!--                    #{id}-->
<!--                </foreach>-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                <if test="reqVO.classId != null and reqVO.classId != ''">-->
<!--                    and a.id = #{reqVO.classId}-->
<!--                </if>-->
<!--                <if test="reqVO.year != null and reqVO.year != ''">-->
<!--                    and a.year = #{reqVO.year}-->
<!--                </if>-->
<!--                <if test="reqVO.semester != null and reqVO.semester != ''">-->
<!--                    and a.semester = #{reqVO.semester}-->
<!--                </if>-->
<!--                <if test="reqVO.status != null and reqVO.status != ''">-->
<!--                    <choose>-->
<!--                        <when test="reqVO.status == '01'">-->
<!--                            and a.publish = 2-->
<!--                        </when>-->
<!--                        <when test="reqVO.status == '02'">-->
<!--                            and a.publish = 1-->
<!--                        </when>-->
<!--                        <when test="reqVO.status == '03'">-->
<!--                            and a.publish = 1 and a.registration_start_time &lt;= now() and a.registration_end_time &gt;= now()-->
<!--                        </when>-->
<!--                        <when test="reqVO.status == '04'">-->
<!--                            and a.publish = 1 and a.registration_end_time &lt; now()-->
<!--                        </when>-->
<!--                        <when test="reqVO.status == '05'">-->
<!--                            and a.publish = 1 and a.class_open_time &lt;= now() and a.completion_time &gt;= now()-->
<!--                        </when>-->
<!--                        <when test="reqVO.status == '06'">-->
<!--                            and a.publish = 1 and a.completion_time &lt; now()-->
<!--                        </when>-->
<!--                        <otherwise>-->

<!--                        </otherwise>-->
<!--                    </choose>-->
<!--                </if>-->
<!--            </otherwise>-->
<!--        </choose>-->
<!--        group by-->
<!--        a.id-->
<!--        order by a.create_time desc-->
<!--    </select>-->
    <select id="getPageByUnitId"
            resultType="com.unicom.swdx.module.edu.controller.admin.trainee.vo.UnitRegistrationPageRespVO">
        select
            e.id,
            e.name,
            e.sex,
            e.card_no,
            e.phone,
            e.unit_name ,
            e.educational_level,
            e."position",
            e.job_level,
            e.political_identity,
            e.status
        from
            edu_trainee e
        where
            e.deleted = 0
        <if test="reqVO.unitId != null and reqVO.unitId != ''">
            and e.unit_id = #{reqVO.unitId}
        </if>
        <if test="reqVO.status != null and reqVO.status != ''">
            <choose>
                <when test="reqVO.status == '1'.toString()">
                    and e.status = 1
                </when>
                <when test="reqVO.status == '2'.toString()">
                    and e.status > 1
                </when>
                <when test="reqVO.status == '3'.toString()">
                    and e.status = 3
                </when>
                <otherwise>

                </otherwise>
            </choose>
        </if>
        <if test="reqVO.reportStatus != null and reqVO.reportStatus != ''">
            <choose>
                <when test="reqVO.reportStatus == '1'.toString() ">
                    and e.status = 1
                </when>
                <when test="reqVO.reportStatus == '2'.toString()">
                    and e.status > 1
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </select>
    <select id="pageTraineeInfo"
            resultMap="getAllTrainMap">
        select
            e.id,
            e.name,
            e.sex,
            e.card_no,
            e.phone,
            e.create_time,
            e.unit_id ,
            e.unit_name,
            e.educational_level,
            e."position",
            e.job_level,
            e.political_identity,
            e.status,
            e.class_id,
            row_number() over() as index,
            e.update_time,
            e.dropout_reason,
            e.height,
            e.weight,
            e.clothing_size,
            a.class_attribute,
            a.class_name,
            CASE
            WHEN a.completion_time &lt; NOW() and a.publish = 1 THEN 6
            WHEN  a.publish = 1 and a.class_open_time &lt;= NOW() AND NOW() &lt; DATE_ADD(a.completion_time, INTERVAL '1 DAY') THEN 5
            WHEN a.publish = 1 and a.registration_end_time &lt;= NOW() AND a.class_open_time > NOW() THEN 4
            WHEN a.publish = 1 and a.registration_start_time &lt;= NOW() AND a.registration_end_time > NOW() THEN 3
            WHEN a.publish = 1 THEN 2
            WHEN a.publish = 2 THEN 1
            ELSE 7
            END AS classStatus,
            CASE
            WHEN e.status = 797 THEN e.create_time
            WHEN e.status = 798 THEN e.report_time
            WHEN e.status = 799 THEN e.graduate_date
            WHEN e.status = 800 THEN e.dropout_date
            ELSE NULL
            END AS statusUpdateTime
        from
            edu_trainee e
            left join edu_class_management a on e.class_id = a.id
        where
            e.deleted = 0
            <if test="reqVO.classId != null and reqVO.classId != ''">
                and e.class_id = #{reqVO.classId}
            </if>
            <if test="reqVO.name != null and reqVO.name != ''">
                and e.name like concat('%',#{reqVO.name},'%')
            </if>
            <if test="reqVO.classAttribute != null and reqVO.classAttribute != ''">
                and a.class_attribute like concat('%',#{reqVO.classAttribute},'%')
            </if>
            <if test="reqVO.teacherId != null">
            AND (
                a."class_teacher_lead" = #{reqVO.teacherId}
            OR a."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR a."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR))
            OR a."coach_teacher" LIKE CONCAT(CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR a."coach_teacher" = CAST(#{reqVO.teacherId} AS VARCHAR)
            )
            </if>
            <if test="reqVO.unitName != null and reqVO.unitName != ''">
                and e.unit_name like concat('%',#{reqVO.unitName},'%')
            </if>
            <if test="reqVO.unitId != null and reqVO.unitId != ''">
                <choose>
                <when test="reqVO.unitId == -1">
                    and e.unit_id is null
                </when>
                <otherwise>
                    and e.unit_id = #{reqVO.unitId}
                </otherwise>
                </choose>
            </if>
            <if test="reqVO.phone != null and reqVO.phone != ''">
                and e.phone like concat('%',#{reqVO.phone},'%')
            </if>
            <if test="reqVO.className != null and reqVO.className != ''">
                and a.class_name like concat('%',#{reqVO.className},'%')
            </if>
            <if test="reqVO.status != null and reqVO.status != ''">
                <choose>
                    <when test="reqVO.status == 797">
                        and e.status in (797)
                    </when>
                    <when test="reqVO.status == 798">
                        and e.status in (798)
                    </when>
                    <when test="reqVO.status == 799">
                        and e.status in (799)
                    </when>
                    <when test="reqVO.status == 800">
                        and e.status = 800
                    </when>
                    <otherwise>

                    </otherwise>
                </choose>
            </if>
            <if test="reqVO.reportStatus != null and reqVO.reportStatus != ''">
                <choose>
                    <when test="reqVO.reportStatus == 797 or reqVO.reportStatus == 1">
                        and e.status = 797
                    </when>
                    <when test="reqVO.reportStatus == 798 or reqVO.reportStatus == 2">
                        and (e.status = 798 or e.status = 799)
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
        <if test = 'reqVO.orderStatus == 1'>
            order by e.create_time asc
        </if>
        <if test = 'reqVO.orderStatus == 2 or reqVO.orderStatus == 5'>
            order by e.create_time desc
        </if>
        <if test = 'reqVO.orderStatus == 3'>
            order by e.unit_name asc
        </if>
        <if test = 'reqVO.orderStatus == 4'>
            order by e.unit_name desc
        </if>
        <if test = 'reqVO.orderStatus == 7'>
            order by statusUpdateTime desc
        </if>
    </select>
    <select id="reportPage" resultType="com.unicom.swdx.module.edu.controller.admin.trainee.vo.ReportPageRespVO">
        select
            a.id,
            a.class_name,
            a.class_open_time openingTime,
            a.class_attribute,
            count(case when b.status =797  and b.deleted =0 then 1 end) as notReportNum ,
            count(case when  (b.status = 798 or b.status = 799 ) and b.deleted =0 then 1 end) as reportNum,
            row_number() over() as index
        from
            edu_class_management a
                left join edu_trainee b on
                a.id = b.class_id
        where
            a.deleted = 0
        <if test="reqVO.className != null and reqVO.className != ''">
            and a.class_name like concat('%',#{reqVO.className},'%')
        </if>
        <if test="reqVO.classAttribute != null and reqVO.classAttribute != ''">
            and a.class_attribute like concat('%',#{reqVO.classAttribute},'%')
        </if>
        <if test="reqVO.teacherId != null">
            AND (
            a."class_teacher_lead" = #{reqVO.teacherId}
            OR a."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR a."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR))
            OR a."coach_teacher" LIKE CONCAT(CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR a."coach_teacher" = CAST(#{reqVO.teacherId} AS VARCHAR)
            )
        </if>
        group by a.id
        <if test="reqVO.orderStatus == 1">
            order by a.id asc
        </if>
        <if test="reqVO.orderStatus == 2 or reqVO.orderStatus == 7">
            order by a.id desc
        </if>
        <if test="reqVO.orderStatus == 3">
            order by a.class_name asc
        </if>
        <if test="reqVO.orderStatus == 4">
            order by a.class_name desc
        </if>
        <if test="reqVO.orderStatus == 5">
            order by a.class_open_time asc
        </if>
        <if test="reqVO.orderStatus == 6">
            order by a.class_open_time desc
        </if>
    </select>
    <select id="reportInfoPageByStatus" resultMap="reportMap" >
        select
            a.id,
            a.name,
            a.card_no,
            a.phone,
            a.unit_name,
            a."position",
            a.report_time reportDate,
            b.class_attribute,
            b.class_name
        from
            edu_trainee a
                left join edu_class_management b on
                a.class_id = b.id
        where
            a.deleted = 0
        <if test="reqVO.reportStatus == 1">
            and a.status = 797
        </if>
        <if test="reqVO.reportStatus == 2">
            and (a.status = 798 or a.status = 799)
        </if>
        <if test="reqVO.className != null and reqVO.className != ''">
            and b.class_name like concat('%',#{reqVO.className},'%')
        </if>
        <if test="reqVO.name != null and reqVO.name != ''">
            and a.name like concat('%',#{reqVO.name},'%')
        </if>
        <if test="reqVO.campus != null and reqVO.campus != ''">
            AND (
            ecm.campus_ids like CONCAT('%,',CAST(#{reqVO.campus} AS VARCHAR), ',%')
            or
            ecm.campus_ids = CAST(#{reqVO.campus} AS VARCHAR)
            or
            ecm.campus_ids like CONCAT(CAST(#{reqVO.campus} AS VARCHAR), ',%')
            or
            ecm.campus_ids like CONCAT('%,',CAST(#{reqVO.campus} AS VARCHAR))
            )
        </if>
        <if test="reqVO.phone != null and reqVO.phone != ''">
            and a.phone like concat('%',#{reqVO.phone},'%')
        </if>
        <if test="reqVO.reportDateBeg != null and reqVO.reportDateBeg != ''">
            and a.report_time &gt;= #{reqVO.reportDateBeg}
        </if>
        <if test="reqVO.classAttribute != null and reqVO.classAttribute != ''">
            and b.class_attribute like concat('%',#{reqVO.classAttribute},'%')
        </if>
        <if test="reqVO.teacherId != null">
            AND (
            b."class_teacher_lead" = #{reqVO.teacherId}
            OR b."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR b."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR))
            OR b."coach_teacher" LIKE CONCAT(CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR b."coach_teacher" = CAST(#{reqVO.teacherId} AS VARCHAR)
            )
        </if>
        <if test="reqVO.reportDateBeg != null and reqVO.reportDateBeg != ''">
            and a.report_time &lt;= #{reqVO.reportDateEnd}
        </if>
        <if test="reqVO.orderStatus == 1">
            order by a.create_time asc
        </if>
        <if test="reqVO.orderStatus == 2 or reqVO.orderStatus == 7">
            order by a.create_time desc
        </if>
        <if test="reqVO.orderStatus == 3">
            order by b.class_name asc
        </if>
        <if test="reqVO.orderStatus == 4">
            order by b.class_name desc
        </if>

    </select>
    <select id="getRegistrationInfoList"
            resultType="com.unicom.swdx.module.edu.controller.admin.trainee.vo.RegistrationInfoExcelVO">
        <include refid="unitInfo"/>
    </select>
    <select id="getTraineeInfoList"
            resultMap="getAllTrainMap">
        select
        e.id,
        e.name,
        e.sex,
        e.card_no,
        e.phone,
        e.create_time,
        e.unit_name ,
        e.educational_level,
        e."position",
        e.job_level,
        e.political_identity,
        e.status,
        e.height,
        e.weight,
        e.clothing_size,
        e.record_address_name,
        e.record_detail_address,
        e.class_id,
        ecm.class_attribute as classAttribute,
        ecm.class_name,
        CASE
        WHEN e.status = 797 THEN e.create_time
        WHEN e.status = 798 THEN e.report_time
        WHEN e.status = 799 THEN e.graduate_date
        WHEN e.status = 800 THEN e.dropout_date
        ELSE NULL
        END AS statusUpdateTime,
        row_number() over() as index
        from
        edu_trainee e left join edu_class_management ecm on e.class_id = ecm.id
        where
        e.deleted = 0
        <if test="reqVO.classId != null and reqVO.classId != ''">
            and e.class_id = #{reqVO.classId}
        </if>
        <if test = "reqVO.classIds != null and reqVO.classIds.size()>0">
            and e.class_id in
            <foreach collection="reqVO.classIds" item = "id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="reqVO.name != null and reqVO.name != ''">
            and e.name like concat('%',#{reqVO.name},'%')
        </if>
        <if test="reqVO.classAttribute != null and reqVO.classAttribute != ''">
            and ecm.class_attribute like concat('%',#{reqVO.classAttribute},'%')
        </if>
        <if test="reqVO.unitName != null and reqVO.unitName != ''">
            and e.unit_name like concat('%',#{reqVO.unitName},'%')
        </if>

        <if test="reqVO.unitId != null and reqVO.unitId != ''">
            <choose>
                <when test="reqVO.unitId == -1">
                    and e.unit_id is null
                </when>
                <otherwise>
                    and e.unit_id = #{reqVO.unitId}
                </otherwise>
            </choose>
        </if>
        <if test="reqVO.phone != null and reqVO.phone != ''">
            and e.phone like concat('%',#{reqVO.phone},'%')
        </if>
        <if test="reqVO.className != null and reqVO.className != ''">
            and ecm.class_name like concat('%',#{reqVO.className},'%')
        </if>
        <if test="reqVO.status != null and reqVO.status != ''">
            <choose>
                <when test="reqVO.status == 797">
                    and e.status in (797)
                </when>
                <when test="reqVO.status == 798">
                    and e.status in (798)
                </when>
                <when test="reqVO.status == 799">
                    and e.status in (799)
                </when>
                <when test="reqVO.status == 800">
                    and e.status = 800
                </when>
                <otherwise>

                </otherwise>
            </choose>
        </if>
        <if test="reqVO.teacherId != null">
            AND (
            ecm."class_teacher_lead" = #{reqVO.teacherId}
            OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR))
            OR ecm."coach_teacher" LIKE CONCAT(CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR ecm."coach_teacher" = CAST(#{reqVO.teacherId} AS VARCHAR)
            )
        </if>
        <if test="reqVO.reportStatus != null and reqVO.reportStatus != ''">
            <choose>
                <when test="reqVO.reportStatus == 797 or reqVO.reportStatus == 1">
                    and e.status = 797
                </when>
                <when test="reqVO.reportStatus == 798 or reqVO.reportStatus == 2">
                    and e.status = 798
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test = 'reqVO.orderStatus == 1'>
            order by e.create_time asc
        </if>
        <if test = 'reqVO.orderStatus == 2 or reqVO.orderStatus == 5'>
            order by e.create_time desc
        </if>
        <if test = 'reqVO.orderStatus == 3'>
            order by e.unit_name asc
        </if>
        <if test = 'reqVO.orderStatus == 4'>
            order by e.unit_name desc
        </if>
        <if test = 'reqVO.orderStatus == 7'>
            order by statusUpdateTime desc
        </if>
    </select>
    <select id="getClassIdSameTime" resultType="java.lang.Long">
        select
        id
        from
        edu_class_management
        where
        deleted = 0
        and completion_time >current_timestamp
    </select>
    <select id="getClassStatus" resultType="java.lang.Integer">
        SELECT
        CASE
        WHEN a.completion_time &lt; NOW() and a.publish = 1 THEN 6
        WHEN  a.publish = 1 and a.class_open_time &lt;= NOW() AND NOW() &lt; DATE_ADD(a.completion_time, INTERVAL '1 DAY') THEN 5
        WHEN a.publish = 1 and a.registration_end_time &lt;= NOW() AND a.class_open_time > NOW() THEN 4
        WHEN a.publish = 1 and a.registration_start_time &lt;= NOW() AND a.registration_end_time > NOW() THEN 3
        WHEN a.publish = 1 THEN 2
        WHEN a.publish = 2 THEN 1
        ELSE 7
        END AS classStatus
        from edu_class_management a
        where a.id = #{classId} and a.deleted = 0
    </select>
    <select id="checkIsSigning" resultType="java.lang.Integer">
        select
            count(1)
        from
            edu_class_management
        where
            registration_start_time &lt;= now()
            and registration_end_time &gt;= now()
            and deleted = 0
            and publish = 1
            and id = #{classId}
    </select>
    <select id="getreportList"
            resultType="com.unicom.swdx.module.edu.controller.admin.trainee.vo.ReportPageRespVO">
        select
        a.id,
        a.class_name,
        a.class_open_time openingTime,
        a.class_attribute,
        count(case when b.status =797 and b.deleted =0 then 1 end) as notReportNum ,
        count(case when b.status =798 and b.deleted =0 then 1 end) as reportNum,
        row_number() over() as index
        from
        edu_class_management a
        left join edu_trainee b on
        a.id = b.class_id
        where
        a.deleted = 0
        <if test="reqVO.classId != null and reqVO.classId != ''">
            and a.id = #{reqVO.classId}
        </if>
        <if test="reqVO.className != null and reqVO.className != ''">
            and a.class_name like concat('%',#{reqVO.className},'%')
        </if>
        <if test="reqVO.classAttribute != null and reqVO.classAttribute != ''">
            and a.class_attribute like concat('%',#{reqVO.classAttribute},'%')
        </if>
        <if test="reqVO.teacherId != null">
            AND (
            a."class_teacher_lead" = #{reqVO.teacherId}
            OR a."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR a."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR))
            OR a."coach_teacher" LIKE CONCAT(CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR a."coach_teacher" = CAST(#{reqVO.teacherId} AS VARCHAR)
            )
        </if>
        group by a.id
        <if test="reqVO.orderStatus == 1">
            order by a.id asc
        </if>
        <if test="reqVO.orderStatus == 2 or reqVO.orderStatus == 7">
            order by a.id desc
        </if>
        <if test="reqVO.orderStatus == 3">
            order by a.class_name asc
        </if>
        <if test="reqVO.orderStatus == 4">
            order by a.class_name desc
        </if>
        <if test="reqVO.orderStatus == 5">
            order by a.class_open_time asc
        </if>
        <if test="reqVO.orderStatus == 6">
            order by a.class_open_time desc
        </if>
    </select>
    <select id="getReportInfoListByStatus" resultMap="reportExcelMap">
        select
        a.id,
        a.name,
        a.card_no,
        a.phone,
        a.unit_name,
        a."position",
        a.report_time reportDate,
        b.class_name,
        b.class_attribute,
        row_number() over() as index
        from
        edu_trainee a
        left join edu_class_management b on
        a.class_id = b.id
        where
        a.deleted = 0
        <if test="reqVO.reportStatus == 1">
            and a.status = 797
        </if>
        <if test="reqVO.reportStatus == 2">
            and (a.status = 798 or a.status = 799)
        </if>
        <if test="reqVO.className != null and reqVO.className != ''">
            and b.class_name like concat('%',#{reqVO.className},'%')
        </if>
        <if test="reqVO.classAttribute != null and reqVO.classAttribute != ''">
            and b.class_attribute like concat('%',#{reqVO.classAttribute},'%')
        </if>
        <if test="reqVO.teacherId != null">
            AND (
            b."class_teacher_lead" = #{reqVO.teacherId}
            OR b."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR b."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR))
            OR b."coach_teacher" LIKE CONCAT(CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR b."coach_teacher" = CAST(#{reqVO.teacherId} AS VARCHAR)
            )
        </if>
        <if test="reqVO.name != null and reqVO.name != ''">
            and a.name like concat('%',#{reqVO.name},'%')
        </if>
        <if test="reqVO.campus != null and reqVO.campus != ''">
            and b.campus like concat('%',#{reqVO.campus},'%')
        </if>
        <if test="reqVO.phone != null and reqVO.phone != ''">
            and a.phone like concat('%',#{reqVO.phone},'%')
        </if>
        <if test="reqVO.reportDateBeg != null and reqVO.reportDateBeg != ''">
            and a.report_time &gt;= #{reqVO.reportDateBeg}
        </if>
        <if test="reqVO.reportDateBeg != null and reqVO.reportDateBeg != ''">
            and a.report_time &lt;= #{reqVO.reportDateEnd}
        </if>
        <if test="reqVO.orderStatus == 1">
            order by a.create_time asc
        </if>
        <if test="reqVO.orderStatus == 2 or reqVO.orderStatus == 7">
            order by a.create_time desc
        </if>
        <if test="reqVO.orderStatus == 3">
            order by b.class_name asc
        </if>
        <if test="reqVO.orderStatus == 4">
            order by b.class_name desc
        </if>
    </select>
    <select id="getAllTraineeInfo" resultMap="getAllTrainMap">
        select
        ROW_NUMBER() OVER () AS index,
        e.id,
        e.name,
        e.sex,
        e.card_no,
        e.phone,
        e.create_time,
        e.unit_id ,
        e.unit_name,
        e.educational_level,
        e."position",
        e.job_level,
        e.political_identity,
        e.status,
        e.class_id,
        row_number() over() as index
        from
        edu_trainee e
        where
        e.deleted = 0
        <if test="reqVO.classId != null and reqVO.classId != ''">
            and e.class_id = #{reqVO.classId}
        </if>
        <if test="reqVO.name != null and reqVO.name != ''">
            and e.name like concat('%',#{reqVO.name},'%')
        </if>
    </select>
<!--    <select id="getDictData" resultType="java.util.Map">-->
<!--        select id,label-->
<!--        from system_dict_data-->
<!--        where dict_type = #{dictType}-->
<!--    </select>-->
    <select id="getDictTypeByDictLabel"
            resultType="com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoImportDictLableExcelVO">
        SELECT
        sdd."label"
        FROM
        system_dict_data sdd
        WHERE
        sdd ."deleted" = '0'

        <if test="type == 1">
            and "dict_type" like 'stu-educational'
        </if>
        <if test="type == 3">
            and "dict_type" like 'nation'
        </if>
        <if test="type == 2">
            and "dict_type" like 'political_identity'
        </if>
        <if test="type == 4">
            and "dict_type" like 'stu_rank'
        </if>

        ORDER BY
        sdd."value"
    </select>
    <select id="getUnit"
            resultType="com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoImportDictLableExcelVO">
        SELECT distinct unit_name as label
        FROM edu_sign_up_unit
        WHERE deleted = 0
          <if test="classId != null">
              AND class_id = #{classId}
          </if>
    </select>
    <select id="getNation" resultType="java.util.Map">
        SELECT
            sdd."label",sdd.id
        FROM
            system_dict_data sdd
        WHERE
            sdd ."deleted" = '0'  and "dict_type" like 'nation'
    </select>
    <select id="getUnitByClassId" resultType="java.util.Map">
        select unit_name,id from edu_sign_up_unit where class_id = #{classId} and deleted = 0
    </select>
    <select id="getUnitCountByClassId" resultType="java.util.Map">
        select
            unit_name ,
            capacity
        from
            edu_sign_up_unit
        where
            deleted = 0
          and is_restrict = 0
          and class_id = #{classId}
    </select>
    <select id="getDictDateMap" resultType="java.util.Map">
        SELECT
            sdd."label",sdd.id
        FROM
            system_dict_data sdd
        WHERE
            sdd ."deleted" = '0'  and "dict_type" like #{type}
    </select>
    <select id="getDictDateMapById" resultType="java.util.Map">
        SELECT
            sdd.id,sdd."label"
        FROM
            system_dict_data sdd
        WHERE
            sdd ."deleted" = '0'  and "dict_type" like #{type}
    </select>

    <select id="getDictDateMapById1" resultType="java.util.Map">
        SELECT
            sdd.value as id,sdd."label"
        FROM
            system_dict_data sdd
        WHERE
            sdd ."deleted" = '0'  and "dict_type" like #{type}
    </select>



    <select id="getDictDateMapByIdandTenant" resultType="java.util.Map">
        SELECT
        sdd.id,sdd."label"
        FROM
        system_dict_data sdd
        WHERE
        sdd ."deleted" = '0'  and "dict_type" like #{type} and tenant_id = #{tenantId}
    </select>

    <select id="getDictDateMapByIdandTenant1" resultType="java.util.Map">
        SELECT
        sdd.value as id,sdd."label"
        FROM
        system_dict_data sdd
        WHERE
        sdd ."deleted" = '0'  and "dict_type" like #{type}  and tenant_id = #{tenantId}
    </select>



    <select id="selectTraineeGroupListByClassIdAndStatus"
            resultType="com.unicom.swdx.module.edu.controller.admin.traineegroup.AppTraineeGroupRespVO">
        select
            et.id id,
            etg.group_name groupName,
            et."name" "name",
            et.sex,
            et.phone,
            et."position",
            et.class_committee_id,
            ecc.class_committee_name
        from edu_trainee et
                 left join edu_class_committee ecc on ecc.id  = et.class_committee_id and ecc.deleted = 0
                 left join edu_trainee_group etg on et.group_id = etg.id and etg.deleted = 0
        where et.deleted =0
        <if test="classId != null">
            and et.class_id = #{classId}
        </if>
        <if test="traineeName != null and traineeName != ''">
            and et."name" like concat('%',#{traineeName},'%')
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and et.status in
            <foreach collection="statusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        order by etg.sort, et.group_sort
    </select>
    <select id="getTraineeId" resultType="java.lang.Long">
        select id from edu_trainee where class_id = #{classId} and user_id = #{userId} and deleted = 0
    </select>
    <select id="getTraineeInfoByTraineeStatus"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO">
        select
            et.*
        from
            edu_trainee et
                left join edu_class_management ecm on
                et.class_id = ecm.id
        where
            et.status = #{status}
          and et.deleted = 0
          and ecm.publish = 1
          and ecm.completion_time > current_timestamp
    </select>
    <select id="getCurrentGraduateTrainee"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO">
        select
        et.*
        from
        edu_trainee et
        left join edu_class_management ecm on
        et.class_id = ecm.id
        where
        et.deleted = 0
        and ecm.publish = 1
        and ecm.completion_time &lt;= current_timestamp
        and et.status in
        <foreach collection="statusList" index="index" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
        and year(ecm.completion_time) = year(current_date)
    </select>
    <select id="getUnitClassificationByClassId" resultType="java.util.Map">
        select unit_name,unit_classification as id from edu_sign_up_unit where class_id = #{classId} and deleted = 0
    </select>
    <select id="selectUserSystemIdList" resultType="java.lang.Long">
        SELECT su.system_id from system_users su LEFT JOIN edu_trainee et on su.id = et.user_id
        where et.id in
        <foreach collection="traineeIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getUserBySystemId" resultType="java.lang.Long">
        select id from system_users where system_id = #{id} and deleted = 0 limit 1
    </select>
    <select id="getSystemIdByUserId" resultType="java.lang.Long">
        select system_id from system_users where id = #{id} and deleted = 0 limit 1
    </select>
    <select id="getTraineeInfoPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.trainee.vo.TraineeInfoPageRespVO">
        <include refid="traineeInfo"/>
    </select>
    <select id="getTraineeReportInfoList"
            resultType="com.unicom.swdx.module.edu.controller.admin.trainee.vo.ExportTraineeInfoExcelVO">
        <include refid="traineeInfo"/>
    </select>

    <select id="selectBatchByPhoneAndTenantId" parameterType="java.util.List"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO">
        select *  FROM edu_trainee
        WHERE (phone, tenant_id) IN
        <foreach collection="list" item="pair" open="(" separator="," close=")">
            (#{pair.key}, #{pair.value})
        </foreach>
    </select>
    <select id="getTraineeGroupPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupRespVO">
        select
        et.id,
        et."name",
        et.group_sort,
        et.group_id,
        etg.group_name,
        et.sex,
        et.birthday,
        et.educational_level,
        et."position",
        et.political_identity,
        ecc.class_committee_name,
        et.class_committee_id
        from
        edu_trainee et
        left join edu_trainee_group etg on et.group_id=etg.id
        left join edu_class_committee ecc on et.class_committee_id = ecc.id
        where
        et.deleted = 0
        and et.class_id =#{reqVO.classId}
        <if test="reqVO.name != null and reqVO.name != ''">
            and (et."name" like concat('%',#{reqVO.name},'%'))
        </if>
        <if test="reqVO.groupId != null and reqVO.groupId != 0">
            and et.group_id = #{reqVO.groupId}
        </if>
        <if test="reqVO.groupId != null and reqVO.groupId == 0">
            and (et.group_id is null or et.group_id = 0)
        </if>
        ORDER BY
        etg.sort,et.group_sort
    </select>

    <select id="updateByIdTenantIgonre" resultType="com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO">
        update edu_trainee et set  et.status=#{param.status},et.report_time=#{param.reportTime}   where  id =#{param.id}
    </select>

    <select id="selectByIdTenantIgnore" resultType="com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO">
        select * from  edu_trainee et where  id =#{id}  and deleted=0
    </select>
    <select id="selectByUserId" resultType="com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO"
            parameterType="java.lang.Long">
        select *
        from edu_trainee et
                 join system_users su on et.id = su.employee_id
            and et.user_id = su.id and su.deleted = 0
        where et.user_id = #{userId} and et.deleted = 0
        order by et.create_time desc
        limit 1
    </select>
    <select id="selectTraineeByUserId"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO">
        select *
        from edu_trainee et
                 join system_users su on et.id = su.employee_id
            and et.user_id = su.id and su.deleted = 0
        where et.user_id = #{userId} and et.deleted = 0
        <if test="studentId != null">
            and et.id = #{studentId}
        </if>
        order by et.create_time desc
        limit 1
    </select>

    <select id="getTraineeByClassIds"
            resultMap="getAllTrainMap">
    select
        e.id,
        e.name,
        e.sex,
        e.card_no,
        e.phone,
        e.create_time,
        e.unit_id ,
        e.unit_name,
        e.educational_level,
        e."position",
        e.job_level,
        e.political_identity,
        e.status,
        e.class_id,
        row_number() over() as index,
        e.update_time,
        e.dropout_reason,
        e.height,
        e.weight,
        e.clothing_size,
        a.class_name,
        CASE
            WHEN a.completion_time &lt; NOW() and a.publish = 1 THEN 6
            WHEN  a.publish = 1 and a.class_open_time &lt;= NOW() AND NOW() &lt; DATE_ADD(a.completion_time, INTERVAL '1 DAY') THEN 5
            WHEN a.publish = 1 and a.registration_end_time &lt;= NOW() AND a.class_open_time > NOW() THEN 4
            WHEN a.publish = 1 and a.registration_start_time &lt;= NOW() AND a.registration_end_time > NOW() THEN 3
            WHEN a.publish = 1 THEN 2
            WHEN a.publish = 2 THEN 1
        ELSE 7
        END AS classStatus,
        CASE
        WHEN e.status = 797 THEN e.create_time
        WHEN e.status = 798 THEN e.report_time
        WHEN e.status = 799 THEN e.graduate_date
        WHEN e.status = 800 THEN e.dropout_date
        ELSE NULL
        END AS statusUpdateTime
        from
        edu_trainee e
        left join edu_class_management a on e.class_id = a.id
        where
        e.deleted = 0

        and e.class_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

        and e.status != 800

        <if test="classId != null and classId!='' ">
            and e.class_id=#{classId}
        </if>

        <if test="name != null and name!='' ">
            and (e.name LIKE CONCAT('%',#{name},'%')
            or e.phone LIKE CONCAT('%',#{name},'%'))
        </if>

        order by e.create_time asc

    </select>
</mapper>
