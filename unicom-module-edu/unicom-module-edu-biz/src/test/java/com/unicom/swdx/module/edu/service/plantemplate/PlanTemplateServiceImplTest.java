package com.unicom.swdx.module.edu.service.plantemplate;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.unicom.swdx.framework.test.core.ut.BaseDbUnitTest;

import com.unicom.swdx.module.edu.controller.admin.plantemplate.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plantemplate.PlanTemplateDO;
import com.unicom.swdx.module.edu.dal.mysql.plantemplate.PlanTemplateMapper;
import com.unicom.swdx.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.framework.test.core.util.AssertUtils.*;
import static com.unicom.swdx.framework.test.core.util.RandomUtils.*;
import static com.unicom.swdx.framework.common.util.object.ObjectUtils.*;
import static com.unicom.swdx.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link PlanTemplateServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(PlanTemplateServiceImpl.class)
public class PlanTemplateServiceImplTest extends BaseDbUnitTest {

    @Resource
    private PlanTemplateServiceImpl planTemplateService;

    @Resource
    private PlanTemplateMapper planTemplateMapper;

    @Test
    public void testCreatePlanTemplate_success() {
        // 准备参数
        PlanTemplateCreateReqVO reqVO = randomPojo(PlanTemplateCreateReqVO.class);

        // 调用
        Long planTemplateId = planTemplateService.createPlanTemplate(reqVO);
        // 断言
        assertNotNull(planTemplateId);
        // 校验记录的属性是否正确
        PlanTemplateDO planTemplate = planTemplateMapper.selectById(planTemplateId);
        assertPojoEquals(reqVO, planTemplate);
    }

    @Test
    public void testUpdatePlanTemplate_success() {
        // mock 数据
        PlanTemplateDO dbPlanTemplate = randomPojo(PlanTemplateDO.class);
        planTemplateMapper.insert(dbPlanTemplate);// @Sql: 先插入出一条存在的数据
        // 准备参数
        PlanTemplateUpdateReqVO reqVO = randomPojo(PlanTemplateUpdateReqVO.class, o -> {
            o.setId(dbPlanTemplate.getId()); // 设置更新的 ID
        });

        // 调用
        planTemplateService.updatePlanTemplate(reqVO);
        // 校验是否更新正确
        PlanTemplateDO planTemplate = planTemplateMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, planTemplate);
    }

    @Test
    public void testUpdatePlanTemplate_notExists() {
        // 准备参数
        PlanTemplateUpdateReqVO reqVO = randomPojo(PlanTemplateUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> planTemplateService.updatePlanTemplate(reqVO), PLAN_TEMPLATE_NOT_EXISTS);
    }

    @Test
    public void testDeletePlanTemplate_success() {
        // mock 数据
        PlanTemplateDO dbPlanTemplate = randomPojo(PlanTemplateDO.class);
        planTemplateMapper.insert(dbPlanTemplate);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlanTemplate.getId();

        // 调用
        planTemplateService.deletePlanTemplate(id);
       // 校验数据不存在了
       assertNull(planTemplateMapper.selectById(id));
    }

    @Test
    public void testDeletePlanTemplate_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planTemplateService.deletePlanTemplate(id), PLAN_TEMPLATE_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanTemplatePage() {
       // mock 数据
       PlanTemplateDO dbPlanTemplate = randomPojo(PlanTemplateDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setCreateTime(null);
       });
       planTemplateMapper.insert(dbPlanTemplate);
       // 测试 name 不匹配
       planTemplateMapper.insert(cloneIgnoreId(dbPlanTemplate, o -> o.setName(null)));
       // 测试 createTime 不匹配
       planTemplateMapper.insert(cloneIgnoreId(dbPlanTemplate, o -> o.setCreateTime(null)));
       // 准备参数
       PlanTemplatePageReqVO reqVO = new PlanTemplatePageReqVO();
       reqVO.setName(null);
//       reqVO.setCreateTime((new LocalDateTime[]{}));

       // 调用
       PageResult<PlanTemplateDO> pageResult = planTemplateService.getPlanTemplatePage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlanTemplate, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanTemplateList() {
       // mock 数据
       PlanTemplateDO dbPlanTemplate = randomPojo(PlanTemplateDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setCreateTime(null);
       });
       planTemplateMapper.insert(dbPlanTemplate);
       // 测试 name 不匹配
       planTemplateMapper.insert(cloneIgnoreId(dbPlanTemplate, o -> o.setName(null)));
       // 测试 createTime 不匹配
       planTemplateMapper.insert(cloneIgnoreId(dbPlanTemplate, o -> o.setCreateTime(null)));
       // 准备参数
       PlanTemplateExportReqVO reqVO = new PlanTemplateExportReqVO();
       reqVO.setName(null);
//       reqVO.setCreateTime((new LocalDateTime[]{}));

       // 调用
       List<PlanTemplateDO> list = planTemplateService.getPlanTemplateList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlanTemplate, list.get(0));
    }

}
