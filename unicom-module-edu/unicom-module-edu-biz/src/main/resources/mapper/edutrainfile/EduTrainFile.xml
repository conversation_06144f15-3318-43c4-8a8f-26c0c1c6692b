<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.edutrainFile.EduTrainFileMapper">


    <select id="listForHome"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.edutrainfile.EduTrainFileDO">
        select
            id,
            title,
            file,
            user_id,
            user_name,
            publish_time,
            create_time
        from
            edu_train_file etf
        where etf.deleted = 0
        order by publish_time desc
        limit 5
    </select>
</mapper>
