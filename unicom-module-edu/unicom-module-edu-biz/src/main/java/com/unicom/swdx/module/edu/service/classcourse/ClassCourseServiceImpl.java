package com.unicom.swdx.module.edu.service.classcourse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.common.util.object.ObjectUtils;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.controller.admin.classcourse.dto.ClassCourseClassNameRespDTO;
import com.unicom.swdx.module.edu.controller.admin.classcourse.dto.ClassMergeCourseGroupRespDTO;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.*;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoRespVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementRespVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassRuleClockingInVO;
import com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesRespVO;
import com.unicom.swdx.module.edu.convert.classcourse.ClassCourseConvert;
import com.unicom.swdx.module.edu.convert.classmanagement.ClassManagementConvert;
import com.unicom.swdx.module.edu.convert.courses.CoursesConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classclockcalendar.ClassClockCalendarDO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.YearMothBean;
import com.unicom.swdx.module.edu.dal.dataobject.classcourseorder.ClassCourseOrderDO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourseteacher.ClassCourseTeacherDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.classroomlibrary.ClassroomLibraryDO;
import com.unicom.swdx.module.edu.dal.dataobject.clockininfo.ClockInInfoDO;
import com.unicom.swdx.module.edu.dal.dataobject.courses.CoursesDO;
import com.unicom.swdx.module.edu.dal.dataobject.plan.PlanDO;
import com.unicom.swdx.module.edu.dal.dataobject.ruletemplate.RuleTemplateDO;
import com.unicom.swdx.module.edu.dal.dataobject.schoolaccommodationattendance.SchoolAccommodationAttendanceDO;
import com.unicom.swdx.module.edu.dal.dataobject.systemsetting.SystemSettingDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classclockcalendar.ClassClockCalendarMapper;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.classcourseorder.ClassCourseOrderMapper;
import com.unicom.swdx.module.edu.dal.mysql.classcourseteacher.ClassCourseTeacherMapper;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.classroomlibrary.ClassroomLibraryMapper;
import com.unicom.swdx.module.edu.dal.mysql.clockininfo.ClockInInfoMapper;
import com.unicom.swdx.module.edu.dal.mysql.courses.CoursesMapper;
import com.unicom.swdx.module.edu.dal.mysql.courses.HolidayMapper;
import com.unicom.swdx.module.edu.dal.mysql.plan.PlanMapper;
import com.unicom.swdx.module.edu.dal.mysql.ruletemplate.RuleTemplateMapper;
import com.unicom.swdx.module.edu.dal.mysql.schoolaccommodationattendance.SchoolAccommodationAttendanceMapper;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper;
import com.unicom.swdx.module.edu.dal.mysql.traineeleave.TraineeLeaveMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.enums.classcourse.PeriodEnum;
import com.unicom.swdx.module.edu.enums.classcourse.StatusEnum;
import com.unicom.swdx.module.edu.enums.classmanagement.ClassManageDictTypeEnum;
import com.unicom.swdx.module.edu.enums.classmanagement.IsCheckElseEnum;
import com.unicom.swdx.module.edu.enums.classmanagement.IsCheckEnum;
import com.unicom.swdx.module.edu.enums.classmanagement.PublishEnum;
import com.unicom.swdx.module.edu.enums.clockininfo.ClockStatusEnum;
import com.unicom.swdx.module.edu.enums.clockininfo.MealPeriodEnum;
import com.unicom.swdx.module.edu.enums.clockininfo.TypeEnum;
import com.unicom.swdx.module.edu.enums.courses.CoursesDictEnum;
import com.unicom.swdx.module.edu.enums.courses.CoursesPeriodEnum;
import com.unicom.swdx.module.edu.enums.courses.CoursesTypeEnum;
import com.unicom.swdx.module.edu.enums.plan.PlanStatusEnum;
import com.unicom.swdx.module.edu.enums.trainee.TraineeStatusEnum;
import com.unicom.swdx.module.edu.enums.traineeleave.TraineeLeaveStatus;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementService;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementServiceImpl;
import com.unicom.swdx.module.edu.service.classroomlibrary.ClassroomLibraryService;
import com.unicom.swdx.module.edu.service.classroomlibrary.ClassroomLibraryServiceImpl;
import com.unicom.swdx.module.edu.service.clockininfo.ClockInInfoService;
import com.unicom.swdx.module.edu.service.clockininfo.ClockInInfoServiceImpl;
import com.unicom.swdx.module.edu.service.common.EduCommonService;
import com.unicom.swdx.module.edu.service.courses.CoursesService;
import com.unicom.swdx.module.edu.service.courses.CoursesServiceImpl;
import com.unicom.swdx.module.edu.service.evaluationdetail.EvaluationDetailService;
import com.unicom.swdx.module.edu.service.plan.PlanService;
import com.unicom.swdx.module.edu.service.plan.PlanServiceImpl;
import com.unicom.swdx.module.edu.service.rollcallrecord.RollcallRecordServiceImpl;
import com.unicom.swdx.module.edu.service.rollcallsignin.RollcallSignInServiceImpl;
import com.unicom.swdx.module.edu.service.ruletemplate.RuleTemplateServiceImpl;
import com.unicom.swdx.module.edu.service.schoolaccommodationattendance.SchoolAccommodationAttendanceServiceImpl;
import com.unicom.swdx.module.edu.service.systemsetting.SystemSettingService;
import com.unicom.swdx.module.edu.service.teacherinformation.TeacherInformationService;
import com.unicom.swdx.module.edu.service.teacherinformation.TeacherInformationServiceImpl;
import com.unicom.swdx.module.edu.service.training.TraineeService;
import com.unicom.swdx.module.edu.service.training.TraineeServiceImpl;
import com.unicom.swdx.module.edu.utils.classcourse.ClassCourseUtil;
import com.unicom.swdx.module.edu.utils.date.DateConvertUtil;
import com.unicom.swdx.module.edu.utils.holiday.HolidayUtils;
import com.unicom.swdx.module.edu.utils.numberconverter.NumberConverter;
import com.unicom.swdx.module.edu.utils.tree.TreeDataUtil;
import com.unicom.swdx.module.edu.utils.tree.dto.TreeNodeDTO;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.text.MessageFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.util.date.DateUtils.*;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;


/**
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ClassCourseServiceImpl extends ServiceImpl<ClassCourseMapper, ClassCourseDO> implements ClassCourseService {

    public static final String CONFLICT_INFO_TEMPLATE = "老师冲突班级：{0},教室冲突班级：{1}";

    public static final Long OPTIONAL_COURSE_ID = -1L;

    @Resource
    private ClassCourseMapper classCourseMapper;

    @Resource
    private ClassManagementService classManagementService;

    @Resource
    private ClassManagementServiceImpl classManagementServiceImpl;

    @Resource
    private ClassroomLibraryService classroomLibraryService;

    @Resource
    private TeacherInformationService teacherInformationService;

    @Resource
    private CoursesService coursesService;

    @Resource
    private CoursesServiceImpl coursesServiceImpl;

    @Resource
    private PlanService planService;

    @Resource
    private ClassManagementMapper classManagementMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private HolidayMapper holidayMapper;

    @Resource
    SystemSettingService systemSettingService;

    @Resource
    public ClassCourseUtil classCourseUtil;

    @Resource
    public ClassCourseTeacherMapper classCourseTeacherMapper;

    @Autowired
    private TraineeService traineeService;

    @Resource
    private ClassCourseOrderMapper classCourseOrderMapper;

    @Resource
    private CoursesMapper coursesMapper;

    @Resource
    private ClockInInfoMapper clockInInfoMapper;

    @Resource
    private PlanMapper planMapper;

    @Lazy
    @Resource
    private ClockInInfoService clockInInfoService;

    @Resource
    private TeacherInformationMapper teacherInformationMapper;

    @Resource
    private EduCommonService eduCommonService;

    @Resource
    private ClassroomLibraryMapper classroomLibraryMapper;

    @Override
    public Long createClassCourse(ClassCourseCreateReqVO createReqVO) {
        // 插入
        ClassCourseDO classCourse = ClassCourseConvert.INSTANCE.convert(createReqVO);
        // 教学计划-常用教室补充
        if(createReqVO.getPlanId() != null){
            PlanDO plan = planService.getPlan(createReqVO.getPlanId());
            if (plan.getClassroomId() != null) {
                classCourse.setClassroomId(plan.getClassroomId());
            }
        }else {
            //教学计划id为空时，主动查询教学计划，看classCourse的date字段是属于该班级哪个教学计划的开始日期和结束日期之间，如果能找到对应的教学计划，则手动设置planId，找不到则仍然为空
            LambdaQueryWrapper<PlanDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.le(PlanDO::getBeginDate, createReqVO.getDate());
            queryWrapper.ge(PlanDO::getEndDate, createReqVO.getDate());
            queryWrapper.eq(PlanDO::getClassId, createReqVO.getClassId());
            List<PlanDO> planDOList = planMapper.selectList(queryWrapper);
            if(!planDOList.isEmpty()){
                classCourse.setPlanId(planDOList.get(0).getId());
            }
        }
        // 校验是否有冲突时段
        List<ClassCourseDO> classCourseDOList = this.lambdaQuery()
                .lt(ClassCourseDO::getBeginTime, createReqVO.getEndTime())
                .gt(ClassCourseDO::getEndTime, createReqVO.getBeginTime())
                .list();
        classCourseDOList = classCourseDOList.stream().filter(classCourseDO -> createReqVO.getClassId().equals(classCourseDO.getClassId())).collect(Collectors.toList());
        if (!classCourseDOList.isEmpty()) {
            throw exception(CLASS_COURSE_TIME_CONFLICT);
        }
        // 课程上午  下午  默认开启考勤
        if (StringUtils.equals(createReqVO.getPeriod(), "0") || StringUtils.equals(createReqVO.getPeriod(), "1")) {
            classCourse.setIsCheck(true);
        }
        classCourseMapper.insert(classCourse);
        // 返回
        return classCourse.getId();
    }

    /**
     * 批量创建班级课程安排
     *
     * @param createReqVOList 创建信息列表
     * @param classCourseId  班级课程安排id
     */
    @Override
    public void createBatch(Long classCourseId , List<ClassCourseCreateReqVO> createReqVOList){
        if (CollectionUtils.isEmpty(createReqVOList)) {
            return;
        }
        // 1. 获取所有计划 ID 和 班级 ID
        Set<Long> planIds = createReqVOList.stream()
                .map(ClassCourseCreateReqVO::getPlanId)
                .collect(Collectors.toSet());
        Set<Long> classIds = createReqVOList.stream()
                .map(ClassCourseCreateReqVO::getClassId)
                .collect(Collectors.toSet());
        // 2. 一次性查询这些计划下的所有课程安排和教学计划
        List<ClassCourseDO> existingCourses = this.lambdaQuery()
                .in(ClassCourseDO::getClassId, classIds)
                .list();
        List<PlanDO> plans = !planIds.isEmpty() ? planService.getPlanList(planIds) : Collections.emptyList();
        Map<Long, PlanDO> planMap = plans.stream().collect(Collectors.toMap(PlanDO::getId, p -> p));
        List<ClassCourseDO> classCoursesToInsert = new ArrayList<>();
        for (ClassCourseCreateReqVO reqVO : createReqVOList) {
            ClassCourseDO classCourse = ClassCourseConvert.INSTANCE.convert(reqVO);
            if(reqVO.getPlanId() != null){
                PlanDO plan = planMap.get(reqVO.getPlanId());
                if (plan != null && plan.getClassroomId() != null) {
                    classCourse.setClassroomId(plan.getClassroomId());
                }
            }
            // 3. 冲突检测在内存中完成
            List<ClassCourseDO> planCourses = existingCourses.stream()
                    .filter(c -> c.getClassId().equals(reqVO.getClassId()) && !Objects.equals(c.getId(), classCourseId))
                    .collect(Collectors.toList());
            boolean hasConflict = planCourses.stream().anyMatch(existing ->
                    (reqVO.getBeginTime().isBefore(existing.getEndTime()) &&
                            reqVO.getEndTime().isAfter(existing.getBeginTime()))
            );
            if (hasConflict) {
                throw exception(CLASS_COURSE_TIME_CONFLICT);
            }
            if ("0".equals(reqVO.getPeriod()) || "1".equals(reqVO.getPeriod())) {
                classCourse.setIsCheck(true);
            }
            classCoursesToInsert.add(classCourse);
            // 追加用于后续的冲突检测
            existingCourses.add(classCourse);
        }
        //删除原课表格子
        classCourseMapper.deleteById(classCourseId);
        //批量新增
        if (!classCoursesToInsert.isEmpty()) {
            classCourseMapper.insertBatch(classCoursesToInsert);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateClassCourse(ClassCourseUpdateReqVO updateReqVO) {
        // 校验存在
        ClassCourseDO courseDOPre = this.validateClassCourseExists(updateReqVO.getId());

        ClassCourseDO courseDOGetTime = new ClassCourseDO();
        BeanUtils.copyProperties(courseDOPre, courseDOGetTime);
        // 校验是否有冲突时段
        if(updateReqVO.getBeginTime() != null && updateReqVO.getEndTime() != null){
            List<ClassCourseDO> classCourseDOList = this.lambdaQuery()
                    .lt(ClassCourseDO::getBeginTime, updateReqVO.getEndTime())
                    .gt(ClassCourseDO::getEndTime, updateReqVO.getBeginTime())
                    .list();
            classCourseDOList = classCourseDOList.stream().filter(classCourseDO -> updateReqVO.getClassId().equals(classCourseDO.getClassId()) && !classCourseDO.getId().equals(updateReqVO.getId())).collect(Collectors.toList());
            if (!classCourseDOList.isEmpty()) {
                throw exception(CLASS_COURSE_TIME_CONFLICT);
            }
        }

        // 更新
        if(updateReqVO.getPeriod() != null){
            // 课程上午  下午  默认开启考勤
            if (StringUtils.equals(updateReqVO.getPeriod(), PeriodEnum.MORNING.getCode().toString()) || StringUtils.equals(updateReqVO.getPeriod(), PeriodEnum.AFTERNOON.getCode().toString())) {
                updateReqVO.setIsCheck(true);
            }
        }
        ClassCourseDO updateObj = ClassCourseConvert.INSTANCE.convert(updateReqVO);
        //设置数据更新时间为当前时间
        updateObj.setUpdateTime(LocalDateTime.now());

        ObjectUtils.mergeNonNullFields(courseDOPre , updateObj);

        classCourseMapper.updateById(courseDOPre);

        if(updateReqVO.getBeginTime() == null || updateReqVO.getEndTime() == null){
            //将指定的课表格子开始时间和结束时间置空
            LambdaUpdateWrapper<ClassCourseDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ClassCourseDO::getId, updateReqVO.getId());
            updateWrapper.set(ClassCourseDO::getBeginTime, null);
            updateWrapper.set(ClassCourseDO::getEndTime, null);
            classCourseMapper.update(null, updateWrapper);
        }


        //筛选已合班的格子，并同步更新该格子的所有变化
        if(courseDOPre.getIsMerge()){
            List<ClassCourseUpdateReqVO> newUpdateList = new ArrayList<>();
            LambdaQueryWrapper<ClassCourseDO> classCourseDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            // 查询给定班级下是否有授课时间与原课表格子开始时间、结束时间的小时、分钟相等的格子

            if(courseDOGetTime.getBeginTime() != null && courseDOGetTime.getEndTime() != null){
                classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getDate, courseDOGetTime.getDate())
                        .ne(ClassCourseDO::getClassId, courseDOGetTime.getClassId())
                        .eq(ClassCourseDO::getCourseId, courseDOGetTime.getCourseId())
                        .apply("to_char(begin_time, 'HH24:MI') = to_char({0}, 'HH24:MI')", courseDOGetTime.getBeginTime())
                        .apply("to_char(end_time, 'HH24:MI') = to_char({0}, 'HH24:MI')", courseDOGetTime.getEndTime());
            }else{
                classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getDate, courseDOPre.getDate())
                        .ne(ClassCourseDO::getClassId, courseDOPre.getClassId())
                        .eq(ClassCourseDO::getPeriod, courseDOPre.getPeriod())
                        .eq(ClassCourseDO::getCourseId, courseDOPre.getCourseId());
            }

            classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getIsMerge, true);
            List<ClassCourseDO> classCourseDOList = classCourseMapper.selectList(classCourseDOLambdaQueryWrapper);
            classCourseDOList.forEach(entity -> {
                ClassCourseUpdateReqVO newVO = new ClassCourseUpdateReqVO();
                // 拷贝原 VO 的所有属性
                BeanUtils.copyProperties(courseDOPre, newVO);
                // 替换 ID 为 entity 的 ID
                newVO.setId(entity.getId());
                newVO.setClassId(entity.getClassId());

                if(updateObj.getBeginTime()!=null && updateObj.getEndTime()!=null){
                    newVO.setBeginTime(updateObj.getBeginTime());
                    newVO.setEndTime(updateObj.getEndTime());
                }

                newUpdateList.add(newVO);
            });

            //根据合并课程信息同步批量更新课程表
            if(!newUpdateList.isEmpty()){
                updateTableBatchByMergeInfo(StatusEnum.TEMPORARY.getCode(), newUpdateList);
            }
        }

    }

    @Override
    public void deleteClassCourse(Long id) {
        // 校验存在
        this.validateClassCourseExists(id);
        // 删除
        classCourseMapper.deleteById(id);
        //删除关联数据
        List<Long> ccIds = new ArrayList<>();
        ccIds.add(id);
        classCourseUtil.deleteClassCourseLinkInfo(ccIds);
    }

    public ClassCourseDO validateClassCourseExists(Long id) {

        ClassCourseDO classCourseDO =  classCourseMapper.selectById(id);
        if (classCourseDO == null) {
            throw exception(CLASS_COURSE_NOT_EXISTS);
        }
        return classCourseDO;
    }

    @Override
    public ClassCourseRespVO getClassCourse(Long id) {
        return classCourseMapper.selectRespVOById(id);
    }

    @Override
    public List<ClassCourseDO> getClassCourseList(Collection<Long> ids) {
        return classCourseMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ClassCourseDO> getClassCoursePage(ClassCoursePageReqVO pageReqVO) {
//        // 获取当前班级所启用的教学计划对应的课表
//        if (pageReqVO.getPlanId() == null) {
//            List<PlanDO> planList = planService.lambdaQuery()
//                    .eq(PlanDO::getClassId, pageReqVO.getClassId())
//                    .eq(PlanDO::getStatus, PlanStatusEnum.ON.getCode())
//                    .list();
//            if (planList.isEmpty()) {
//                return null;
//            }
//            planDO = planList.get(0);
//            pageReqVO.setPlanId(planDO.getId());
//        } else {
//            planDO = planService.getPlan(pageReqVO.getPlanId());
//        }
        //获取当前班级下的所有教学计划，用以添加班级课表格子的常用教室信息
        LambdaQueryWrapper<PlanDO> planWrapper = new LambdaQueryWrapper<>();
        planWrapper.eq(PlanDO::getClassId, pageReqVO.getClassId());
        List<PlanDO> planList = planMapper.selectList(planWrapper);

        PlanDO planDOGet = null;
        if (pageReqVO.getPlanId() != null) {
            planDOGet = planService.getPlan(pageReqVO.getPlanId());
            planList.add(planDOGet);
        }

        List<Long> classRoomIdsAlways = planList.stream().map(PlanDO::getClassroomId).collect(Collectors.toList());

        PageResult<ClassCourseDO> classCourseDOPageResult = classCourseMapper.selectPage(pageReqVO);
        // 获取所有合班授课分组信息 分组内的排课id数量必大于1
        List<ClassMergeCourseGroupRespDTO> classMergeCourseGroupList = classCourseMapper.getClassMergeCourseGroup(null);
        // 所有有合班授课的排课id集合
        Set<Long> mergedClassCourseIdSet = classMergeCourseGroupList.stream()
                .map(ClassMergeCourseGroupRespDTO::getId)
                .collect(Collectors.toSet());

        List<ClassCourseDO> list = classCourseDOPageResult.getList();
        if (!list.isEmpty()) {
            // 教室
            List<Long> classRoomIds = list.stream().map(ClassCourseDO::getClassroomId).filter(Objects::nonNull).collect(Collectors.toList());
            // 添加常用教室信息
            classRoomIds.addAll(classRoomIdsAlways);
            List<ClassroomLibraryDO> classroomLibraryList = !classRoomIds.isEmpty() ? classroomLibraryService.listByIds(classRoomIds) : new ArrayList<>();
            Map<Integer, ClassroomLibraryDO> classroomLibraryMap = classroomLibraryList.stream()
                    .collect(Collectors.toMap(ClassroomLibraryDO::getId, cmDO -> cmDO));

            List<Long> teacherIds = list.stream()
                    .map(ClassCourseDO::getTeacherIdString)
                    .filter(Objects::nonNull)
                    .flatMap(teacherIdString -> Arrays.stream(teacherIdString.split(",")))
                    .map(String::trim)
                    .filter(id -> {
                        try {
                            // 尝试转换为 Long
                            Long.parseLong(id);
                            return true;
                        } catch (NumberFormatException e) {
                            // 如果抛出异常，则过滤掉
                            return false;
                        }
                    })
                    .map(Long::valueOf)
                    .distinct() // 去除重复的 ID
                    .collect(Collectors.toList());
            List<TeacherInformationDO> teacherInformationList = !teacherIds.isEmpty() ? teacherInformationService.listByIds(teacherIds) : new ArrayList<>();
            Map<Long, TeacherInformationDO> teacherInformationMap = teacherInformationList.stream()
                    .collect(Collectors.toMap(TeacherInformationDO::getId, cmDO -> cmDO));
            // 课程
            List<Long> courseList = list.stream().map(ClassCourseDO::getCourseId).filter(Objects::nonNull).collect(Collectors.toList());
            // 带有字典值的课程
            Map<Long, CoursesRespVO> coursesInfoMap = getCoursesInfoMap(courseList);

            // 冲突
            // 获取所有其他班级生效的排课信息，用于判定冲突
            // 不与自己班级判断冲突信息
            Long classId = pageReqVO.getClassId();
            if(classId == null && planDOGet != null){
                classId = planDOGet.getClassId();
            }
            // 开始判断冲突
            // 课程,获取传入批量格子的开始结束时间用于判断冲突
            // 本次用于判定冲突查询到的对象
            Map<Long, ClassCourseDO> classCourseMap = list.stream()
                    .collect(Collectors.toMap(ClassCourseDO::getId, cmDO -> cmDO));
            if (list.isEmpty()) {
                throw exception(CLASS_COURSE_NOT_EXISTS);
            }
            // 找到当前所有已发布的课程（除自己班级外，不与自己班级判断冲突信息）
//            List<PlanDO> planList = planService.lambdaQuery().eq(PlanDO::getStatus, PlanStatusEnum.ON.getCode()).ne(PlanDO::getClassId, classId).list();
//            List<Long> planIds = planList.stream().map(PlanDO::getId).collect(Collectors.toList());
            List<ClassCourseDO> classCourseList = this.lambdaQuery()
                            .ne(ClassCourseDO::getClassId, classId)
                            .isNotNull(ClassCourseDO::getCourseId)
                            .list();
            // 班级
            List<Long> classIds = classCourseList.stream().map(ClassCourseDO::getClassId).collect(Collectors.toList());
            // 缓存中加上自己的班级id
            classIds.add(classId);
            // 将Long值转换为Integer
            List<Integer> classIdsAsIntegers = classIds.stream()
                    .map(Long::intValue)
                    .collect(Collectors.toList());
            List<ClassManagementDO> classManagementList = !classIdsAsIntegers.isEmpty() ? classManagementService.getClassManagementList(classIdsAsIntegers) : new ArrayList<>();
            // 开始判定冲突
            for (ClassCourseDO vo : list) {
                // 过滤没有排课的格子
                if (vo.getCourseId() == null) {
                    continue;
                }
                // 设置合班显示标签
                vo.setIsShowMergeTag(mergedClassCourseIdSet.contains(vo.getId()));
                ClassCourseDO classCourseDOTemp = classCourseMap.get(vo.getId());
                // 冲突教室-班级Id
                List<Long> classRoomIdList = new ArrayList<>();
                // 冲突教师-班级Id
                List<Long> teacherIdList = new ArrayList<>();
                // 收集冲突对象，用于判断产生冲突的对象是否都已合班
                List<ClassCourseDO> conflictDOList = new ArrayList<>();
                for (ClassCourseDO classCourseDO : classCourseList) {
                    try {
                        // 判断时间是否冲突
                        boolean result = false;
                        if(vo.getIsTemporary() || vo.getBeginTime() == null || vo.getEndTime() == null){
                            //若被比较的格子是草稿状态，则直接判定午别是否一致来判定是否冲突
                            if(Objects.equals(vo.getPeriod(), classCourseDO.getPeriod()) && Objects.equals(vo.getDate(), classCourseDO.getDate())){
                                result = true;
                            }
                        }else{
                            //被比较的格子是发布状态，根据时间是否交叉来判定冲突，且只与已发布的格子比较
                            if(classCourseDO.getIsTemporary()){
                                continue;
                            }
                            result = isTimeOverlap(classCourseDOTemp.getBeginTime(), classCourseDOTemp.getEndTime(), classCourseDO.getBeginTime(), classCourseDO.getEndTime());
                        }
                        // 判定最终是否冲突
                        boolean conflict = false;
                        if (result) {
                            if (classCourseDO.getClassroomId() != null && classCourseDO.getClassroomId().equals(vo.getClassroomId())) {
                                classRoomIdList.add(classCourseDO.getClassId());
                                conflict = true;
                            }
                            if (classCourseDO.getTeacherId() != null && classCourseDO.getTeacherId().equals(vo.getTeacherId())) {
                                teacherIdList.add(classCourseDO.getClassId());
                                conflict = true;
                            }
                        }
                        if (conflict) {
                            conflictDOList.add(classCourseDO);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage());
                    }
                }
                // 若自己本身为合班，且所有冲突对象都为合班，则不冲突，不设置冲突信息
                boolean isAllMerge = true;
                for (ClassCourseDO temp : conflictDOList) {
                    if (!temp.getIsMerge()) {
                        isAllMerge = false;
                        break;
                    }
                }
                if (classCourseDOTemp != null && !classCourseDOTemp.getIsMerge()) {
                    isAllMerge = false;
                }
                if (isAllMerge) {
                    continue;
                }
                // 设置冲突信息
                if (!classRoomIdList.isEmpty() || !teacherIdList.isEmpty()) {
                    String classroomClassName = "-";
                    String teacherClassName = "-";
                    if (!classRoomIdList.isEmpty()) {
                        List<ClassManagementDO> classList = classManagementList.stream()
                                .filter(classroomLibraryDO -> classRoomIdList.contains(classroomLibraryDO.getId()))
                                .collect(Collectors.toList());
                        classroomClassName = classList.stream()
                                .map(ClassManagementDO::getClassName)
                                .collect(Collectors.joining("，"));
                    }
                    if (!teacherIdList.isEmpty()) {
                        List<ClassManagementDO> classList = classManagementList.stream()
                                .filter(classroomLibraryDO -> teacherIdList.contains(classroomLibraryDO.getId()))
                                .collect(Collectors.toList());
                        teacherClassName = classList.stream()
                                .map(ClassManagementDO::getClassName)
                                .collect(Collectors.joining("，"));
                    }
                    String conflictInfo = MessageFormat.format(CONFLICT_INFO_TEMPLATE, teacherClassName, classroomClassName);
                    vo.setConflictInfo(conflictInfo);
                }
            }
            // 补充其他信息
            for (ClassCourseDO classCourse : list) {
                try {
                    // 设置date对应的周几信息
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY);
                    LocalDate date = LocalDate.parse(classCourse.getDate(), formatter);
                    DayOfWeek dayOfWeek = date.getDayOfWeek();
                    classCourse.setWeekDay(dayOfWeek.toString());
                    // 时间范围设置回显
                    if(classCourse.getBeginTime() != null && classCourse.getEndTime() != null){
                        List<LocalDateTime> localDateTimeList = new ArrayList<>();
                        localDateTimeList.add(classCourse.getBeginTime());
                        localDateTimeList.add(classCourse.getEndTime());
                        classCourse.setLocalDateTimeList(localDateTimeList);
                        String result = localDateTimeList.stream()
                                .map(dt -> String.format("%02d:%02d", dt.getHour(), dt.getMinute()))
                                .collect(Collectors.joining("-"));
                        classCourse.setTimeRange(result);
                    }
                    // 设置常用教室id
                    List<PlanDO> planTempList = planList.stream().filter(planDO -> planDO.getId().equals(classCourse.getPlanId())).collect(Collectors.toList());
                    if (!planTempList.isEmpty()) {
                        PlanDO planDO = planTempList.get(0);
                        if (planDO.getClassroomId() != null) {
                            classCourse.setClassroomIdAlways(planDO.getClassroomId());
                            classCourse.setClassroomIdAlwaysName(classroomLibraryMap.get(planDO.getClassroomId().intValue()).getClassName());
                        }
                    }
                    // 非选修课情况
                    if (!OPTIONAL_COURSE_ID.equals(classCourse.getCourseId())) {
                        if (classCourse.getClassroomId() != null) {
                            ClassroomLibraryDO classroomLibraryDO = classroomLibraryMap.get(classCourse.getClassroomId().intValue());
                            // 补充教室信息
                            if (classroomLibraryDO != null) {
                                classCourse.setClassroomName(classroomLibraryDO.getClassName());
                            }
                        }
                        // 补充老师信息
                        if(!classCourse.getDepartment()){
                            if (StringUtils.isNotEmpty(classCourse.getTeacherIdString())) {
                                // 以逗号分隔的教师ID字符串拆分并获取每个教师的信息
                                String[] teacherIdList = classCourse.getTeacherIdString().split(",");
                                StringBuilder teacherNames = new StringBuilder();

                                for (String teacherIdStr : teacherIdList) {
                                    try {
                                        // 转换为Long类型
                                        Long teacherId = Long.valueOf(teacherIdStr.trim());
                                        TeacherInformationDO teacherInformationDO = teacherInformationMap.get(teacherId);
                                        if (teacherInformationDO != null) {
                                            if (teacherNames.length() > 0) {
                                                // 如果不是第一个教师，添加逗号
                                                teacherNames.append("，");
                                            }
                                            teacherNames.append(teacherInformationDO.getName());
                                        }
                                    } catch (NumberFormatException e) {
                                        log.error(e.getMessage());
                                    }
                                }
                                // 设置教师名称，多个教师的名字用逗号分隔
                                classCourse.setTeacherName(teacherNames.toString());
                            }
                        }else{
                            //部门授课
                            classCourse.setTeacherName(classCourse.getTeacherIdString());
                        }
                        // 补充课程信息
                        CoursesRespVO coursesRespVO = coursesInfoMap.get(classCourse.getCourseId());
                        if (coursesRespVO != null) {
                            classCourse.setCourseName(coursesRespVO.getName());
                            classCourse.setCourseType(CoursesTypeEnum.getDescByType(coursesRespVO.getCoursesType()));
                            classCourse.setCourseTypeId(coursesRespVO.getCoursesType().toString());
                            classCourse.setEducateFormId(coursesRespVO.getEducateFormId());
                            classCourse.setEducateFormName(coursesRespVO.getEducateForm());
                            classCourse.setThemeId(coursesRespVO.getThemeId());
                            classCourse.setThemeName(coursesRespVO.getTheme());
                            if (CoursesTypeEnum.TEACHING_ACTIVITY.getType().equals(coursesRespVO.getCoursesType())) {
                                classCourse.setEducateFormName(CoursesTypeEnum.TEACHING_ACTIVITY.getDesc());
                                classCourse.setActivityType(CoursesTypeEnum.TEACHING_ACTIVITY.getDesc());
                            }
                        }
                    } else {
                        // 为选修课时
                        classCourse.setCourseType(CoursesTypeEnum.OPTIONAL_COURSE.getDesc());
                        classCourse.setCourseTypeId(CoursesTypeEnum.OPTIONAL_COURSE.getType().toString());
                    }
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            classCourseDOPageResult.setList(list);
        }
        return classCourseDOPageResult;
    }

    @Override
    public List<ClassCourseDO> getClassCourseList(ClassCourseExportReqVO exportReqVO) {
        return classCourseMapper.selectList(exportReqVO);
    }


    /**
     * 获取合班授课分组信息列表
     *
     * @param classCourseIds 筛选出包含这些排课id的合班授课分组信息列表
     * @return 合班授课分组信息列表
     */
    @Override
    public List<ClassMergeCourseGroupRespDTO> getClassMergeCourseGroup(List<Long> classCourseIds) {
        return classCourseMapper.getClassMergeCourseGroup(classCourseIds);
    }

    public static Cache<MyClassScheduleParamsVO, List<ScheduleChartVO> > resultCache=
            CacheBuilder.newBuilder()
                    .initialCapacity(1024) // 初始容量
                    .maximumSize(1024*10)   // 设定最大容量
                    .expireAfterWrite(2L, TimeUnit.MINUTES) // 设定写入过期时间
                    .concurrencyLevel(8)  // 设置最大并发写操作线程数
                    .build();



    /**
     * 获得我的课表分页  缓存2分钟
     *
     * @param myClassScheduleParamsVO 分页查询
     * @return MyClassScheduleVO
     */
    @Override
    public List<ScheduleChartVO> getMyClassSchedule(MyClassScheduleParamsVO myClassScheduleParamsVO) {
        // 判断登录用户是否在学员表中存在
        Long userIds = SecurityFrameworkUtils.getLoginUserId();
        myClassScheduleParamsVO.setUserIds(userIds);
        List<ScheduleChartVO>  listtemp;
        try {
            listtemp = resultCache.get(myClassScheduleParamsVO, () -> {
                // 获取学员id
                Long eduTraineeId = classCourseMapper.getEduTraineeId(userIds);
                myClassScheduleParamsVO.setTraineeId(eduTraineeId);
                if (eduTraineeId == null || eduTraineeId == 0) {
                    throw exception(NO_PERMISSION_ERROR);
                }
                // 获取学员班级id
                Long eduTraineeClassId = classCourseMapper.getEduTraineeClassId(userIds);
                myClassScheduleParamsVO.setClassID(eduTraineeClassId);
                //获取学员所在班级的人数
                //为每个班级新增班级人数字段信息
                List<TraineeDO> traineeDOList = traineeService.lambdaQuery().eq(TraineeDO::getStatus, TraineeStatusEnum.REPORTED.getStatus()).list();
                long traineeNum = traineeDOList.stream()
                        .filter(trainee -> Objects.equals(trainee.getClassId(), eduTraineeClassId))
                        .count();
                // 获得学员的选修课表
                List<MyClassScheduleVO> optionalCourse = classCourseMapper.getOptionalCourse(myClassScheduleParamsVO);
                if (optionalCourse == null || optionalCourse.isEmpty()) {
                    myClassScheduleParamsVO.setIsCover(2);
                } else {
                    myClassScheduleParamsVO.setIsCover(1);
                    List<Long> ids = optionalCourse.stream().map(MyClassScheduleVO::getClassCourseId).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(ids)){
                        Map<Long, Boolean> map = classCourseMapper.selectBatchIds(ids).stream().collect(Collectors.toMap(ClassCourseDO::getId, ClassCourseDO::getIsCheck));
                        optionalCourse.forEach(item->{
                            item.setIsCheck(map.getOrDefault(item.getClassCourseId(),false));
                        });
                    }
                }
                // 获取班级的必修课
                List<MyClassScheduleVO> myClassScheduleVOS = classCourseMapper.getMyClassSchedule(myClassScheduleParamsVO);
                // 为选修课 插入课程名字
                if (myClassScheduleParamsVO.getIsCover() == 2) {
                    for (MyClassScheduleVO list : myClassScheduleVOS) {
                        if (list.getCourseName() == null) {
                            list.setCourseName("选修课");
                            list.setCoursesType(2L);
                        }
                    }
                }

                // 获取指定排课id列表的合班排课分组情况 分组内的排课id数量必大于1
                List<ClassMergeCourseGroupRespDTO> classMergeCourseGroupList = classCourseMapper.getClassMergeCourseGroup(myClassScheduleVOS.stream()
                        .map(MyClassScheduleVO::getClassCourseId)
                        .collect(Collectors.toList()));
                // 所有有合班授课的排课id->排课信息 Map
                Map<Long, ClassMergeCourseGroupRespDTO> classCourseIdToClassInfoMap = classMergeCourseGroupList.stream()
                        .collect(Collectors.toMap(ClassMergeCourseGroupRespDTO::getId, o -> o, (a, b) -> a));
                // 标记合班授课分组Map
                Map<Integer, List<ClassMergeCourseGroupRespDTO>> classMergeCourseGroupMap = classMergeCourseGroupList.stream().collect(Collectors.groupingBy(ClassMergeCourseGroupRespDTO::getGroupNum));

                myClassScheduleVOS.forEach(vo -> {
                    List<ClassInfoRespVO> mergedClasses  = new ArrayList<>();
                    // 排课是否在合班授课分组中 （分组中的合班授课数量都大于2 ）
                    vo.setIsShowMergeTag(classCourseIdToClassInfoMap.containsKey(vo.getClassCourseId()));
                    if (Boolean.TRUE.equals(vo.getIsMerge()) && Boolean.TRUE.equals(vo.getIsShowMergeTag())) {
                        // 设置合班授课
                        List<ClassMergeCourseGroupRespDTO> classMergeCourseGroupRespDTOS = classMergeCourseGroupMap.get(classCourseIdToClassInfoMap.get(vo.getClassCourseId()).getGroupNum());
                        if (CollUtil.isNotEmpty(classMergeCourseGroupRespDTOS)) {
                            mergedClasses = classMergeCourseGroupRespDTOS.stream()
                                    .filter(o -> !o.getId().equals(vo.getClassCourseId()))
                                    .map(o -> {
                                        ClassInfoRespVO classInfoRespVO = new ClassInfoRespVO();
                                        classInfoRespVO.setName(o.getClassName());
                                        classInfoRespVO.setId(o.getClassId());
                                        return classInfoRespVO;
                                    }).collect(Collectors.toList());
                        }
                    }
                    vo.setMergedClass(mergedClasses);
                });
                //给teacherName复制
                assignMySchuleTeacherName(myClassScheduleVOS);
                // 合并两个列表
                List<MyClassScheduleVO> combinedList = new ArrayList<>(myClassScheduleVOS);
                combinedList.addAll(optionalCourse);
                // 按开始时间排序
                combinedList.sort(Comparator.comparing(MyClassScheduleVO::getBeginTime));
                //为每堂课附带班级人数
                combinedList.forEach(vo -> vo.setTraineeNum(traineeNum));
                // 按天分组并排序
                Map<LocalDate, List<MyClassScheduleVO>> groupedByDate = combinedList.stream()
                        .collect(Collectors.groupingBy(vo -> vo.getBeginTime().toLocalDate()));
                // 创建 ScheduleChart 列表，整合分组结果
                List<ScheduleChartVO> scheduleCharts = new ArrayList<>();
                // 按日期升序遍历分组
                List<LocalDate> sortedDates = new ArrayList<>(groupedByDate.keySet());
                // 按日期升序排序
                sortedDates.sort(Comparator.naturalOrder());
                for (LocalDate date : sortedDates) {
                    // 转换为字符串日期
                    String dateStr = date.toString();
                    ScheduleChartVO chart = new ScheduleChartVO();
                    chart.setDate(dateStr);
                    chart.setSchedules(groupedByDate.get(date));
                    scheduleCharts.add(chart);
                }
                return scheduleCharts;
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        return listtemp;
    }

    /**
     * 获取节假日
     * @param year 年
     * @param month 月
     * @return 节假日日期
     */
    @Override
    public Set<String> getVacation(int year, int month) {
        return HolidayUtils.JJR(year, month, holidayMapper);
    }

    @Override
    public void updateBatch(List<ClassCourseUpdateReqVO> updateReqVOList) {
        List<ClassCourseUpdateReqVO> updateList = updateReqVOList.stream().filter(vo -> vo.getId() != null).collect(Collectors.toList());
        List<ClassCourseUpdateReqVO> addList = updateReqVOList.stream().filter(vo -> vo.getId() == null).collect(Collectors.toList());
        // 更新
        if (!updateList.isEmpty()) {
            List<ClassCourseDO> updateDOList = ClassCourseConvert.INSTANCE.convertToDoList(updateList);
            //批量更新时设置数据更新时间为当前时间
            updateDOList.forEach(vo -> {
                vo.setUpdateTime(LocalDateTime.now());
            });
            classCourseMapper.updateBatch(updateDOList);
        }
        // 新增
        if (!addList.isEmpty()) {
            List<ClassCourseDO> updateDOList = ClassCourseConvert.INSTANCE.convertToDoList(addList);
            classCourseMapper.insertBatch(updateDOList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateTableBatch(String status, List<ClassCourseUpdateReqVO> updateReqVOList) {

        //根据updateReqVOList每个对象的id去查询所有原课表格子的信息
        List<ClassCourseDO> classCourseDOListPre = classCourseMapper.selectBatchIds(updateReqVOList.stream().map(ClassCourseUpdateReqVO::getId).collect(Collectors.toList()));
        //根据id组成map
        Map<Long, ClassCourseDO> classCourseDOMap = classCourseDOListPre.stream().collect(Collectors.toMap(ClassCourseDO::getId, o -> o));

        // 批量更新
        List<ClassCourseDO> updateDOList = ClassCourseConvert.INSTANCE.convertToDoList(updateReqVOList);

//        //筛选并去掉数据库中为已发布的课表格子，不允许更新，已发布的课表格子在调课模块更新
//        if(!CollectionUtils.isEmpty(updateDOList)){
//            ClassCourseDO classCourseDO = updateDOList.get(0);
//            LambdaQueryWrapper<ClassCourseDO> classCourseDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            classCourseDOLambdaQueryWrapper
//                    .eq(ClassCourseDO::getIsTemporary, false)
//                    .eq(ClassCourseDO::getClassId, classCourseDO.getClassId())
//                    .select(ClassCourseDO::getId);
//            // 执行查询，返回 id 列表
//            List<Long> classCourseIdList = classCourseMapper.selectList(classCourseDOLambdaQueryWrapper)
//                    .stream()
//                    .map(ClassCourseDO::getId)
//                    .collect(Collectors.toList());
//            // 去掉updateDOList中id在classCourseIdList中的数据
//            updateDOList.removeIf(vo -> classCourseIdList.contains(vo.getId()));
//        }

//        if (StatusEnum.PUBLISHED.getCode().equals(status)) {



            updateDOList.forEach(vo -> {
                //保存并发布时首先校验是否还存在没有时间安排且排了课的空格子，若有则提示
//                if((vo.getBeginTime() == null || vo.getEndTime() == null) && vo.getCourseId() != null){
//                    throw exception(ELECTIVE_RELEASE_TIME_ERROR);
//                }
                if (vo.getCourseId() != null) {
//                    vo.setIsTemporary(false);
//                    // 默认开启合班授课
//                    vo.setIsMerge(true);


                    ClassMergeReqVO reqVO =new  ClassMergeReqVO();
                    reqVO.setCourseId(vo.getCourseId());
                    reqVO.setBeginTime(vo.getBeginTime());
                    reqVO.setEndTime(vo.getEndTime());
                    // 获取该课程合班授课的课程
                    List<ClassCourseClassNameRespDTO> mergedClassCourseList = classCourseMapper.getMergedClassListByReqVO(reqVO);


                    if(CollectionUtil.isNotEmpty(mergedClassCourseList)){

                        vo.setIsShowMergeTag(true);
                    }else{
                        vo.setIsShowMergeTag(false);
                    }

                }
            });
//        }

        //临时逻辑：将teacherIdString中的第一位老师放置于teacherId字段
        for (ClassCourseDO classCourse : updateDOList) {
            if (StringUtils.isNotEmpty(classCourse.getTeacherIdString())) {
                // 获取逗号分隔的教师ID字符串并提取第一个ID
                String[] teacherIds = classCourse.getTeacherIdString().split(",");
                // 将第一个教师ID赋值给 teacherId 字段
                if (teacherIds.length > 0) {
                    try {
                        classCourse.setTeacherId(Long.parseLong(teacherIds[0]));
                    } catch (NumberFormatException e) {
                        // 如果解析失败，处理异常
                        log.error("无法解析教师ID: {}", teacherIds[0]);
                    }
                }
            }
        }

        // 更新班级课程表中 teacherIdString 和 classroomId 字段为 null，防止保存为草稿时无法置空（如将原本的专题课换为活动课，对应的教师和班级不会置空）
        List<Long> teacherNullIds = updateDOList.stream()
                .filter(item -> item.getTeacherIdString() == null)
                .map(ClassCourseDO::getId)
                .collect(Collectors.toList());
        List<Long> classroomNullIds = updateDOList.stream()
                .filter(item -> item.getClassroomId() == null)
                .map(ClassCourseDO::getId)
                .collect(Collectors.toList());

        if (!teacherNullIds.isEmpty()) {
            UpdateWrapper<ClassCourseDO> teacherUpdateWrapper = new UpdateWrapper<>();
            teacherUpdateWrapper.in("id", teacherNullIds);
            teacherUpdateWrapper.set("teacher_id", null);
            teacherUpdateWrapper.set("teacher_id_string", null);
            classCourseMapper.update(null, teacherUpdateWrapper);
        }

        if (!classroomNullIds.isEmpty()) {
            UpdateWrapper<ClassCourseDO> classroomUpdateWrapper = new UpdateWrapper<>();
            classroomUpdateWrapper.in("id", classroomNullIds);
            classroomUpdateWrapper.set("classroom_id", null);
            classCourseMapper.update(null, classroomUpdateWrapper);
        }

        //删除原本自带的教师关联信息后,新增现有的关联信息
        changeClassCourseTeacher(updateDOList);

        if(CollectionUtil.isEmpty(updateDOList)){
            return;
        }

        //批量排课时设置数据更新时间为当前时间
        updateDOList.forEach(vo -> {
            vo.setUpdateTime(LocalDateTime.now());
        });

        classCourseMapper.updateBatch(updateDOList);
        // courseId为空，即取消排课的格子，需要置其他的信息位为空，包括课程
        List<ClassCourseDO> updateNull = updateDOList.stream().filter(vo -> vo.getCourseId() == null).collect(Collectors.toList());
        List<Long> ccIds = updateNull.stream().map(ClassCourseDO::getId).collect(Collectors.toList());
        UpdateWrapper<ClassCourseDO> updateWrapper = new UpdateWrapper<>();
        if (!ccIds.isEmpty()) {
            updateWrapper.in("id", ccIds)
                    .set("course_id", null)
                    .set("teacher_id", null)
                    .set("classroom_id", null)
                    .set("is_merge", false)
                    .set("is_change", false);
            update(updateWrapper);
            //删除关联信息
            classCourseUtil.deleteClassCourseLinkInfo(ccIds);
        }

        //筛选开始、结束时间为空且未排课的格子并删除
        List<Long> deleteIds = updateReqVOList.stream()
                .filter(updateReqVO -> (updateReqVO.getBeginTime() == null || updateReqVO.getEndTime() == null) && updateReqVO.getCourseId() == null)
                .map(ClassCourseUpdateReqVO::getId)
                .collect(Collectors.toList());
        if (!deleteIds.isEmpty()) {
            //删除关联信息
            classCourseMapper.deleteBatchIds(deleteIds);
        }
        //发布课表时刷新签到表
        if (StatusEnum.PUBLISHED.getCode().equals(status)) {
        // 立即提交任务，不等待结果
        // 刷新签到表,立即提交任务，不等待结果
        CompletableFuture.runAsync(() -> clockInInfoService.generateRecords())
                .exceptionally(e -> {
                    log.error(e.getMessage(), e);
                    return null;
                });
        }

        //筛选已合班的格子，并同步更新该格子的所有变化
        List<ClassCourseUpdateReqVO> newUpdateList = new ArrayList<>();
        List<ClassCourseUpdateReqVO> mergeUpdateList = updateReqVOList.stream().filter(ClassCourseBaseVO::getIsMerge).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(mergeUpdateList)){
            // 1、创建线程池
            ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
            // 2、多线程计数器
            CountDownLatch latch = new CountDownLatch(mergeUpdateList.size());
            for(ClassCourseUpdateReqVO vo : mergeUpdateList){
                executorService.submit(() -> {
                    try {
                        ClassCourseDO classCourseDO = classCourseDOMap.get(vo.getId());
                        LambdaQueryWrapper<ClassCourseDO> classCourseDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        // 查询给定班级下是否有授课时间与原课表格子开始时间、结束时间的小时、分钟相等的格子，且打开合班了的格子
                        if(classCourseDO.getBeginTime() != null && classCourseDO.getEndTime() != null){
                            classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getDate, vo.getDate())
                                    .ne(ClassCourseDO::getClassId, classCourseDO.getClassId())
                                    .eq(ClassCourseDO::getCourseId, classCourseDO.getCourseId())
                                    .apply("to_char(begin_time, 'HH24:MI') = to_char({0}, 'HH24:MI')", classCourseDO.getBeginTime())
                                    .apply("to_char(end_time, 'HH24:MI') = to_char({0}, 'HH24:MI')", classCourseDO.getEndTime());
                        }else{
                            classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getDate, classCourseDO.getDate())
                                    .ne(ClassCourseDO::getClassId, classCourseDO.getClassId())
                                    .eq(ClassCourseDO::getPeriod, classCourseDO.getPeriod())
                                    .eq(ClassCourseDO::getCourseId, classCourseDO.getCourseId());
                        }

                        classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getIsMerge, true);
                        List<ClassCourseDO> classCourseDOList = classCourseMapper.selectList(classCourseDOLambdaQueryWrapper);
                        classCourseDOList.forEach(entity -> {
                            ClassCourseUpdateReqVO newVO = new ClassCourseUpdateReqVO();
                            // 拷贝原 VO 的所有属性
                            BeanUtils.copyProperties(vo, newVO);
                            // 替换 ID 为 entity 的 ID
                            newVO.setId(entity.getId());
                            newVO.setClassId(entity.getClassId());
                            newUpdateList.add(newVO);
                        });
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }finally {
                        latch.countDown();
                    }
                });
            }
            // 3. 主线程等待所有任务完成
            try {
                latch.await();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            // 4. 关闭线程池
            executorService.shutdown();
            //根据合并课程信息同步批量更新课程表
            if(CollectionUtil.isNotEmpty(newUpdateList)){
                updateTableBatchByMergeInfo(StatusEnum.TEMPORARY.getCode(), newUpdateList);
            }
        }
    }

    /**
     * 根据合并课程信息批量更新课程表
     * 此方法主要用于根据提供的课程状态和更新请求列表来批量更新课程表信息
     * 它处理各种场景，如过滤已发布的课程、更新课程和教师信息、处理课程取消排课等
     *
     * @param status 课程表的状态，决定是否发布课程表
     * @param updateReqVOList 包含要更新的课程信息的请求列表
     */
    public void updateTableBatchByMergeInfo(String status, List<ClassCourseUpdateReqVO> updateReqVOList) {

        // 判断联动合班变动是否会产生与已有课表格子冲突的情况
        // Step 1. 获取所有班级 ID（避免重复）
        Set<Long> classIdSet = updateReqVOList.stream()
                .map(ClassCourseUpdateReqVO::getClassId)
                .collect(Collectors.toSet());

        List<ClassManagementDO> classManagementList = classIdSet.isEmpty() ? new ArrayList<>() : classManagementMapper.selectList(
                new LambdaQueryWrapper<ClassManagementDO>()
                        .in(ClassManagementDO::getId, classIdSet));

        // 转换为 Map<classId, ClassManagementDO>
        Map<Long, ClassManagementDO> classManagementMap = classManagementList.stream()
                .collect(Collectors.toMap(ClassManagementDO::getId, Function.identity()));


        // Step 2. 一次性查询所有相关班级下的已有课表（排除已删除项和自身 id）
        List<ClassCourseDO> existingCourses = classCourseMapper.selectList(new LambdaQueryWrapper<ClassCourseDO>().in(ClassCourseDO::getClassId, classIdSet));

        // Step 3. 按班级分组，构建 Map<Long, List<ClassCourseDO>>
        Map<Long, List<ClassCourseDO>> classCourseMap = existingCourses.stream()
                .collect(Collectors.groupingBy(ClassCourseDO::getClassId));

        // Step 4. 校验每一个更新对象是否冲突
        List<String> conflictMessages = new ArrayList<>();
        for (ClassCourseUpdateReqVO updateVO : updateReqVOList) {
            Long classId = updateVO.getClassId();
            // 本次更新自己的 ID
            Long classCourseId = updateVO.getId();
            LocalDateTime newBegin = updateVO.getBeginTime();
            LocalDateTime newEnd = updateVO.getEndTime();
            if (newBegin == null || newEnd == null) {
                // 或者记录异常，空时间段跳过
                continue;
            }
            List<ClassCourseDO> currentClassCourses = classCourseMap.getOrDefault(classId, Collections.emptyList());
            for (ClassCourseDO existing : currentClassCourses) {
                // 排除自己
                if (Objects.equals(existing.getId(), classCourseId)) continue;
                LocalDateTime existBegin = existing.getBeginTime();
                LocalDateTime existEnd = existing.getEndTime();
                if (existBegin == null || existEnd == null) continue;
                boolean isConflict = newBegin.isBefore(existEnd) && newEnd.isAfter(existBegin);
                if (isConflict) {
//                    conflictMessages.add(String.format("课程ID %d 与已有课程ID %d 时间冲突", classCourseId, existing.getId()));
                    ClassManagementDO classManagementDO = classManagementMap.get(classId);
                    conflictMessages.add(classManagementDO.getClassName());
                    break;
                }
            }
        }
        // Step 5. 抛出冲突信息或处理
        if (!conflictMessages.isEmpty()) {
//            String conflictMessage = "存在课程时间冲突：" + String.join("；", conflictMessages);
//            throw new RuntimeException("存在课程时间冲突：" + String.join("；", conflictMessages));
            throw exception(CLASS_COURSE_CONFLICT_BY_TIME,String.join("；", conflictMessages));
        }


        // 批量更新
        List<ClassCourseDO> updateDOList = ClassCourseConvert.INSTANCE.convertToDoList(updateReqVOList);

//        //筛选并去掉数据库中为已发布的课表格子，不允许更新，已发布的课表格子在调课模块更新
//        if (!CollectionUtils.isEmpty(updateDOList)) {
//            ClassCourseDO classCourseDO = updateDOList.get(0);
//            LambdaQueryWrapper<ClassCourseDO> classCourseDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            classCourseDOLambdaQueryWrapper
//                    .eq(ClassCourseDO::getIsTemporary, false)
//                    .eq(ClassCourseDO::getClassId, classCourseDO.getClassId())
//                    .select(ClassCourseDO::getId);
//            // 执行查询，返回 id 列表
//            List<Long> classCourseIdList = classCourseMapper.selectList(classCourseDOLambdaQueryWrapper)
//                    .stream()
//                    .map(ClassCourseDO::getId)
//                    .collect(Collectors.toList());
//            // 去掉updateDOList中id在classCourseIdList中的数据
//            updateDOList.removeIf(vo -> classCourseIdList.contains(vo.getId()));
//        }

//        if (StatusEnum.PUBLISHED.getCode().equals(status)) {
            updateDOList.forEach(vo -> {
//                //保存并发布时首先校验是否还存在没有时间安排且排了课的空格子，若有则提示
//                if ((vo.getBeginTime() == null || vo.getEndTime() == null) && vo.getCourseId() != null) {
//                    throw exception(ELECTIVE_RELEASE_TIME_ERROR);
//                }
                if (vo.getCourseId() != null) {
//                    vo.setIsTemporary(false);
                    // 默认开启合班授课
                    vo.setIsMerge(true);
                }
            });
//        }

        //临时逻辑：将teacherIdString中的第一位老师放置于teacherId字段
        for (ClassCourseDO classCourse : updateDOList) {
            if (StringUtils.isNotEmpty(classCourse.getTeacherIdString())) {
                // 获取逗号分隔的教师ID字符串并提取第一个ID
                String[] teacherIds = classCourse.getTeacherIdString().split(",");
                // 将第一个教师ID赋值给 teacherId 字段
                if (teacherIds.length > 0) {
                    try {
                        classCourse.setTeacherId(Long.parseLong(teacherIds[0]));
                    } catch (NumberFormatException e) {
                        // 如果解析失败，处理异常
                        log.error("无法解析教师ID: {}", teacherIds[0]);
                    }
                }
            }
        }

        // 更新班级课程表中 teacherIdString 和 classroomId 字段为 null，防止保存为草稿时无法置空（如将原本的专题课换为活动课，对应的教师和班级不会置空）
        List<Long> insertIds = updateDOList.stream().filter(item -> item.getTeacherIdString() == null || item.getClassroomId() == null).map(ClassCourseDO::getId).collect(Collectors.toList());
        if (!insertIds.isEmpty()) {
            UpdateWrapper<ClassCourseDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in("id", insertIds)
                    .set("teacher_id", null)
                    .set("teacher_id_string", null)
                    .set("classroom_id", null);
            classCourseMapper.update(null, updateWrapper);
        }

        //删除原本自带的教师关联信息后,新增现有的关联信息
        changeClassCourseTeacher(updateDOList);

        if(CollectionUtil.isEmpty(updateDOList)){
            return;
        }


        //批量排课时设置数据更新时间为当前时间
        updateDOList.forEach(vo -> {
            vo.setUpdateTime(LocalDateTime.now());
        });

        classCourseMapper.updateBatch(updateDOList);
        // courseId为空，即取消排课的格子，需要置其他的信息位为空，包括课程
        List<ClassCourseDO> updateNull = updateDOList.stream().filter(vo -> vo.getCourseId() == null).collect(Collectors.toList());
        List<Long> ccIds = updateNull.stream().map(ClassCourseDO::getId).collect(Collectors.toList());
        UpdateWrapper<ClassCourseDO> updateWrapper = new UpdateWrapper<>();
        if (!ccIds.isEmpty()) {
            updateWrapper.in("id", ccIds)
                    .set("course_id", null)
                    .set("teacher_id", null)
                    .set("classroom_id", null)
                    .set("is_merge", false)
                    .set("is_change", false);
            update(updateWrapper);

            //删除关联信息
            classCourseUtil.deleteClassCourseLinkInfo(ccIds);
        }

        //筛选开始、结束时间为空且未排课的格子并删除
        List<Long> deleteIds = updateReqVOList.stream()
                .filter(updateReqVO -> (updateReqVO.getBeginTime() == null || updateReqVO.getEndTime() == null) && updateReqVO.getCourseId() == null)
                .map(ClassCourseUpdateReqVO::getId)
                .collect(Collectors.toList());
        if (!deleteIds.isEmpty()) {
            //删除关联信息
            classCourseMapper.deleteBatchIds(deleteIds);
        }

        //发布课表时刷新签到表
        if (StatusEnum.PUBLISHED.getCode().equals(status)) {
            // 立即提交任务，不等待结果
            // 刷新签到表,立即提交任务，不等待结果
            CompletableFuture.runAsync(() -> clockInInfoService.generateRecords())
                    .exceptionally(e -> {
                        log.error(e.getMessage(), e);
                        return null;
                    });
        }
    }

    /**
     * 更新班级课程教师关联信息
     * 此方法首先删除现有的班级课程教师关联信息，然后根据更新列表重新插入新的关联信息
     * 它处理两种情况：非部门教师分配和部门教师分配
     * 非部门教师分配情况下，教师ID字符串被分割成单个教师ID，并为每个ID创建关联记录
     * 部门教师分配情况下，直接使用部门ID和教师ID字符串创建单一关联记录
     *
     * @param updateDOList 包含更新信息的班级课程列表
     */
    public void changeClassCourseTeacher(List<ClassCourseDO> updateDOList) {
        //删除原本自带的教师关联信息后,新增现有的关联信息
        List<Long> insertIds = updateDOList.stream().map(ClassCourseDO::getId).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(insertIds)){
            LambdaQueryWrapper<ClassCourseTeacherDO> classCourseTeacherDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            classCourseTeacherDOLambdaQueryWrapper.in(ClassCourseTeacherDO::getClassCourseId, insertIds);
            classCourseTeacherMapper.delete(classCourseTeacherDOLambdaQueryWrapper);
        }
        List<ClassCourseTeacherDO> classCourseTeacherDOList = new ArrayList<>();
        updateDOList.forEach(updateDO -> {
            if (StringUtils.isNotEmpty(updateDO.getTeacherIdString()) && !updateDO.getDepartment()) {
                // 将teacherIdString按逗号分隔成教师ID数组
                String[] teacherIds = updateDO.getTeacherIdString().split(",");

                // 遍历teacherIds数组，为每个ID创建ClassCourseTeacherDO对象
                long sort = 0;
                for (String teacherId : teacherIds) {
                    ClassCourseTeacherDO teacherDO = new ClassCourseTeacherDO();
                    teacherDO.setClassCourseId(updateDO.getId());
                    teacherDO.setSort(sort);
                    try {
                        teacherDO.setTeacherId(Long.parseLong(teacherId));
                    } catch (NumberFormatException e) {
                        // 如果teacherId无法转换为Long，可以处理异常
                        System.err.println("无效的教师ID: " + teacherId);
                    }
                    if(teacherDO.getTeacherId() != null && teacherDO.getClassCourseId() != null){
                        classCourseTeacherDOList.add(teacherDO);
                        sort++;
                    }
                }
            } else if (StringUtils.isNotEmpty(updateDO.getTeacherIdString()) && updateDO.getDepartment()) {
                ClassCourseTeacherDO teacherDO = new ClassCourseTeacherDO();
                teacherDO.setClassCourseId(updateDO.getId());
                teacherDO.setSort(0L);
                teacherDO.setTeacherId(updateDO.getDeptId());
                teacherDO.setDeptName(updateDO.getTeacherIdString());
                classCourseTeacherDOList.add(teacherDO);
            }
        });
        classCourseTeacherMapper.insertBatch(classCourseTeacherDOList);
    }


    /**
     * 判断两个时间段是否重合
     *
     * @param beginTime1 第一个时间段的开始时间
     * @param endTime1   第一个时间段的结束时间
     * @param beginTime2 第二个时间段的开始时间
     * @param endTime2   第二个时间段的结束时间
     * @return 如果时间重合返回true，否则返回false
     */
    public static boolean isTimeOverlap(
            LocalDateTime beginTime1, LocalDateTime endTime1,
            LocalDateTime beginTime2, LocalDateTime endTime2) {

        // 检查时间参数是否有效
        if (beginTime1 == null || endTime1 == null || beginTime2 == null || endTime2 == null) {
            throw new IllegalArgumentException("时间参数不能为空");
        }
        if (beginTime1.isAfter(endTime1) || beginTime2.isAfter(endTime2)) {
            throw new IllegalArgumentException("时间段的开始时间不应晚于结束时间");
        }

        // 判断时间段是否重合
        return !(beginTime1.isAfter(endTime2) || endTime1.isBefore(beginTime2));
    }

    @Override
    public ClassTableRespVO getTimeTable(ClassTimeTableReqVO reqVO) {

//        reqVO.setTeacherId(null);

//        if(reqVO.getClassId() == null && reqVO.getTeacherId()==null){
//            throw exception(CLASS_ID_OR_TEACHER_ID_NOT_GIVE);
//        }

        if (StrUtil.isNotBlank(reqVO.getDateBeg())) {
            List<String> weekStartAndEnd = DateConvertUtil.getWeekStartAndEnd(reqVO.getDateBeg());
            reqVO.setDateBeg(weekStartAndEnd.get(0));
            reqVO.setDateEnd(weekStartAndEnd.get(1));
        }
        //ClassTimeTableInfoVO
        List<ClassTimeTableInfoVO> list = getClassTimeTableInfoList(reqVO);

        List<Long> classIds = list.stream().map(ClassTimeTableInfoVO::getClassId).collect(Collectors.toList());

        List<ClassManagementDO> classList = classManagementServiceImpl.lambdaQuery().in(ClassManagementDO::getId, classIds).list();

        Map<Long, ClassManagementDO> classMap = classList.stream()
                .collect(Collectors.toMap(ClassManagementDO::getId, classDO -> classDO));

        Map<String, List<ClassTimeTableInfoVO>> groupedByDate = list.stream()
                .collect(Collectors.groupingBy(
                        ClassTimeTableInfoVO::getDate,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                courseList -> courseList.stream()
                                        .sorted(Comparator.comparing(ClassTimeTableInfoVO::getBeginTime, Comparator.nullsLast(Comparator.naturalOrder())))
                                        .collect(Collectors.toList()))));

        List<ClassTimeTableRespVO> respList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(FORMAT_HOUR_MINUTE);

        List<Integer> classRoomIds = list.stream().map(ClassTimeTableInfoVO::getClassroomId).collect(Collectors.toList());

        List<ClassroomLibraryDO> classroomLibraryList = classroomLibraryService.listByIds(classRoomIds);
        Map<Integer, ClassroomLibraryDO> classroomLibraryMap = classroomLibraryList.stream()
                .collect(Collectors.toMap(ClassroomLibraryDO::getId, cmDO -> cmDO));


        ConcurrentHashMap<YearMothBean, Set<String>> forcache = new ConcurrentHashMap<>();


        // 获取指定排课id列表的合班排课分组情况 分组内的排课id数量必大于1
        List<ClassMergeCourseGroupRespDTO> classMergeCourseGroupList = classCourseMapper.getClassMergeCourseGroup(list.stream()
                .map(ClassTimeTableInfoVO::getId)
                .collect(Collectors.toList()));
        // 所有有合班授课的排课id->排课信息 Map
        Map<Long, ClassMergeCourseGroupRespDTO> classCourseIdToClassInfoMap = classMergeCourseGroupList.stream()
                .collect(Collectors.toMap(ClassMergeCourseGroupRespDTO::getId, o -> o, (a, b) -> a));
        // 标记合班授课分组Map
        Map<Integer, List<ClassMergeCourseGroupRespDTO>> classMergeCourseGroupMap = classMergeCourseGroupList.stream()
                .collect(Collectors.groupingBy(ClassMergeCourseGroupRespDTO::getGroupNum));


        groupedByDate.forEach((date, courses) -> {


            ClassTimeTableRespVO respVO = new ClassTimeTableRespVO();

            //班级id带出，用于全校课表
            if(!courses.isEmpty()){
                ClassTimeTableInfoVO classTimeTableInfoVO = courses.get(0);
                Long classId = classTimeTableInfoVO.getClassId();
                respVO.setClassId(classId);
                ClassManagementDO classManagementDO = classMap.get(classId);
                if(classManagementDO!=null){
                    respVO.setClassName(classManagementDO.getClassName());
                }
            }

            int year = LocalDate.parse(date).getYear();
            int month = LocalDate.parse(date).getMonthValue();


            respVO.setDate(date);

            YearMothBean yearMothBean = new YearMothBean(year, month);

            Set<String> stringSet = forcache.get(yearMothBean);
            if (CollectionUtil.isEmpty(stringSet)) {
                stringSet = HolidayUtils.JJR(year, month, holidayMapper);
                forcache.put(yearMothBean, stringSet);
            }

            respVO.setHoliday(stringSet.contains(date));


            List<ClassTimeTableRespVO.CourseVO> morning = new ArrayList<>();
            List<ClassTimeTableRespVO.CourseVO> afternoon = new ArrayList<>();


            List<ClassTimeTableRespVO.CourseVO> evening = new ArrayList<>();
            courses.forEach(course -> {
                ClassTimeTableRespVO.CourseVO courseVO = new ClassTimeTableRespVO.CourseVO();
                courseVO.setTeacherName(course.getTeacherName());
                courseVO.setCourseTitle(course.getCourseName());
                courseVO.setClassId(course.getClassId());
                courseVO.setIsDepartmentLeader(course.getIsDepartmentLeader());
                courseVO.setIsChange(course.getIsChange());
                courseVO.setIsLeaderLecture(course.getIsLeaderLecture());
                courseVO.setId(course.getId());
                if(course.getBeginTime()!=null){
                    courseVO.setBeginTime(DateUtils.format(course.getBeginTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                if(course.getEndTime()!=null){
                    courseVO.setEndTime(DateUtils.format(course.getEndTime(),"yyyy-MM-dd HH:mm:ss"));
                }

                try{
                    ClassManagementDO classManagementDO = classMap.get(course.getClassId());
                    if(classManagementDO!=null){
                        courseVO.setClassName(classManagementDO.getClassName());
                    }
                }catch(Exception e){
                    log.error(e.getMessage(),e);
                }

                if (course.getBeginTime() != null) {
                    String timeRange = course.getBeginTime().format(formatter) +
                            "-" +
                            course.getEndTime().format(formatter);

                    courseVO.setTime(timeRange);

                    courseVO.setCourseId(course.getId());

                    courseVO.setOpenCheck(course.getIsCheck());
                }
                courseVO.setTeachers(course.getTeacherIdString());
                courseVO.setCoach(course.getTeacherName());
                courseVO.setClassroom(course.getClassroom());
                courseVO.setCourseType(course.getCourseTypeName());
                // 是否合班授课
                courseVO.setIsMerge(course.getIsMerge());
                // 排课是否在合班授课分组中 （分组中的合班授课数量都大于2 ）
                courseVO.setIsShowMergeTag(classCourseIdToClassInfoMap.containsKey(course.getId()));
                if (Boolean.TRUE.equals(course.getIsMerge()) && Boolean.TRUE.equals(courseVO.getIsShowMergeTag())){
                    // 设置合班授课
                    List<ClassMergeCourseGroupRespDTO> classMergeCourseGroupRespDTOS = classMergeCourseGroupMap.get(classCourseIdToClassInfoMap
                            .get(courseVO.getId()).getGroupNum());
                    if (CollUtil.isNotEmpty(classMergeCourseGroupRespDTOS)) {
                        List<ClassInfoRespVO> mergedClasses = classMergeCourseGroupRespDTOS.stream()
                                .filter(o -> !o.getId().equals(courseVO.getId()))
                                .map(o -> {
                                    ClassInfoRespVO classInfoRespVO = new ClassInfoRespVO();
                                    classInfoRespVO.setName(o.getClassName());
                                    classInfoRespVO.setId(o.getClassId());
                                    return classInfoRespVO;
                                }).collect(Collectors.toList());

                        courseVO.setMergedClass(mergedClasses);
                    }
                }
                if (course.getPeriod() != null) {
                    if (course.getPeriod().equals(CoursesPeriodEnum.MORNING.getType())) {
                        morning.add(courseVO);
                    } else if (course.getPeriod().equals(CoursesPeriodEnum.AFTERNOON.getType())) {
                        afternoon.add(courseVO);
                    } else {
                        evening.add(courseVO);
                    }
                }

            });


            List<ClassTimeTableRespVO.DateCourseVO> dateCourseList = new ArrayList<>();

            ClassTimeTableRespVO.DateCourseVO morningCourse = new ClassTimeTableRespVO.DateCourseVO();
            morningCourse.setType(CoursesPeriodEnum.MORNING.getDesc());
            morningCourse.setTimeCourse(morning);
            dateCourseList.add(morningCourse);

            ClassTimeTableRespVO.DateCourseVO afternoonCourse = new ClassTimeTableRespVO.DateCourseVO();
            afternoonCourse.setType(CoursesPeriodEnum.AFTERNOON.getDesc());
            afternoonCourse.setTimeCourse(afternoon);
            dateCourseList.add(afternoonCourse);

            ClassTimeTableRespVO.DateCourseVO eveningCourse = new ClassTimeTableRespVO.DateCourseVO();
            eveningCourse.setType(CoursesPeriodEnum.EVENING.getDesc());
            eveningCourse.setTimeCourse(evening);
            dateCourseList.add(eveningCourse);

            respVO.setDateCourse(dateCourseList);

            respList.add(respVO);

        });
        ClassTableRespVO respVO = new ClassTableRespVO();

        respList.sort(Comparator.comparing(ClassTimeTableRespVO::getDate));
        respVO.setCourse(respList);


        ClassManagementDO classCourse = classManagementMapper.selectById(reqVO.getClassId());
        if(classCourse!=null){
            LocalDateTime classOpenTime = classCourse.getClassOpenTime();
            LocalDateTime classCompletionTime = classCourse.getCompletionTime();
            String courseTableName = classCourse.getClassName() + "课表";
            respVO.setCourseTableName(courseTableName);

            DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String openTime = classOpenTime.format(formatter1);
            String completionTime = classCompletionTime.format(formatter1);
            String courseDate = openTime + "至" + completionTime;
            respVO.setCourseTableDate(courseDate);
        }
        return respVO;
    }

    private List<ClassTimeTableInfoVO> getClassTimeTableInfoList(ClassTimeTableReqVO reqVO) {
        List<ClassTimeTableInfoVO> list = baseMapper.getTimeTable(reqVO);

        // 生成完整的日期范围
        List<LocalDate> completeDateRange = Stream.iterate(
                        LocalDate.parse(reqVO.getDateBeg()),
                        date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(LocalDate.parse(reqVO.getDateBeg()), LocalDate.parse(reqVO.getDateEnd())) + 1)
                .collect(Collectors.toList());

        // 将已有数据的日期收集到一个 Set 中
        Set<LocalDate> existingDates = list.stream()
                .map(info -> LocalDate.parse(info.getDate()))
                .collect(Collectors.toSet());

        // 遍历完整日期范围，添加缺失日期的数据
        for (LocalDate date : completeDateRange) {
            if (!existingDates.contains(date)) {
                // 如果日期缺失，则创建一个新的 ClassTimeTableInfoVO 并添加到 list
                ClassTimeTableInfoVO missingInfo = new ClassTimeTableInfoVO();
                missingInfo.setDate(date.toString());
                // 根据需求设置其他默认值
                list.add(missingInfo);
            }
        }

        // 获取课程分类 教学形式 业中同步过来的字典数据
        List<DictDataRespDTO> dictData = dictDataApi.getByDictTypes(Arrays.asList(CoursesDictEnum.THEME.getType(), CoursesDictEnum.OPTIONAL_THEME.getType(),
                CoursesDictEnum.EDUCATE_FORM.getType())).getCheckedData();
        // 字典数据列表转树节点用于计算
        List<TreeNodeDTO> treeNodeDTOList = coursesServiceImpl.packageDictDataAsTreeNodeDTOList(dictData);
        // 课程分类、教学形式 字典id->完整路径表示ID
        Map<Long, String> map = TreeDataUtil.generateNodeIdToFullPathMap(treeNodeDTOList);

        //给teacherName复制
        assignTeacherName(list);

        // 处理courseType
        for (ClassTimeTableInfoVO vo : list) {

            if (Objects.equals(vo.getCourseId(), -1L)) {
                vo.setCoursesType(CoursesTypeEnum.OPTIONAL_COURSE.getType());
            }
            if (Objects.equals(vo.getCoursesType(), CoursesTypeEnum.TOPIC_COURSE.getType())) {
                vo.setCourseTypeName(map.get(vo.getEducateFormId()));
            } else if (Objects.equals(vo.getCoursesType(), CoursesTypeEnum.TEACHING_ACTIVITY.getType())) {

                if (Objects.isNull(vo.getActivityType())) {
                    vo.setCourseTypeName("教学活动");
                } else if (Objects.equals(vo.getActivityType(), 0)) {
                    vo.setCourseTypeName("常用活动");
                } else if (Objects.equals(vo.getActivityType(), 1)) {
                    vo.setCourseTypeName("其它活动");
                }
            } else if (Objects.equals(vo.getCoursesType(), CoursesTypeEnum.OPTIONAL_COURSE.getType())) {
                vo.setCourseName("选修课");
                vo.setTeacherName("");
                vo.setClassroom("");
            }

        }

        // 最终按日期排序
        list.sort(Comparator.comparing(ClassTimeTableInfoVO::getDate));
        return list;
    }
    private void assignMySchuleTeacherName(List<MyClassScheduleVO> list) {

        if (CollUtil.isEmpty(list)) {
            return;
        }

        // 只处理非部门的记录的教师ID
        List<String> teacherIdStrings = list.stream()
                .filter(vo -> StringUtils.isNotBlank(vo.getTeacherIdString()))
                .filter(vo -> !vo.getDepartment())
                .map(MyClassScheduleVO::getTeacherIdString)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(teacherIdStrings)) {
            // 将部门记录的teacherIdString直接设置为teacherName
            list.stream()
                    .filter(vo -> vo != null && vo.getDepartment() != null)
                    .forEach(vo -> {
                        vo.setTeacherName(vo.getTeacherIdString() == null ? "" : vo.getTeacherIdString());
                    });
            return;
        }

        // 解析教师ID并获取教师信息
        List<Long> teacherIds = teacherIdStrings.stream()
                .flatMap(ids -> Arrays.stream(ids.split(",")))
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .map(id -> {
                    try {
                        return Long.parseLong(id);
                    } catch (NumberFormatException e) {
                        log.warn("无效的 teacher ID 格式: {}", id);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(teacherIds)) {
            return;
        }

        // 获取教师信息映射
        Map<Long, TeacherInformationDO> teacherMap = teacherInformationService.listByIds(teacherIds)
                .stream()
                .collect(Collectors.toMap(
                        TeacherInformationDO::getId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        // 处理教师名称赋值
        list.forEach(vo -> {
            if (vo.getDepartment()!=null &&vo.getDepartment()) {
                vo.setTeacherName(vo.getTeacherIdString());
            } else if (StringUtils.isNotBlank(vo.getTeacherIdString())) {
                String teacherNames = Arrays.stream(vo.getTeacherIdString().split(","))
                        .filter(StringUtils::isNotBlank)
                        .map(String::trim)
                        .map(id -> {
                            try {
                                Long teacherId = Long.parseLong(id);
                                TeacherInformationDO teacher = teacherMap.get(teacherId);
                                return teacher != null ? teacher.getName() : null;
                            } catch (NumberFormatException e) {
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(","));

                if (StringUtils.isNotBlank(teacherNames)) {
                    vo.setTeacherName(teacherNames);
                }
            }
        });


    }
    private void assignTeacherName(List<ClassTimeTableInfoVO> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }

        // 只处理非部门的记录的教师ID
        List<String> teacherIdStrings = list.stream()
                .filter(vo -> StringUtils.isNotBlank(vo.getTeacherIdString()))
                .filter(vo -> !vo.getDepartment())
                .map(ClassTimeTableInfoVO::getTeacherIdString)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(teacherIdStrings)) {
            // 将部门记录的teacherIdString直接设置为teacherName
            list.stream()
                    .filter(vo -> vo != null && vo.getDepartment() != null)
                    .forEach(vo -> {
                        vo.setTeacherName(vo.getTeacherIdString() == null ? "" : vo.getTeacherIdString());
                    });
            return;
        }

        // 解析教师ID并获取教师信息
        List<Long> teacherIds = teacherIdStrings.stream()
                .flatMap(ids -> Arrays.stream(ids.split(",")))
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .map(id -> {
                    try {
                        return Long.parseLong(id);
                    } catch (NumberFormatException e) {
                        log.warn("无效的 teacher ID 格式: {}", id);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(teacherIds)) {
            return;
        }

        // 获取教师信息映射
        Map<Long, TeacherInformationDO> teacherMap = teacherInformationService.listByIds(teacherIds)
                .stream()
                .collect(Collectors.toMap(
                        TeacherInformationDO::getId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        // 处理教师名称赋值
        list.forEach(vo -> {
            if (vo.getDepartment()!=null &&vo.getDepartment()) {
                vo.setTeacherName(vo.getTeacherIdString());
            } else if (StringUtils.isNotBlank(vo.getTeacherIdString())) {
                String teacherNames = Arrays.stream(vo.getTeacherIdString().split(","))
                        .filter(StringUtils::isNotBlank)
                        .map(String::trim)
                        .map(id -> {
                            try {
                                Long teacherId = Long.parseLong(id);
                                TeacherInformationDO teacher = teacherMap.get(teacherId);
                                return teacher != null ? teacher.getName() : null;
                            } catch (NumberFormatException e) {
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(","));

                if (StringUtils.isNotBlank(teacherNames)) {
                    vo.setTeacherName(teacherNames);
                }
            }
        });
    }

    @Override
    public Workbook exportTimeTable(ClassTimeTableReqVO reqVO, List<ClassTimeTableRespVO> list, HttpServletResponse response) {

        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("课表");


        // 示例数据结构：每一天包含上午和下午的课程
        LinkedHashMap<String, Map<String, List<String>>> scheduleData = getScheduleData(list);

        // 创建一个带有换行属性的样式
        CellStyle wrapTextStyle1 = workbook.createCellStyle();
        wrapTextStyle1.setWrapText(true);
        // 水平居中
        wrapTextStyle1.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        wrapTextStyle1.setVerticalAlignment(VerticalAlignment.CENTER);

        // 写入表头：日期
        int rowNum = 0;
        // 日期行
        Row dateRow = sheet.createRow(rowNum);
        int colNum = 1;
        for (String day : scheduleData.keySet()) {
            Cell dateCell = dateRow.createCell(colNum);
            String dayNum = "星期" + NumberConverter.arabicToChinese(colNum);
            dayNum = dayNum.replace("七", "日 ");
            String cellValue = dayNum + "\n" + day;
            dateCell.setCellValue(cellValue);
            dateCell.setCellStyle(wrapTextStyle1);
            colNum++;
        }

        // 创建一个带有换行属性的样式
        CellStyle wrapTextStyle = workbook.createCellStyle();
        wrapTextStyle.setWrapText(true);
        // 水平左对齐
        wrapTextStyle.setAlignment(HorizontalAlignment.LEFT);
        // 垂直上对齐
        wrapTextStyle.setVerticalAlignment(VerticalAlignment.TOP);

        int max1 = 1;
        int max2 = 1;
        int max3 = 1;
        for (Map.Entry<String, Map<String, List<String>>> dayEntry : scheduleData.entrySet()) {

            Map<String, List<String>> sessions = dayEntry.getValue();
            List<String> morningClasses = sessions.get("morning");
            List<String> afternoonClasses = sessions.get("afternoon");
            List<String> eveningClasses = sessions.get("evening");

            if (morningClasses != null && morningClasses.size() > max1) {
                max1 = morningClasses.size();
            }
            if (afternoonClasses != null && afternoonClasses.size() > max2) {
                max2 = afternoonClasses.size();
            }
            if (eveningClasses != null && eveningClasses.size() > max3) {
                max3 = eveningClasses.size();
            }

        }

        if (sheet.getRow(1) == null) {
            Row morningLabelRow = sheet.createRow(1);
            Cell morningLabelCell = morningLabelRow.createCell(0);
            morningLabelCell.setCellValue("上午");
            morningLabelRow.setHeightInPoints(50);
            morningLabelCell.setCellStyle(wrapTextStyle1);
        }

        int rowNum1;
        rowNum1 = max1 + 1;

        if (sheet.getRow(rowNum1) == null) {
            Row afternoonLabelRow = sheet.createRow(rowNum1);
            Cell afternoonLabelCell = afternoonLabelRow.createCell(0);
            afternoonLabelCell.setCellValue("下午");
            afternoonLabelRow.setHeightInPoints(50);
            afternoonLabelCell.setCellStyle(wrapTextStyle1);
        }

        int rowNum2;
        rowNum2 = max1 + max2 + 1;

        if (sheet.getRow(rowNum2) == null) {
            Row eveningLabelRow = sheet.createRow(rowNum2);
            Cell eveningLabelCell = eveningLabelRow.createCell(0);
            eveningLabelCell.setCellValue("晚上");
            eveningLabelRow.setHeightInPoints(50);
            eveningLabelCell.setCellStyle(wrapTextStyle1);
        }

        if (max1 > 1) {
            sheet.addMergedRegion(new CellRangeAddress(1, max1, 0, 0)); // max1 应该是结束行
        }
        if (max2 > 1) {
            sheet.addMergedRegion(new CellRangeAddress(max1 + 1, max1 + max2, 0, 0)); // max2 应该是结束行
        }
        if (max3 > 1) {
            sheet.addMergedRegion(new CellRangeAddress(max1 + max2 + 1, max1 + max2 + max3, 0, 0)); // max3 应该是结束行
        }

        // 填充每一天的上午和下午课程内容
        colNum = 1;
        for (Map.Entry<String, Map<String, List<String>>> dayEntry : scheduleData.entrySet()) {
            // 获取上午和下午课程
            Map<String, List<String>> sessions = dayEntry.getValue();
            List<String> morningClasses = sessions.get("morning");
            List<String> afternoonClasses = sessions.get("afternoon");
            List<String> eveningClasses = sessions.get("evening");

            // 写入上午课程内容
            int morning = 1;
            if (morningClasses != null && !morningClasses.isEmpty()) {
                int morningRowNum = 1;

                for (String classInfo : morningClasses) {
                    Row morningRow = sheet.getRow(morningRowNum) != null ? sheet.getRow(morningRowNum) : sheet.createRow(morningRowNum);
                    Cell morningContentCell = morningRow.getCell(colNum) != null ? morningRow.getCell(colNum) : morningRow.createCell(colNum);

                    // 设置行高（单位为点，1 点大约等于 1/20 厘米）
                    morningRow.setHeightInPoints(50); // 设置为 20 点，可以根据需要调整

                    morningContentCell.setCellValue(classInfo);
                    morningContentCell.setCellStyle(wrapTextStyle);
                    morningRowNum++;
                }

                if (morningRowNum == 1) {
                    morningRowNum++;
                }
                morning = morningRowNum;
            } else {
                morning++;
            }
            if (morning - 1 < max1) {
                // 合并列单元格
                sheet.addMergedRegion(new CellRangeAddress(morning - 1, max1, colNum, colNum));
            }

            // 写入下午课程内容
            int afternoon = max1 + 1;
            if (afternoonClasses != null && !afternoonClasses.isEmpty()) {
                int afternoonRowNum = max1 + 1;

                for (String classInfo : afternoonClasses) {
                    Row afternoonRow = sheet.getRow(afternoonRowNum) != null ? sheet.getRow(afternoonRowNum) : sheet.createRow(afternoonRowNum);
                    Cell afternoonContentCell = afternoonRow.getCell(colNum) != null ? afternoonRow.getCell(colNum) : afternoonRow.createCell(colNum);

                    afternoonRow.setHeightInPoints(50);
                    afternoonContentCell.setCellValue(classInfo);

                    afternoonContentCell.setCellStyle(wrapTextStyle);
                    afternoonRowNum++;
                }

                if (afternoonRowNum == max1 + 1) {
                    afternoonRowNum++;
                }
                afternoon = afternoonRowNum;


            } else {
                afternoon++;
            }

            if (afternoon - 1 < max1 + max2) {
                // 合并列单元格
                sheet.addMergedRegion(new CellRangeAddress(afternoon - 1, max1 + max2, colNum, colNum));
            }


            // 写入晚上课程内容
            int evening = max1 + max2 + 1;
            if (eveningClasses != null && !eveningClasses.isEmpty()) {
                int eveningRowNum = max1 + max2 + 1;

                for (String classInfo : eveningClasses) {
                    Row eveningRow = sheet.getRow(eveningRowNum) != null ? sheet.getRow(eveningRowNum) : sheet.createRow(eveningRowNum);
                    Cell eveningContentCell = eveningRow.getCell(colNum) != null ? eveningRow.getCell(colNum) : eveningRow.createCell(colNum);

                    eveningRow.setHeightInPoints(50);
                    eveningContentCell.setCellValue(classInfo);

                    eveningContentCell.setCellStyle(wrapTextStyle);
                    eveningRowNum++;
                }


                if (eveningRowNum == max1 + max2 + 1) {
                    eveningRowNum++;
                }

                evening = eveningRowNum;

            } else {
                evening++;
            }

            if (evening - 1 < max1 + max2 + max3) {
                // 合并列单元格
                sheet.addMergedRegion(new CellRangeAddress(evening - 1, max1 + max2 + max3, colNum, colNum));
            }

            colNum++;
        }

        // 调整列宽
        sheet.setColumnWidth(0, 10 * 256);
        for (int i = 1; i < colNum; i++) {
            sheet.autoSizeColumn(i);
//            sheet.setColumnWidth(colNum, 100 * 256);
        }

//        sheet.setColumnWidth(colNum, 140 * 256); // 设置为 30 字符宽度，可以根据需要调整


        return workbook;
    }

    @Override
    public List<ScheduleChartVO> getClassLeaderSchedule(MyClassScheduleParamsVO reqVO) {


        if (StrUtil.isNotBlank(reqVO.getStartTime())) {
            reqVO.setStartTime(reqVO.getStartTime() + " 00:00:00");
        }
        if (StrUtil.isNotBlank(reqVO.getEndTime())) {
            reqVO.setEndTime(reqVO.getEndTime() + " 23:59:59");
        }

        List<MyClassScheduleVO> list = classCourseMapper.getMyClassSchedule(reqVO);

        //给teacherName复制
        assignMySchuleTeacherName(list);

        // 生成完整的日期范围
        String startDateStr = reqVO.getStartTime().split(" ")[0]; // 只保留日期部分
        String endDateStr = reqVO.getEndTime().split(" ")[0]; // 只保留日期部分
        // 生成完整的日期范围
        List<LocalDate> completeDateRange = Stream.iterate(
                        LocalDate.parse(startDateStr),
                        date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(LocalDate.parse(startDateStr), LocalDate.parse(endDateStr)) + 1)
                .collect(Collectors.toList());

        // 将已有数据的日期收集到一个 Set 中
        Set<LocalDate> existingDates = list.stream()
                .map(info -> LocalDate.parse(info.getDate()))
                .collect(Collectors.toSet());

        // 遍历完整日期范围，添加缺失日期的数据
        for (LocalDate date : completeDateRange) {
            if (!existingDates.contains(date)) {
                // 如果日期缺失，则创建一个新的 ClassTimeTableInfoVO 并添加到 list
                MyClassScheduleVO missingInfo = new MyClassScheduleVO();
                missingInfo.setCoursesType(9999L);
                missingInfo.setDate(date.toString());
                // 根据需求设置其他默认值
                list.add(missingInfo);
            }
        }


        Map<String, List<MyClassScheduleVO>> map = list.stream().collect(Collectors.groupingBy(MyClassScheduleVO::getDate));

        // 获取指定排课id列表的合班排课分组情况 分组内的排课id数量必大于1
        List<ClassMergeCourseGroupRespDTO> classMergeCourseGroupList = classCourseMapper.getClassMergeCourseGroup(list.stream()
                .map(MyClassScheduleVO::getClassCourseId)
                .collect(Collectors.toList()));
        // 所有有合班授课的排课id->排课信息 Map
        Map<Long, ClassMergeCourseGroupRespDTO> classCourseIdToClassInfoMap = classMergeCourseGroupList.stream()
                .collect(Collectors.toMap(ClassMergeCourseGroupRespDTO::getId, o -> o, (a, b) -> a));
        // 标记合班授课分组Map
        Map<Integer, List<ClassMergeCourseGroupRespDTO>> classMergeCourseGroupMap = classMergeCourseGroupList.stream().collect(Collectors.groupingBy(ClassMergeCourseGroupRespDTO::getGroupNum));


        for (Map.Entry<String, List<MyClassScheduleVO>> entry : map.entrySet()) {
            String key = entry.getKey();
            List<MyClassScheduleVO> value = entry.getValue();
            for (MyClassScheduleVO item : value) {
                if (Objects.equals(item.getCourseId(), -1L)) {
                    item.setCourseName("选修课");
                    item.setCoursesType(2L);
                }

                List<ClassInfoRespVO> mergedClasses  = new ArrayList<>();
                // 排课是否在合班授课分组中 （分组中的合班授课数量都大于2 ）
                item.setIsShowMergeTag(classCourseIdToClassInfoMap.containsKey(item.getClassCourseId()));
                if (Boolean.TRUE.equals(item.getIsMerge()) && Boolean.TRUE.equals(item.getIsShowMergeTag())) {
                    // 设置合班授课
                    List<ClassMergeCourseGroupRespDTO> classMergeCourseGroupRespDTOS = classMergeCourseGroupMap.get(classCourseIdToClassInfoMap
                            .get(item.getClassCourseId()).getGroupNum());
                    if (CollUtil.isNotEmpty(classMergeCourseGroupRespDTOS)) {
                        mergedClasses = classMergeCourseGroupRespDTOS.stream()
                                .filter(o -> !o.getId().equals(item.getClassCourseId()))
                                .map(o -> {
                                    ClassInfoRespVO classInfoRespVO = new ClassInfoRespVO();
                                    classInfoRespVO.setName(o.getClassName());
                                    classInfoRespVO.setId(o.getClassId());
                                    return classInfoRespVO;
                                }).collect(Collectors.toList());
                    }
                }
                item.setMergedClass(mergedClasses);
            }
        }

        List<ScheduleChartVO> scheduleList = new ArrayList<>();

        // 遍历map
        map.forEach((key, value) -> {
            ScheduleChartVO chart = new ScheduleChartVO();
            chart.setDate(key);
            if (value.stream().anyMatch(item -> Objects.equals(item.getCoursesType(), 9999L))) {
                chart.setSchedules(new ArrayList<>());
            } else {
                value.sort(Comparator.comparing(MyClassScheduleVO::getPeriod, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(MyClassScheduleVO::getBeginTime, Comparator.naturalOrder()));
                chart.setSchedules(value);
            }
            scheduleList.add(chart);
        });


        scheduleList.sort(Comparator.comparing(ScheduleChartVO::getDate));
        return scheduleList;
    }


    // 自动换行的单元格样式
    private static CellStyle wrapTextCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setWrapText(true);
        return style;
    }

    // 示例课程数据
    private static LinkedHashMap<String, Map<String, List<String>>> getScheduleData(List<ClassTimeTableRespVO> list) {
        LinkedHashMap<String, Map<String, List<String>>> scheduleData = new LinkedHashMap<>();


        for (ClassTimeTableRespVO respVO : list) {
            Map<String, List<String>> daySessions = new HashMap<>();
            List<ClassTimeTableRespVO.DateCourseVO> course = respVO.getDateCourse();

            List<String> morning = new ArrayList<>();
            List<String> afternoon = new ArrayList<>();
            List<String> evening = new ArrayList<>();

            for (ClassTimeTableRespVO.DateCourseVO courseVO : course) {
                List<ClassTimeTableRespVO.CourseVO> timeCourse = courseVO.getTimeCourse();
                for (ClassTimeTableRespVO.CourseVO courseVO1 : timeCourse) {
//                    String courseInfo = courseVO1.getCourseTitle() + " \n" + courseVO1.getTime() + " \n" + courseVO1.getCoach() + " \n" + courseVO1.getClassroom();
                    if (Objects.isNull(courseVO1.getCourseType())) {
                        courseVO1.setCourseType("");
                    }
                    if (Objects.isNull(courseVO1.getTime())) {
                        courseVO1.setTime("");
                    }
                    if (Objects.isNull(courseVO1.getCourseTitle())) {
                        courseVO1.setCourseTitle("");
                    }
                    if (Objects.isNull(courseVO1.getCoach())) {
                        courseVO1.setCoach("");
                    }
                    if (Objects.isNull(courseVO1.getClassroom())) {
                        courseVO1.setClassroom("");
                    }
                    String courseInfo = courseVO1.getTime() + " \n" + courseVO1.getCourseType() + " \n" + "课程名：" + courseVO1.getCourseTitle() + " \n" + "讲课人：" + courseVO1.getCoach() + " \n" + "上课地点：" + courseVO1.getClassroom();

                    if (courseVO.getType().equals("上午")) {
                        morning.add(courseInfo);
                    } else if (courseVO.getType().equals("下午")) {
                        afternoon.add(courseInfo);
                    } else {
                        evening.add(courseInfo);
                    }
                }
            }
            daySessions.put("morning", morning);
            daySessions.put("afternoon", afternoon);
            daySessions.put("evening", evening);
            scheduleData.put(respVO.getDate(), daySessions);
        }

        return scheduleData;
    }


    /**
     * 创建表头样式
     *
     * @param wb
     * @return
     */
    private static XSSFCellStyle createHeadCellStyle(XSSFWorkbook wb) {
        XSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setWrapText(true);// 设置自动换行
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());// 背景颜色
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直对齐
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setBottomBorderColor(IndexedColors.BLACK.index);
        cellStyle.setBorderBottom(BorderStyle.THIN); // 下边框
        cellStyle.setBorderLeft(BorderStyle.THIN); // 左边框
        cellStyle.setBorderRight(BorderStyle.THIN); // 右边框
        cellStyle.setBorderTop(BorderStyle.THIN); // 上边框


        XSSFFont headerFont = (XSSFFont) wb.createFont(); // 创建字体样式
        headerFont.setBold(true); // 字体加粗
        headerFont.setFontName("黑体"); // 设置字体类型
        headerFont.setFontHeightInPoints((short) 12); // 设置字体大小
        cellStyle.setFont(headerFont); // 为标题样式设置字体样式

        return cellStyle;
    }

    /**
     * 创建内容样式
     *
     * @param wb
     * @return
     */
    private static XSSFCellStyle createContentCellStyle(XSSFWorkbook wb) {
        XSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);// 水平居中
        cellStyle.setWrapText(true);// 设置自动换行
        cellStyle.setBorderBottom(BorderStyle.THIN); // 下边框
        cellStyle.setBorderLeft(BorderStyle.THIN); // 左边框
        cellStyle.setBorderRight(BorderStyle.THIN); // 右边框
        cellStyle.setBorderTop(BorderStyle.THIN); // 上边框

        // 生成12号字体
        XSSFFont font = wb.createFont();
        font.setColor((short) 8);
        font.setFontHeightInPoints((short) 12);
        cellStyle.setFont(font);

        return cellStyle;
    }

    private void setRegin(XSSFSheet sheet, CellRangeAddress region) {
        sheet.addMergedRegion(region);
        RegionUtil.setBorderTop(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderBottom(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, region, sheet);
    }

    @Override
    public List<StatVO> getStat(ClassCoursePageReqVO pageVO) {

        PageResult<ClassCourseDO> classCoursePage = this.getClassCoursePage(pageVO);
        if (classCoursePage == null) {
            return null;
        }
        List<ClassCourseDO> list = classCoursePage.getList();
        // 占比基数
        Long total = classCoursePage.getTotal();
        // 只需要统计专题课，筛选出专题课的类型
        List<ClassCourseDO> classCourseList = list.stream()
                .filter(vo -> Objects.nonNull(vo.getCourseTypeId()))
                .filter(vo -> CoursesTypeEnum.TOPIC_COURSE.getType().equals(Integer.parseInt((vo.getCourseTypeId()))))
                .collect(Collectors.toList());
        List<Long> courseIds = classCourseList.stream().map(ClassCourseDO::getCourseId).collect(Collectors.toList());
        Map<Long, CoursesRespVO> coursesInfoMap = getCoursesInfoMap(courseIds);
        // 专题课类型名称补充
        for (ClassCourseDO classCourseDO : classCourseList) {
            CoursesRespVO coursesRespVO = coursesInfoMap.get(classCourseDO.getCourseId());
            if (coursesRespVO != null) {
                classCourseDO.setEducateFormId(coursesRespVO.getEducateFormId());
                classCourseDO.setEducateFormName(coursesRespVO.getEducateForm());
            }
        }

        // 统计每种教学形式的数量
        Map<String, Long> countMap = list.stream()
                .filter(vo -> vo.getEducateFormName() != null)
                .collect(Collectors.groupingBy(ClassCourseDO::getEducateFormName, Collectors.counting()));
        // 构建 StatVO 列表
        List<StatVO> statVOList = new ArrayList<>();
        for (Map.Entry<String, Long> entry : countMap.entrySet()) {
            String educateFormName = entry.getKey();
            Long count = entry.getValue();

            // 计算占比并转换为字符串形式
            String percent = String.format("%.2f", (double) count / total * 100);

            // 创建 StatVO 对象并添加到列表
            StatVO statVO = new StatVO();
            statVO.setTypeName(educateFormName);
            statVO.setPercent(percent);
            statVOList.add(statVO);
        }

        // 按照占比大小由大到小排序
        statVOList.sort((vo1, vo2) -> Double.compare(Double.parseDouble(vo2.getPercent().substring(0, vo2.getPercent().length() - 1)), Double.parseDouble(vo1.getPercent().substring(0, vo1.getPercent().length() - 1))));

        return statVOList;
    }

    @Override
    public void updateClassCourseByClockingIn(ClassCourseClockingInVO clockingInVO) {
        classCourseMapper.updateClassCourseByClockingIn(clockingInVO);
        //修改课程考勤开关后，刷新签到表
        // 刷新签到表,立即提交任务，不等待结果
        CompletableFuture.runAsync(() -> clockInInfoService.generateRecords())
                .exceptionally(e -> {
                    log.error(e.getMessage(), e);
                    return null;
                });
    }

    // 获取课程信息，包括教学形式、课程分类的字典值
    public Map<Long, CoursesRespVO> getCoursesInfoMap(List<Long> courseIds) {
        if (courseIds.size() == 0) {
            return new HashMap<>();
        }
        List<CoursesDO> coursesDOList = coursesService.getCoursesList(courseIds);
        List<CoursesRespVO> coursesRespVOList = CoursesConvert.INSTANCE.convertList(coursesDOList);
        // 获取字典类型拼接
        // 获取课程分类 教学形式 业中同步过来的字典数据
        List<DictDataRespDTO> dictData = dictDataApi.getByDictTypes(Arrays.asList(CoursesDictEnum.THEME.getType(), CoursesDictEnum.OPTIONAL_THEME.getType(),
                CoursesDictEnum.EDUCATE_FORM.getType())).getCheckedData();
        // 字典数据列表转树节点用于计算
        List<TreeNodeDTO> treeNodeDTOList = coursesServiceImpl.packageDictDataAsTreeNodeDTOList(dictData);
        // 课程分类、教学形式 字典id->完整路径表示ID
        Map<Long, String> map = TreeDataUtil.generateNodeIdToFullPathMap(treeNodeDTOList);
        coursesRespVOList.forEach(coursesRespVO -> {
            if (Objects.nonNull(coursesRespVO.getThemeId())) {
                coursesRespVO.setTheme(map.get(coursesRespVO.getThemeId())); // 课程分类名
            }
            if (Objects.nonNull(coursesRespVO.getEducateFormId())) {
                coursesRespVO.setEducateForm(map.get(coursesRespVO.getEducateFormId())); // 教学形式
            }
            // 科比哦啊教学活动直接显示未教学活动，无需获取字典值
        });
        return coursesRespVOList.stream()
                .collect(Collectors.toMap(CoursesRespVO::getId, vo -> vo, (existing, replacement) -> existing));
    }

    /**
     * 获取发布课表下拉列表
     *
     * @param reqVO 请求参数
     * @return 下拉列表
     */
    @Override
    public List<ClassCourseSimpleRespVO> getSimpleList(ClassCourseSimpleReqVO reqVO) {
        List<ClassCourseSimpleRespVO> simpleList = classCourseMapper.getSimpleList(reqVO);
        simpleList.forEach(vo -> {
            vo.setClassStartTimeStr(DateUtils.format(vo.getClassStartTime(), FORMAT_HOUR_MINUTE));
            vo.setClassEndTimeStr(DateUtils.format(vo.getClassEndTime(), FORMAT_HOUR_MINUTE));
        });
        return simpleList;
    }

    /**
     * 根据午别、上课日期获取选修课上课时间段
     *
     * @param dayPeriod 午别
     * @param classDate 上课日期 YY-MM-HH
     * @return 上课时段
     */
    @Override
    public List<String> getClassTimeByPeriodAndDate(Integer dayPeriod, String classDate) {
        List<ClassCourseDO> list = classCourseMapper.getClassTimeByPeriodAndDate(dayPeriod, classDate);
        return list.stream().map(vo -> DateUtils.format(vo.getBeginTime(), FORMAT_HOUR_MINUTE) + "-" +
                DateUtils.format(vo.getEndTime(), FORMAT_HOUR_MINUTE)).distinct().collect(Collectors.toList());
    }

    /**
     * 获取全校课表
     * @return 全校所有开课中班级课表信息
     */
    @Override
    public List<ClassTableAllRespVO> getTimeTableAll(ClassTimeTableReqVO reqVO){
        //获取当前全校开班中的班级id
        List<ClassManagementDO> classList = classManagementServiceImpl.lambdaQuery()
                .ge(ClassManagementDO::getClassOpenTime, LocalDateTime.now())
                .le(ClassManagementDO::getCompletionTime, LocalDateTime.now())
                .list();

        List<Long> classIdIntList = classList.stream().map(ClassManagementDO::getId).collect(Collectors.toList());
        List<Long> classIdList = new ArrayList<>(classIdIntList);
        reqVO.setClassIdList(classIdList);
        ClassTableRespVO timeTableAll = this.getTimeTable(reqVO);
        List<ClassTimeTableRespVO> courseList = timeTableAll.getCourse();
        List<ClassTableAllRespVO> respVOList = new ArrayList<>();
        List<ClassManagementDO> classManagementList = classManagementServiceImpl.list();
        Map<Long, ClassManagementDO> classManagementMap = classManagementList.stream()
                .collect(Collectors.toMap(ClassManagementDO::getId, cm -> cm));
        Map<String, ClassTimeTableRespVO> classTimeTables = new HashMap<>();
        for(ClassTimeTableRespVO classTimeTableRespVO : courseList){
        // 用于存储按classId分组的ClassTimeTableRespVO对象
            for (ClassTimeTableRespVO.DateCourseVO dateCourse : classTimeTableRespVO.getDateCourse()) {
                for (ClassTimeTableRespVO.CourseVO course : dateCourse.getTimeCourse()) {
                    if(course.getClassId()==null){
                        continue;
                    }
                    // 根据classId获取或创建ClassTimeTableRespVO对象
                    ClassTimeTableRespVO classTimeTable = classTimeTables.computeIfAbsent(course.getClassId().toString() + "日期：" + classTimeTableRespVO.getDate() , k -> {
                        ClassTimeTableRespVO newClassTimeTable = new ClassTimeTableRespVO();
                        newClassTimeTable.setClassId(course.getClassId());
                        newClassTimeTable.setClassName(course.getClassName());
                        newClassTimeTable.setDate(classTimeTableRespVO.getDate());
                        // 其他属性可以根据需要设置
                        return newClassTimeTable;
                    });
                    // 将当前CourseVO添加到对应的ClassTimeTableRespVO对象中
                    // 查找或创建对应的时间段
                    ClassTimeTableRespVO.DateCourseVO classDateCourse = classTimeTable.getDateCourse().stream()
                            .filter(dc -> dc.getType().equals(dateCourse.getType()))
                            .findFirst()
                            .orElseGet(() -> {
                                ClassTimeTableRespVO.DateCourseVO newDateCourse = new ClassTimeTableRespVO.DateCourseVO();
                                newDateCourse.setType(dateCourse.getType());
                                classTimeTable.getDateCourse().add(newDateCourse);
                                return newDateCourse;
                            });
                    // 将当前CourseVO添加到对应的时间段中
                    classDateCourse.getTimeCourse().add(course);
                }
            }
        }
        for (Map.Entry<String, ClassTimeTableRespVO> entry : classTimeTables.entrySet()) {
            String key = entry.getKey();
            ClassTimeTableRespVO value = entry.getValue();
            // 从键中提取classId和date
            String[] parts = key.split("日期：");
            if (parts.length == 2) {
                String classIdStr = parts[0].trim();
                String date = parts[1].trim();
                Long classId = Long.parseLong(classIdStr);
                ClassTableAllRespVO existingVO = null;
                for (ClassTableAllRespVO vo : respVOList) {
                    if (vo.getClassId().equals(classId)) {
                        existingVO = vo;
                        break;
                    }
                }
                if (existingVO == null) {
                    // 如果不存在，则创建新的ClassTableAllRespVO对象
                    ClassTableAllRespVO classTableAllRespVO = new ClassTableAllRespVO();
                    classTableAllRespVO.setClassId(classId);
                    classTableAllRespVO.setClassName(value.getClassName());
                    classTableAllRespVO.setCourse(new ArrayList<>());
                    respVOList.add(classTableAllRespVO);
                    existingVO = classTableAllRespVO;
                }
                // 将ClassTimeTableRespVO添加到对应的List中
                existingVO.getCourse().add(value);
            }
        }
        //班级当天没课或缺少相关午别，则补充相关数据对象
        // 定义日期格式，按日期遍历
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate dateBegin = LocalDate.parse(reqVO.getDateBeg(), dateFormatter);

        for(ClassTableAllRespVO classTableAllRespVO : respVOList){
            List<ClassTimeTableRespVO> course = classTableAllRespVO.getCourse();
            //比对收集
            Set<String> dateSet = course.stream()
                    // 提取 date 字段
                    .map(ClassTimeTableRespVO::getDate)
                    .collect(Collectors.toSet());
            for(int weekday = 1 ; weekday <= 7 ; weekday++){
                String date = dateBegin.plusDays(weekday-1).toString();
                if(!dateSet.contains(date)){
                    ClassTimeTableRespVO classTimeTableRespVO = new ClassTimeTableRespVO();
                    classTimeTableRespVO.setDate(date);
                    //午别填充
                    List<ClassTimeTableRespVO.DateCourseVO> dateCourse = new ArrayList<>();
                    ClassTimeTableRespVO.DateCourseVO morningVO = new ClassTimeTableRespVO.DateCourseVO();
                    morningVO.setType(PeriodEnum.MORNING.getDesc());
                    dateCourse.add(morningVO);
                    ClassTimeTableRespVO.DateCourseVO afternoonVO = new ClassTimeTableRespVO.DateCourseVO();
                    afternoonVO.setType(PeriodEnum.AFTERNOON.getDesc());
                    dateCourse.add(afternoonVO);
                    ClassTimeTableRespVO.DateCourseVO eveningVO = new ClassTimeTableRespVO.DateCourseVO();
                    eveningVO.setType(PeriodEnum.EVENING.getDesc());
                    dateCourse.add(eveningVO);
                    classTimeTableRespVO.setDateCourse(dateCourse);
                    course.add(classTimeTableRespVO);
                }else{
                    ClassTimeTableRespVO classTimeTableRespVO = course.stream().filter(courseVO -> date.equals(courseVO.getDate())).findFirst().orElse(null);
                    List<ClassTimeTableRespVO.DateCourseVO> dateCourse = classTimeTableRespVO.getDateCourse();
                    //比对去重
                    Set<String> typeSet = dateCourse.stream()
                            .map(ClassTimeTableRespVO.DateCourseVO::getType)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
                    for(PeriodEnum period : PeriodEnum.values()){
                        if(!typeSet.contains(period.getDesc())){
                            ClassTimeTableRespVO.DateCourseVO dateCourseVO = new ClassTimeTableRespVO.DateCourseVO();
                            dateCourseVO.setType(period.getDesc());
                            dateCourse.add(dateCourseVO);
                        }
                    }
                }
            }
            //重新排序
            course.sort(Comparator.comparing(ClassTimeTableRespVO::getDate));
        }
        // 过滤 classId 为空的对象
        respVOList = respVOList.stream()
                .filter(respVO -> respVO.getClassId() != null)
                .collect(Collectors.toList());

        //为每个班级新增班级人数字段信息
        List<TraineeDO> traineeDOList = traineeService.lambdaQuery().eq(TraineeDO::getStatus, TraineeStatusEnum.REPORTED.getStatus()).list();
        respVOList.forEach(vo -> vo.setTraineeNum(traineeDOList.stream()
                .filter(trainee -> Objects.equals(trainee.getClassId(), vo.getClassId()))
                .count()));

        if(reqVO.getClassOrder()==null){
            //根据班级id排序
            respVOList.sort(Comparator.comparing(ClassTableAllRespVO::getClassId));
        }else {
            if(respVOList!=null){



                // 使用逗号分割字符串
                String[] numberArray = reqVO.getClassOrder().split(",");

                // 创建一个列表来存储数字
                List<Long> numberList = new ArrayList<>();

                // 将数组中的每个元素转换为 Integer 并添加到列表中
                for (String number : numberArray) {
                    numberList.add(Long.valueOf(number));  // 将字符串转换为整数
                }



                //按班次顺序调整
                respVOList = respVOList.stream().sorted((p1, p2) -> {
                            int indexA = numberList.indexOf(p1.getClassId()); // 获取 p1 的 id 在 classOrder 中的索引
                            int indexB = numberList.indexOf(p2.getClassId()); // 获取 p2 的 id 在 classOrder 中的索引
                            // 如果 p1 或 p2 不在 classOrder 中, 就返回 -1，表示排到最后
                            if (indexA == -1 && indexB == -1) {
                                return 0; // 都不在 classOrder 中，保持原顺序
                            }
                            if (indexA == -1) {
                                return 1; // p1 不在 classOrder 中，排在 p2 后面
                            }
                            if (indexB == -1) {
                                return -1; // p2 不在 classOrder 中，排在 p1 后面
                            }
                            return Integer.compare(indexA, indexB); // 正常比较，按照 classOrder 中的顺序排序
                        }

                ).collect(Collectors.toList());
            }
        }


        return respVOList;
    }

    @Override
    public List<Long> getTimeTableAllOrderClassIdList(ClassTimeTableReqVO reqVO) {


        List<ClassTableAllRespVO> timeTableAll = getTimeTableAll(reqVO);
        List<Long> classIdList = timeTableAll.stream().map(ClassTableAllRespVO::getClassId).collect(Collectors.toList());

        LambdaQueryWrapper<ClassCourseOrderDO> lqw=new LambdaQueryWrapper<>();

        ClassCourseOrderDO classCourseOrderDO = classCourseOrderMapper.selectOne(lqw);

        String classIdOrder = null;
        // 创建一个列表来存储数字
        List<Long> numberList = new ArrayList<>();

        if(classCourseOrderDO!=null&&classCourseOrderDO.getClassIdOrder()!=null){
             classIdOrder = classCourseOrderDO.getClassIdOrder();
        }

        if(classIdOrder!=null){
            // 使用逗号分割字符串
            String[] numberArray = classIdOrder.split(",");

            // 将数组中的每个元素转换为 Integer 并添加到列表中
            for (String number : numberArray) {
                numberList.add(Long.parseLong(number));  // 将字符串转换为整数
            }
        }

        //按班次顺序调整
        classIdList = classIdList.stream().sorted((p1, p2) -> {
                    int indexA = numberList.indexOf(p1); // 获取 p1 的 id 在 classOrder 中的索引
                    int indexB = numberList.indexOf(p2); // 获取 p2 的 id 在 classOrder 中的索引
                    // 如果 p1 或 p2 不在 classOrder 中, 就返回 -1，表示排到最后
                    if (indexA == -1 && indexB == -1) {
                        return 0; // 都不在 classOrder 中，保持原顺序
                    }
                    if (indexA == -1) {
                        return 1; // p1 不在 classOrder 中，排在 p2 后面
                    }
                    if (indexB == -1) {
                        return -1; // p2 不在 classOrder 中，排在 p1 后面
                    }
                    return Integer.compare(indexA, indexB); // 正常比较，按照 classOrder 中的顺序排序
                }

        ).collect(Collectors.toList());

        return classIdList;
    }

    @Override
    public Boolean updateTimeTableAllOrderClassIdList(String classOrder) {
        if(classOrder!=null){
            LambdaQueryWrapper<ClassCourseOrderDO> lqw=new LambdaQueryWrapper<>();
            ClassCourseOrderDO classCourseOrderDO = classCourseOrderMapper.selectOne(lqw);
            if(classCourseOrderDO==null){
                ClassCourseOrderDO idOrder=new ClassCourseOrderDO();
                idOrder.setClassIdOrder(classOrder);
                classCourseOrderMapper.insert(idOrder);
            }else {
                classCourseOrderDO.setClassIdOrder(classOrder);
                classCourseOrderMapper.updateById(classCourseOrderDO);
            }
        }

        return true;
    }

    @Override
    public Set<Map.Entry<String, Map<String, List<UserAppletCourseResponseVO.Data.Sql1>>>> getTimeTableAllApplet(ClassTimeTableReqVO reqVO) {
        List<String> between = new ArrayList<>();
        LocalDate start = LocalDate.parse(reqVO.getDateBeg());
        LocalDate end = LocalDate.parse(reqVO.getDateEnd());
        while (!start.isAfter(end)) {
            between.add(start.toString());
            start = start.plusDays(1);
        }
        if(reqVO.getTeacherId()!=null){
            Long teacherId = teacherInformationService.getTeacherBySystemId(reqVO.getTeacherId());
            if(teacherId!=null){
                reqVO.setTeacherId(teacherId);
            }else {
                //如果未查到则无课表数据
                reqVO.setTeacherId(0l);
            }
        }
        //获取当前全校开班中的班级id
        List<ClassManagementDO> classList = classManagementServiceImpl.lambdaQuery()
                .ge(ClassManagementDO::getClassOpenTime, LocalDateTime.now())
                .le(ClassManagementDO::getCompletionTime, LocalDateTime.now())
                .eq(ClassManagementDO::getTenantId,reqVO.getTenantId())
                .list();
        List<Long> classIdIntList = classList.stream().map(ClassManagementDO::getId).collect(Collectors.toList());
        List<Long> classIdList = new ArrayList<>(classIdIntList);
        reqVO.setClassIdList(classIdList);
        ClassTableRespVO timeTableAll = this.getTimeTable(reqVO);
        List<ClassTimeTableRespVO> courseList = timeTableAll.getCourse();
        List<ClassTableAllRespVO> respVOList = new ArrayList<>();
        List<ClassManagementDO> classManagementList = classManagementServiceImpl.list();
        Map<Long, ClassManagementDO> classManagementMap = classManagementList.stream()
                .collect(Collectors.toMap(ClassManagementDO::getId, cm -> cm));
        Map<String, ClassTimeTableRespVO> classTimeTables = new HashMap<>();
        for(ClassTimeTableRespVO classTimeTableRespVO : courseList){
            // 用于存储按classId分组的ClassTimeTableRespVO对象
            for (ClassTimeTableRespVO.DateCourseVO dateCourse : classTimeTableRespVO.getDateCourse()) {
                for (ClassTimeTableRespVO.CourseVO course : dateCourse.getTimeCourse()) {
                    if(course.getClassId()==null){
                        continue;
                    }
                    // 根据classId获取或创建ClassTimeTableRespVO对象
                    ClassTimeTableRespVO classTimeTable = classTimeTables.computeIfAbsent(course.getClassId().toString() + "日期：" + classTimeTableRespVO.getDate() , k -> {
                        ClassTimeTableRespVO newClassTimeTable = new ClassTimeTableRespVO();
                        newClassTimeTable.setClassId(course.getClassId());
                        newClassTimeTable.setClassName(course.getClassName());
                        newClassTimeTable.setDate(classTimeTableRespVO.getDate());
                        // 其他属性可以根据需要设置
                        return newClassTimeTable;
                    });
                    // 将当前CourseVO添加到对应的ClassTimeTableRespVO对象中
                    // 查找或创建对应的时间段
                    ClassTimeTableRespVO.DateCourseVO classDateCourse = classTimeTable.getDateCourse().stream()
                            .filter(dc -> dc.getType().equals(dateCourse.getType()))
                            .findFirst()
                            .orElseGet(() -> {
                                ClassTimeTableRespVO.DateCourseVO newDateCourse = new ClassTimeTableRespVO.DateCourseVO();
                                newDateCourse.setType(dateCourse.getType());
                                classTimeTable.getDateCourse().add(newDateCourse);
                                return newDateCourse;
                            });
                    // 将当前CourseVO添加到对应的时间段中
                    classDateCourse.getTimeCourse().add(course);
                }
            }
        }
        for (Map.Entry<String, ClassTimeTableRespVO> entry : classTimeTables.entrySet()) {
            String key = entry.getKey();
            ClassTimeTableRespVO value = entry.getValue();
            // 从键中提取classId和date
            String[] parts = key.split("日期：");
            if (parts.length == 2) {
                String classIdStr = parts[0].trim();
                String date = parts[1].trim();
                Long classId = Long.parseLong(classIdStr);
                ClassTableAllRespVO existingVO = null;
                for (ClassTableAllRespVO vo : respVOList) {
                    if (vo.getClassId().equals(classId)) {
                        existingVO = vo;
                        break;
                    }
                }
                if (existingVO == null) {
                    // 如果不存在，则创建新的ClassTableAllRespVO对象
                    ClassTableAllRespVO classTableAllRespVO = new ClassTableAllRespVO();
                    classTableAllRespVO.setClassId(classId);
                    classTableAllRespVO.setClassName(value.getClassName());
                    classTableAllRespVO.setCourse(new ArrayList<>());
                    respVOList.add(classTableAllRespVO);
                    existingVO = classTableAllRespVO;
                }
                // 将ClassTimeTableRespVO添加到对应的List中
                existingVO.getCourse().add(value);
            }
        }
        // 过滤 classId 为空的对象
        respVOList = respVOList.stream()
                .filter(respVO -> respVO.getClassId() != null)
                .collect(Collectors.toList());

        //为每个班级新增班级人数字段信息
        List<TraineeDO> traineeDOList = traineeService.lambdaQuery().eq(TraineeDO::getStatus, TraineeStatusEnum.REPORTED.getStatus()).list();
        respVOList.forEach(vo -> vo.setTraineeNum(traineeDOList.stream()
                .filter(trainee -> Objects.equals(trainee.getClassId(), vo.getClassId()))
                .count()));
        //把教务的数据提取成最小单元
        List<UserAppletCourseResponseVO.Data.Sql1> dataList = new ArrayList<>();
        for (ClassTableAllRespVO c : respVOList){
            //第二层为日期
            List<ClassTimeTableRespVO> dateList = c.getCourse();
            for (ClassTimeTableRespVO classTimeTableRespVO : dateList){
                //为上午中午晚上
                List<ClassTimeTableRespVO.DateCourseVO> course = classTimeTableRespVO.getDateCourse();
                for (ClassTimeTableRespVO.DateCourseVO dateCourseVO : course){
                    //一个上午可以有多节课
                    List<ClassTimeTableRespVO.CourseVO> courseVO = dateCourseVO.getTimeCourse();
                    for (ClassTimeTableRespVO.CourseVO courseVO1 : courseVO){
                        UserAppletCourseResponseVO.Data.Sql1 min = new UserAppletCourseResponseVO.Data.Sql1();
                        //赋值
                        //班次id
                        min.setClass_id(courseVO1.getClassId().toString());
                        //班次名称
                        min.setClass_name(courseVO1.getClassName());
                        //课程名称
                        min.setKC_NAME(courseVO1.getCourseTitle());
                        //学员数
                        min.setStu_num(c.getTraineeNum().toString());
                        //上课日期(上两层取)
                        min.setSk_sj(classTimeTableRespVO.getDate());
                        //上课时段(从上一层取)
                        min.setSk_sd(dateCourseVO.getType());
                        //课程类型
                        min.setKC_TYPE(courseVO1.getCourseType());
                        //教室名称
                         min.setRoom_name(courseVO1.getClassroom());
                        //任课教师名称(主)
                        min.setMAJOR_TEACHERNAME(courseVO1.getTeacherName());
                        //任课教师ID(主)(业中)
                        min.setMAJOR_TEACHER_ID_YZ(courseVO1.getTeachers());
                        //设置开始时间
                        min.setBeginTime(courseVO1.getBeginTime());
                        //设置结束时间
                        min.setEndTime(courseVO1.getEndTime());
                        //设置是否为党政领导讲课
                        min.setLeaderLecture(courseVO1.getIsLeaderLecture());
                        min.setIsDepartmentLeader(courseVO1.getIsDepartmentLeader());
                        min.setIsMerge(courseVO1.getIsMerge());
                        // 是否显示合班授课标签
                        min.setIsShowMergeTag(courseVO1.getIsShowMergeTag());
                        //合并授课班级
                        min.setMergedClass(courseVO1.getMergedClass());
                        dataList.add(min);
                        //任课教师ID(辅)(业中)
//                         min.setMINOR_TEACHER_ID_YZ();
                        //任课教师名称(辅)
//                         min.setMINOR_TEACHERNAME();
                    }
                }
            }
        }
        List<String> dateList = dataList.stream().map(UserAppletCourseResponseVO.Data.Sql1::getSk_sj).distinct().sorted().collect(Collectors.toList());
        Collections.reverse(dateList);
        List<String> classListY = dataList.stream().filter(c->c.getClass_name()!=null).map(UserAppletCourseResponseVO.Data.Sql1::getClass_name).distinct().sorted().collect(Collectors.toList());
        Map<String,Map<String,List<UserAppletCourseResponseVO.Data.Sql1>>> allCourseByDate=new HashMap<>();
        for (String date : dateList) {
            List<UserAppletCourseResponseVO.Data.Sql1> list=new ArrayList<>();
            list=dataList.stream().filter(d->d.getSk_sj().equals(date)).collect(Collectors.toList());
            Map<String,List<UserAppletCourseResponseVO.Data.Sql1>> map=new HashMap<>();
            for (String classData :classListY){
                List<UserAppletCourseResponseVO.Data.Sql1> mapValue = list.stream().filter(c->c.getClass_name()!=null).filter(l -> l.getClass_name().equals(classData)).sorted(Comparator.comparing(UserAppletCourseResponseVO.Data.Sql1::getSk_sd)).collect(Collectors.toList());
                if(mapValue!=null&&mapValue.size()>0){
                    map.put(classData,mapValue);
                }
            }
            allCourseByDate.put(date,map);
        }
        System.out.println(allCourseByDate);
        allCourseByDate.entrySet().removeIf(entry -> entry.getValue() == null || entry.getValue().isEmpty()
                || !between.contains(entry.getKey()));
        System.out.println(allCourseByDate);
        Map<String,Map<String,List<UserAppletCourseResponseVO.Data.Sql1>>> sortedMap = new TreeMap<>(allCourseByDate);
        Set<Map.Entry<String, Map<String, List<UserAppletCourseResponseVO.Data.Sql1>>>> entries = sortedMap.entrySet();

        return entries;
    }

    @Override
    public Long getTeacherIdByUserId(Long userId){
        TeacherInformationDO teacherDO = teacherInformationService.lambdaQuery()
                .eq(TeacherInformationDO::getSystemId, userId)
                .orderByDesc(TeacherInformationDO::getId)
                .last("LIMIT 1")
                .one();
        if(teacherDO==null){
//            throw exception(COURSES_TEACHER_NOT_EXISTS);
            return null;
        }
        return teacherDO.getId();
    }

    @Override
    public Workbook exportTimeTableAll(ClassTimeTableReqVO reqVO, List<ClassTableAllRespVO> listByClass, HttpServletResponse response){
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("课表");
        //工作表默认列宽
        sheet.setColumnWidth(0, 10 * 256);
        //公用样式
        XSSFCellStyle cellStyle = workbook.createCellStyle();
        // 水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        //1、计算一周七天中每天上午、下午、晚上每个时间段需要的最大行数
        List<ClassTimeTableRespVO> allCourses = listByClass.stream()
                .flatMap(classTable -> classTable.getCourse().stream())
                .collect(Collectors.toList());
        // 定义日期格式
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        //获取每天的最大行数
        List<MaxNumPeriod> maxNumPeriodList = new ArrayList<>();
        //周一至周天的对应时间段最大行数
        for(int i=1 ; i<=7 ; i++){
            int weekday = i;
            List<ClassTimeTableRespVO> weekdayCourses = allCourses.stream()
                    .filter(course -> {
                        LocalDate date = LocalDate.parse(course.getDate(), dateFormatter);
                        return date.getDayOfWeek().getValue() == weekday;
                    })
                    .collect(Collectors.toList());
            // 结果存储 Map，按 classId 存储上午、下午、晚上最大数组大小
            // 存储每个时间段的最大值
            Map<String, Integer> maxSizes = new HashMap<>();
            // 依次遍历班级数据，比较每个时间段的最大数组大小
            for (ClassTimeTableRespVO classTimeTable : weekdayCourses) {
                for (ClassTimeTableRespVO.DateCourseVO dateCourse : classTimeTable.getDateCourse()) {
                    String type = dateCourse.getType();
                    int courseSize = dateCourse.getTimeCourse().size();
                    // 更新每个时间段的最大课程数组大小
                    maxSizes.put(type, Math.max(maxSizes.getOrDefault(type, 0), courseSize));
                }
            }
            if(CollectionUtil.isEmpty(maxSizes)){
                continue;
            }
            int morningNum = maxSizes.get(PeriodEnum.MORNING.getDesc()) == 0 ? 1 : maxSizes.get(PeriodEnum.MORNING.getDesc());
            int afternoonNum = maxSizes.get(PeriodEnum.AFTERNOON.getDesc()) == 0 ? 1 : maxSizes.get(PeriodEnum.AFTERNOON.getDesc());
            int eveningNum = maxSizes.get(PeriodEnum.EVENING.getDesc()) == 0 ? 1 : maxSizes.get(PeriodEnum.EVENING.getDesc());
            MaxNumPeriod maxNumPeriod = new MaxNumPeriod(morningNum,afternoonNum,eveningNum);
            maxNumPeriodList.add(maxNumPeriod);
        }
        //2、第一、二行的日期样式
        // 创建第一行
        XSSFRow row1 = sheet.createRow(0);
        // 合并第一、二行的第一、二列（0-based index）
        CellRangeAddress mergedRegion = new CellRangeAddress(0, 1, 0, 1);
        sheet.addMergedRegion(mergedRegion);
        // 设置合并单元格的内容
        XSSFCell cell = row1.createCell(0);
        cell.setCellValue("日期");
        //3、记录每天对应午别开始的行数 + 填充第一二列的日期、午别
        List<StartRowRecord> startRowRecordList = new ArrayList<>();
        // 从第三行开始
        List<String> weekDays = getWeekDays(reqVO.getDateBeg(), reqVO.getDateEnd());
        int startRow = 2;
        int weekdayCount = 0;
        for(MaxNumPeriod maxNumPeriod : maxNumPeriodList){
            //该天占用总行数
            int totalRows = maxNumPeriod.getMorningNum() + maxNumPeriod.getAfternoonNum() + maxNumPeriod.getEveningNum();
            //该天开始行位置
            int fromRow = startRow;
            //该天结束行位置
            int toRow = fromRow + totalRows - 1;
            //该天上午行结束位置
            int toMorningRow = fromRow + maxNumPeriod.getMorningNum() - 1;
            //该天下午行结束位置
            int toAfternoonRow = toMorningRow + maxNumPeriod.getAfternoonNum();
            //该天晚上行结束位置
            int toEveningRow = toAfternoonRow + maxNumPeriod.getEveningNum();
            //记录每天对应午别开始的行数
            StartRowRecord startRowRecord = new StartRowRecord(fromRow,toMorningRow+1,toAfternoonRow+1);
            startRowRecordList.add(startRowRecord);

            //第一列的日期
            // 合并单元格
            CellRangeAddress mergedRegionWeekDay = new CellRangeAddress(fromRow, toRow, 0, 0);
            sheet.addMergedRegion(mergedRegionWeekDay);
            // 在合并单元格的第一个单元格中设置内容
            XSSFRow row = sheet.getRow(fromRow);
            if (row == null) {
                row = sheet.createRow(fromRow);
            }
            XSSFCell cellWeekDay = row.createCell(0);
            cellWeekDay.setCellValue(weekDays.get(weekdayCount));
            weekdayCount++;
            // 设置样式
            cellWeekDay.setCellStyle(cellStyle);

            //第二列的午别
            //上午
            // 合并单元格
            if(fromRow!=toMorningRow){
                CellRangeAddress mergedRegionMorning = new CellRangeAddress(fromRow, toMorningRow, 1, 1);
                sheet.addMergedRegion(mergedRegionMorning);
            }
            // 在合并单元格的第一个单元格中设置内容
            XSSFRow rowMorning = sheet.getRow(fromRow);
            if (rowMorning == null) {
                rowMorning = sheet.createRow(fromRow);
            }
            XSSFCell cellMorning = rowMorning.createCell(1);
            cellMorning.setCellValue(PeriodEnum.MORNING.getDesc());
            // 设置样式
            cellMorning.setCellStyle(cellStyle);

            //下午
            // 合并单元格
            if(toMorningRow+1 != toAfternoonRow){
                CellRangeAddress mergedRegionAfternoon = new CellRangeAddress(toMorningRow+1, toAfternoonRow, 1, 1);
                sheet.addMergedRegion(mergedRegionAfternoon);
            }
            // 在合并单元格的第一个单元格中设置内容
            XSSFRow rowAfternoon = sheet.getRow(toMorningRow+1);
            if (rowAfternoon == null) {
                rowAfternoon = sheet.createRow(toMorningRow+1);
            }
            XSSFCell cellAfternoon = rowAfternoon.createCell(1);
            cellAfternoon.setCellValue(PeriodEnum.AFTERNOON.getDesc());
            // 设置样式
            cellAfternoon.setCellStyle(cellStyle);

            //晚上
            // 合并单元格
            if(toAfternoonRow+1!=toEveningRow){
                CellRangeAddress mergedRegionEvening = new CellRangeAddress(toAfternoonRow+1, toEveningRow, 1, 1);
                sheet.addMergedRegion(mergedRegionEvening);
            }
            // 在合并单元格的第一个单元格中设置内容
            XSSFRow rowEvening = sheet.getRow(toAfternoonRow+1);
            if (rowEvening == null) {
                rowEvening = sheet.createRow(toAfternoonRow+1);
            }
            XSSFCell cellEvening = rowEvening.createCell(1);
            cellEvening.setCellValue(PeriodEnum.EVENING.getDesc());
            // 设置样式
            cellEvening.setCellStyle(cellStyle);

            // 更新下一行的起始位置
            startRow = toRow + 1;
        }

        //4、开始填充班级和对应的课程
        //开始列
        int columnNumPre = 2;
        //按照班级构建每一列
        for(ClassTableAllRespVO classTableAllRespVO : listByClass){
            int columnNumStart = columnNumPre;
            columnNumPre+=2;
            //第一行的班级名称
            XSSFRow rowClassName = sheet.getRow(0);
            //合并第一行的第一、二列
            CellRangeAddress mergedRegionClassName = new CellRangeAddress(0, 0, columnNumStart, columnNumStart+1);
            sheet.addMergedRegion(mergedRegionClassName);
            //设置合并单元格的内容
            XSSFCell cellClassName = rowClassName.createCell(columnNumStart);
            cellClassName.setCellValue(classTableAllRespVO.getClassName());
            cellClassName.setCellStyle(cellStyle);
            //第二行一二列的"内容、讲课人"子样
            XSSFRow rowTitle = sheet.getRow(1);
            if(rowTitle==null){
                rowTitle = sheet.createRow(1);
            }
            // 设置单元格的内容
            XSSFCell cellContent = rowTitle.createCell(columnNumStart);
            cellContent.setCellValue("内容");
            cellContent.setCellStyle(cellStyle);
            XSSFCell cellTeacherName = rowTitle.createCell(columnNumStart+1);
            cellTeacherName.setCellValue("讲课人");
            cellTeacherName.setCellStyle(cellStyle);
            //课程信息开始填充
            List<ClassTimeTableRespVO> list = classTableAllRespVO.getCourse();
            //数据包装
            LinkedHashMap<String, Map<String, List<String>>> scheduleData = getScheduleData(list);
            //提取课程名和讲课人的正则式
            String courseNamePattern = "课程名：(.*?)\\n";
            String lecturerPattern = "讲课人：(.*?)\\n";
            String classLocationPattern = "上课地点：(.*)";
            //weekdayCount周一到周日计数器
            for(weekdayCount = 0 ; weekdayCount <= 6 ; weekdayCount++){
                //该日的午别开始行数位置
                StartRowRecord startRowRecord = startRowRecordList.get(weekdayCount);
                //该日的所有课程信息
                LocalDate date = LocalDate.parse(reqVO.getDateBeg(), dateFormatter);
                String day = date.plusDays(weekdayCount).toString();
                Map<String, List<String>> periodCoursesMap = scheduleData.get(day);
                if(CollectionUtil.isEmpty(periodCoursesMap)){
                    continue;
                }
                //上午课程填充
                //上午开始行数
                Integer morningNum = startRowRecord.getMorningNum();
                List<String> morningClassList = periodCoursesMap.get("morning");
                for(String classInfo : morningClassList){
                    //内容
                    String content = extractValue(classInfo, courseNamePattern);
                    if(StringUtils.isNotEmpty(extractValue(classInfo, classLocationPattern))){
                        content += "(" +extractValue(classInfo, classLocationPattern)+ ")";
                    }
                    //讲课人
                    String teacherName = extractValue(classInfo, lecturerPattern);
                    //设置单元格内容
                    XSSFRow rowMorning = sheet.getRow(morningNum);
                    if(rowMorning==null){
                        rowMorning = sheet.createRow(morningNum);
                    }
                    XSSFCell cellMorningContent = rowMorning.createCell(columnNumStart);
                    cellMorningContent.setCellValue(content);
                    cellMorningContent.setCellStyle(cellStyle);
                    XSSFCell cellMorningTeacher = rowMorning.createCell(columnNumStart+1);
                    cellMorningTeacher.setCellValue(teacherName);
                    cellMorningTeacher.setCellStyle(cellStyle);
                    //下一行起始位置
                    morningNum++;
                }
                //下午课程填充
                //下午开始行数
                Integer afternoonNum = startRowRecord.getAfternoonNum();
                List<String> afternoonClassList = periodCoursesMap.get("afternoon");
                for(String classInfo : afternoonClassList){
                    //内容
                    String content = extractValue(classInfo, courseNamePattern);
                    if(StringUtils.isNotEmpty(extractValue(classInfo, classLocationPattern))){
                        content += "(" +extractValue(classInfo, classLocationPattern)+ ")";
                    }
                    //讲课人
                    String teacherName = extractValue(classInfo, lecturerPattern);
                    //设置单元格内容
                    XSSFRow rowAfternoon = sheet.getRow(afternoonNum);
                    if(rowAfternoon==null){
                        rowAfternoon = sheet.createRow(afternoonNum);
                    }
                    XSSFCell cellAfternoonContent = rowAfternoon.createCell(columnNumStart);
                    cellAfternoonContent.setCellValue(content);
                    cellAfternoonContent.setCellStyle(cellStyle);
                    XSSFCell cellAfternoonTeacher = rowAfternoon.createCell(columnNumStart+1);
                    cellAfternoonTeacher.setCellValue(teacherName);
                    cellAfternoonTeacher.setCellStyle(cellStyle);
                    //下一行起始位置
                    afternoonNum++;
                }
                //晚上课程填充
                //晚上开始行数
                Integer eveningNum = startRowRecord.getEveningNum();
                List<String> eveningClassList = periodCoursesMap.get("evening");
                for(String classInfo : eveningClassList){
                    //内容
                    String content = extractValue(classInfo, courseNamePattern);
                    if(StringUtils.isNotEmpty(extractValue(classInfo, classLocationPattern))){
                        content += "(" +extractValue(classInfo, classLocationPattern)+ ")";
                    }
                    //讲课人
                    String teacherName = extractValue(classInfo, lecturerPattern);
                    //设置单元格内容
                    XSSFRow rowEvening = sheet.getRow(eveningNum);
                    if(rowEvening==null){
                        rowEvening = sheet.createRow(eveningNum);
                    }
                    XSSFCell cellEveningContent = rowEvening.createCell(columnNumStart);
                    cellEveningContent.setCellValue(content);
                    cellEveningContent.setCellStyle(cellStyle);
                    XSSFCell cellEveningTeacher = rowEvening.createCell(columnNumStart+1);
                    cellEveningTeacher.setCellValue(teacherName);
                    cellEveningTeacher.setCellStyle(cellStyle);
                    //下一行起始位置
                    eveningNum++;
                }
            }
            //设置统一样式
            // 获取表格的总行数
            int numberOfRows = sheet.getPhysicalNumberOfRows();
            // 遍历所有行
            for (int i = 0; i < numberOfRows; i++) {
                XSSFRow row = sheet.getRow(i);
                if (row == null) {
                    continue; // 如果行为空，跳过
                }
                // 获取当前行的总列数
                int numberOfCells = row.getPhysicalNumberOfCells();
                // 遍历所有列
                for (int j = 0; j < numberOfCells; j++) {
                    XSSFCell styleCell = row.getCell(j);
                    if (styleCell == null) {
                        // 如果单元格为空，跳过
                        continue;
                    }
                    // 设置样式
                    styleCell.setCellStyle(cellStyle);
                }
            }
            // 获取总行数
            int totalRows = sheet.getPhysicalNumberOfRows();
            // 用于计算表格总列数
            int totalColumns = 0;
            // 计算总列数（以第一行的列数为准）
            if (totalRows > 0) {
                XSSFRow firstRow = sheet.getRow(0);
                if (firstRow != null) {
                    totalColumns = firstRow.getPhysicalNumberOfCells();
                }
            }
            // 创建无边框样式
            XSSFCellStyle cellStyleFinal = workbook.createCellStyle();
            // 水平居中
            cellStyleFinal.setAlignment(HorizontalAlignment.CENTER);
            // 垂直居中
            cellStyleFinal.setVerticalAlignment(VerticalAlignment.CENTER);
            // 遍历设置样式、行高和列宽
            for (int rowIndex = 0; rowIndex < totalRows; rowIndex++) {
                XSSFRow row = sheet.getRow(rowIndex);
                if (row == null) {
                    continue;
                }
                // 设置行高
                row.setHeight((short) (30 * 20));
                for (int colIndex = 0; colIndex < totalColumns; colIndex++) {
                    XSSFCell cellFinal = row.getCell(colIndex);
                    if (cellFinal == null) {
                        continue;
                    }
                    // 设置样式（无边框）
                    cellFinal.setCellStyle(cellStyleFinal);
                }
            }
            // 设置第一列宽度为 25（单位为 1/256 字符宽度）
            sheet.setColumnWidth(0, 25 * 256);
            // 设置其余列宽度为 20
            for (int col = 1; col < totalColumns; col++) {
                sheet.setColumnWidth(col, 20 * 256);
            }
        }
        return workbook;
    }

    /**
     * 计算从dateBeg到dateEnd之间每一天的星期几和日期，返回List<String>
     * 格式为 "星期X yyyy-MM-dd"
     *
     * @param dateBeg 起始日期（yyyy-MM-dd）
     * @param dateEnd 结束日期（yyyy-MM-dd）
     * @return 包含每个日期及其星期几的List
     */
    public static List<String> getWeekDays(String dateBeg, String dateEnd) {
        List<String> weekDays = new ArrayList<>();

        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 解析起始日期和结束日期
        LocalDate startDate = LocalDate.parse(dateBeg, formatter);
        LocalDate endDate = LocalDate.parse(dateEnd, formatter);

        // 遍历日期范围，计算每一天的星期几
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            // 获取当前日期的星期几
            DayOfWeek dayOfWeek = currentDate.getDayOfWeek();
            String weekDayName = getWeekDayName(dayOfWeek);

            // 将格式化后的日期和星期几加入列表
            String formattedDate = currentDate.format(formatter);
            weekDays.add(weekDayName + " " + formattedDate);

            // 移动到下一天
            currentDate = currentDate.plusDays(1);
        }

        return weekDays;
    }

    /**
     * 将DayOfWeek转换为星期几的中文名称
     *
     * @param dayOfWeek 星期几
     * @return 中文星期几名称
     */
    private static String getWeekDayName(DayOfWeek dayOfWeek) {
        switch (dayOfWeek) {
            case MONDAY: return "星期一";
            case TUESDAY: return "星期二";
            case WEDNESDAY: return "星期三";
            case THURSDAY: return "星期四";
            case FRIDAY: return "星期五";
            case SATURDAY: return "星期六";
            case SUNDAY: return "星期天";
            default: throw new IllegalArgumentException("Invalid day of week");
        }
    }

    /**
     *
     * 全校导出-匹配课程名和讲课人
     * @param input
     * @param pattern
     * @return
     */
    private static String extractValue(String input, String pattern) {
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(input);
        if (m.find()) {
            // 提取第一个捕获组
            return m.group(1);
        }
        // 如果未匹配到返回 null
        return null;
    }

    /**
     * 获得班级课程最后一节课的排课日期，用于调课时快速定位
     * @param classId 班级id
     * @return 最后一节课的日期
     */
    @Override
    public String getLatestDate(@NonNull Long classId){
//        Long planId;
//        // 获取当前班级所启用的教学计划对应的课表
//        List<PlanDO> planList = planService.lambdaQuery()
//                .eq(PlanDO::getClassId, classId)
//                .eq(PlanDO::getStatus, PlanStatusEnum.ON.getCode())
//                .list();
//        if (planList.isEmpty()) {
//            throw exception(PLAN_NOT_EXIST);
//        }
//        planId = planList.get(0).getId();
        LambdaQueryWrapper<ClassCourseDO> classCourseDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getClassId, classId);
        classCourseDOLambdaQueryWrapper.isNotNull(ClassCourseDO::getCourseId);
        classCourseDOLambdaQueryWrapper.orderByDesc(ClassCourseDO::getId);
        classCourseDOLambdaQueryWrapper.last("LIMIT 1");
        ClassCourseDO classCourseDO = classCourseMapper.selectOne(classCourseDOLambdaQueryWrapper);
        if(classCourseDO==null){
            throw exception(CLASS_COURSE_NOT_EXISTS);
        }
        return classCourseDO.getDate();
    }


    /**
     * 获取一个排课的合班授课的班级列表
     *
     * @param classCourseId 排课id
     * @return 合班授课的班级列表
     */
    @Override
    public List<ClassInfoRespVO> getMergedClassList(Long classCourseId) {
        ClassCourseDO classCourseDO = classCourseMapper.selectById(classCourseId);
        if (classCourseDO == null) {
            throw exception(CLASS_COURSE_NOT_EXISTS);
        }

        // 判断是否为合班授课
        if (Boolean.FALSE.equals(classCourseDO.getIsMerge())){
            return Collections.emptyList();
        }

        // 获取该课程合班授课的课程
        List<ClassCourseClassNameRespDTO> mergedClassCourseList = classCourseMapper.selectMergedClassCourse(classCourseId);

        // 获取班级信息
        List<Long> classIds = mergedClassCourseList.stream().map(ClassCourseClassNameRespDTO::getClassId).collect(Collectors.toList());

        if (CollUtil.isEmpty(classIds)) {
            return Collections.emptyList();
        }

        return mergedClassCourseList.stream().map(o -> {
            ClassInfoRespVO classInfoRespVO = new ClassInfoRespVO();
            classInfoRespVO.setId(o.getClassId());
            classInfoRespVO.setName(o.getClassName());
            return classInfoRespVO;
        }).collect(Collectors.toList());
    }

    /**
     * 更新班级与课程绑定信息的方法
     * 此方法主要用于更新班级与课程之间的绑定关系，确保课程能够在指定的班级中显示和使用
     *
     * @param updateReqVO 包含更新信息的请求对象，包括班级ID列表、课程ID等必要信息
     */
    @Override
    public void updateClassCourseBinding(@Valid ClassCourseBindingUpdateReqVO updateReqVO){
        //查询原课表格子信息
        ClassCourseDO classCourseDO = classCourseMapper.selectById(updateReqVO.getId());
        //查询传入班级Id的实体信息
        ClassManagementDO classManagementDO = classManagementMapper.selectById(updateReqVO.getClassId());
        if (classCourseDO == null) {
            throw exception(CLASS_COURSE_NOT_EXISTS);
        }
        if(updateReqVO.getAddBinding()){
            //增加绑定关系
            addBindingRelation(updateReqVO, classCourseDO, classManagementDO);
        }else {
            //删除绑定关系
            deleteBindingRelation(updateReqVO, classCourseDO);
        }
    }

    /**
     * 获取合并信息
     * 该方法用于处理一组班级与课程绑定的更新请求，提取或生成有关这些绑定的合并信息
     * 主要用途是当需要对多个班级课程绑定进行更新时，通过此方法可以获取到所有绑定的合并信息，
     * 以便在更新前进行统一的处理或校验
     *
     * @param classCourseBindingUpdateReqVOList 班级课程绑定更新请求列表
     *              这是一个包含多个班级课程绑定更新请求的列表，每个请求包含了需要更新的班级课程绑定的信息
     * @return List<String> 合并信息列表
     *         返回一个包含所有合并信息的字符串列表，这些信息是根据输入的班级课程绑定更新请求生成的
     */
    @Override
    public List<ClassCourseBindingInfoRespVO> getMergeInfo(List<ClassCourseBindingUpdateReqVO> classCourseBindingUpdateReqVOList){
        List<String> classNameList = new ArrayList<>();
        //先收集冲突课程对应的课表格子信息，最后再统一附带教师等信息
        List<ClassCourseBindingInfoRespVO> classCourseBindingInfoRespVOList = new ArrayList<>();
        // 1、创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        // 2、多线程计数器
        CountDownLatch latch = new CountDownLatch(classCourseBindingUpdateReqVOList.size());
        classCourseBindingUpdateReqVOList.forEach(updateReqVO -> {
            executorService.submit(() -> {
                try {
                    //查询原课表格子信息
                    ClassCourseDO classCourseDO = classCourseMapper.selectById(updateReqVO.getId());
                    //查询传入班级Id的实体信息
                    ClassManagementDO classManagementDO = classManagementMapper.selectById(updateReqVO.getClassId());
                    if (classCourseDO == null) {
                        throw exception(CLASS_COURSE_NOT_EXISTS);
                    }
                    if(updateReqVO.getAddBinding()){
                        //判断冲突信息
                        //新增草稿格子判断逻辑
                        if(classCourseDO.getIsTemporary()){
                            //课程暂存时直接按照时段进行冲突判断
                            LambdaQueryWrapper<ClassCourseDO> classCourseDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
//                            // 查询给定班级下是否有授课时间与原课表格子开始时间、结束时间的小时、分钟相等的格子
//                            classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getDate, classCourseDO.getDate())
//                                    .eq(ClassCourseDO::getClassId, updateReqVO.getClassId())
//                                    .eq(ClassCourseDO::getDate, classCourseDO.getDate())
//                                    .eq(ClassCourseDO::getPeriod, classCourseDO.getPeriod());
                            if(classCourseDO.getBeginTime() != null && classCourseDO.getEndTime() != null){
                                classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getDate, classCourseDO.getDate())
                                        .eq(ClassCourseDO::getClassId, updateReqVO.getClassId())
                                        .apply("to_char(begin_time, 'HH24:MI') = to_char({0}, 'HH24:MI')", classCourseDO.getBeginTime())
                                        .apply("to_char(end_time, 'HH24:MI') = to_char({0}, 'HH24:MI')", classCourseDO.getEndTime());
                            }else{
                                classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getDate, classCourseDO.getDate())
                                        .eq(ClassCourseDO::getClassId, updateReqVO.getClassId())
                                        .eq(ClassCourseDO::getDate, classCourseDO.getDate())
                                        .eq(ClassCourseDO::getPeriod, classCourseDO.getPeriod())
                                        .isNull(ClassCourseDO::getBeginTime)
                                        .isNull(ClassCourseDO::getEndTime);
                                // 1.4.0暂时的逻辑：只有有时间安排的格子才能合班，否则无法合班，后续不排除解开上面四行的注释，在无时间安排时根据午别合班
//            return;
                            }

                            List<ClassCourseDO> classCourseDOList = classCourseMapper.selectList(classCourseDOLambdaQueryWrapper);
                            if(!classCourseDOList.isEmpty()) {
                                for(ClassCourseDO classCourseTargetDO : classCourseDOList){
                                        //1、有格子则判断是否已排课，或者已排课则不更换，未排课则把教师、课程等信息都同步过去
                                        if (classCourseTargetDO.getCourseId() != null) {
                                            //1.1、已排课
                                            if (!classCourseTargetDO.getCourseId().equals(classCourseDO.getCourseId())) {
                                                //1.1.1、已排课且排课不是同一个课程，则提示冲突
                                                classNameList.add(classManagementDO.getClassName());
                                                ClassCourseBindingInfoRespVO classCourseBindingInfoRespVO = new ClassCourseBindingInfoRespVO();
                                                classCourseBindingInfoRespVO.setClassName(classManagementDO.getClassName());
                                                classCourseBindingInfoRespVO.setClassCourseDOList(classCourseDOList);
                                                classCourseBindingInfoRespVOList.add(classCourseBindingInfoRespVO);
                                                break;
                                            } else {
                                                //已排课、同一个课程，但教师不同，也提示冲突
                                                if (!isSameTeachers(classCourseDOList.get(0), classCourseDO)) {
                                                    classNameList.add(classManagementDO.getClassName());

                                                    ClassCourseBindingInfoRespVO classCourseBindingInfoRespVO = new ClassCourseBindingInfoRespVO();
                                                    classCourseBindingInfoRespVO.setClassName(classManagementDO.getClassName());
                                                    classCourseBindingInfoRespVO.setClassCourseDOList(classCourseDOList);
                                                    classCourseBindingInfoRespVOList.add(classCourseBindingInfoRespVO);
                                                    break;
                                                }
                                            }
                                        }
                                    }
                            }else {
                                //2、没有格子则给该班级增加一个排课且发布非暂存（没有格子则新增一个格子，需要判断时间冲突，直接调用原来的新增午别方法，并排课相同的课，注意授课教师也要更换）
                                ClassCourseCreateReqVO createReqVO = new ClassCourseCreateReqVO();
                                createReqVO.setClassId(updateReqVO.getClassId());
                                createReqVO.setBeginTime(classCourseDO.getBeginTime());
                                createReqVO.setEndTime(classCourseDO.getEndTime());
                                createReqVO.setDate(classCourseDO.getDate());
                                createReqVO.setPeriod(classCourseDO.getPeriod());
                                //查询该班级对应查询到的课表格子的日期，是否存在教学计划包围其中
                                LambdaQueryWrapper<PlanDO> planDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                                planDOLambdaQueryWrapper.eq(PlanDO::getClassId, updateReqVO.getClassId());
                                List<PlanDO> planDOList = planMapper.selectList(planDOLambdaQueryWrapper);
                                //筛选planDOList中每个对象的beginDate与endDate字段，筛选出与classCourseDO的date字段在期间的planDO
                                List<PlanDO> planDOListFiltered = planDOList.stream().filter(planDO -> {
                                    try {
                                        return planDO.getBeginDate().compareTo(classCourseDO.getDate()) <= 0 && planDO.getEndDate().compareTo(classCourseDO.getDate()) >= 0;
                                    } catch (Exception e) {
                                        log.error(e.getMessage());
                                    }
                                    return false;
                                }).collect(Collectors.toList());
                                if (!planDOListFiltered.isEmpty()) {
                                    createReqVO.setPlanId(planDOListFiltered.get(0).getId());
                                }

                                // 检测插入
                                // 校验是否有冲突时段
                                List<ClassCourseDO> classCourseDOListTemp = this.lambdaQuery()
                                        .lt(ClassCourseDO::getBeginTime, createReqVO.getEndTime())
                                        .gt(ClassCourseDO::getEndTime, createReqVO.getBeginTime())
                                        .list();
                                classCourseDOListTemp = classCourseDOListTemp.stream().filter(classCourseDOTemp -> createReqVO.getClassId().equals(classCourseDOTemp.getClassId())).collect(Collectors.toList());
                                if (!classCourseDOListTemp.isEmpty()) {
                                    classNameList.add(classManagementDO.getClassName());

                                    ClassCourseBindingInfoRespVO classCourseBindingInfoRespVO = new ClassCourseBindingInfoRespVO();
                                    classCourseBindingInfoRespVO.setClassName(classManagementDO.getClassName());
                                    classCourseBindingInfoRespVO.setClassCourseDOList(classCourseDOListTemp);
                                    classCourseBindingInfoRespVOList.add(classCourseBindingInfoRespVO);
                                }
                            }
                        }else{
                            LambdaQueryWrapper<ClassCourseDO> classCourseDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                            // 查询给定班级下是否有授课时间与原课表格子开始时间、结束时间的小时、分钟相等的格子
                            classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getDate, classCourseDO.getDate())
                                    .eq(ClassCourseDO::getClassId, updateReqVO.getClassId())
                                    .apply("to_char(begin_time, 'HH24:MI') = to_char({0}, 'HH24:MI')", classCourseDO.getBeginTime())
                                    .apply("to_char(end_time, 'HH24:MI') = to_char({0}, 'HH24:MI')", classCourseDO.getEndTime());
                            List<ClassCourseDO> classCourseDOList = classCourseMapper.selectList(classCourseDOLambdaQueryWrapper);
                            if(!classCourseDOList.isEmpty()){
                                //1、有格子则判断是否已排课，或者已排课则不更换，未排课则把教师、课程等信息都同步过去
                                ClassCourseDO classCourseTargetDO = classCourseDOList.get(0);
                                if(classCourseTargetDO.getCourseId()!=null){
                                    //1.1、已排课
                                    if(!classCourseDOList.get(0).getCourseId().equals(classCourseDO.getCourseId())){
                                        //1.1.1、已排课且排课不是同一个课程，则提示冲突
                                        classNameList.add(classManagementDO.getClassName());

                                        List<ClassCourseDO> courseDOList = new ArrayList<>();
                                        courseDOList.add(classCourseTargetDO);
                                        ClassCourseBindingInfoRespVO classCourseBindingInfoRespVO = new ClassCourseBindingInfoRespVO();
                                        classCourseBindingInfoRespVO.setClassName(classManagementDO.getClassName());
                                        classCourseBindingInfoRespVO.setClassCourseDOList(courseDOList);
                                        classCourseBindingInfoRespVOList.add(classCourseBindingInfoRespVO);
                                    }else{
                                        //已排课、同一个课程，但教师不同，也提示冲突
                                        if(!isSameTeachers(classCourseDOList.get(0),classCourseDO)){
                                            classNameList.add(classManagementDO.getClassName());

                                            List<ClassCourseDO> courseDOList = new ArrayList<>();
                                            courseDOList.add(classCourseTargetDO);
                                            ClassCourseBindingInfoRespVO classCourseBindingInfoRespVO = new ClassCourseBindingInfoRespVO();
                                            classCourseBindingInfoRespVO.setClassName(classManagementDO.getClassName());
                                            classCourseBindingInfoRespVO.setClassCourseDOList(courseDOList);
                                            classCourseBindingInfoRespVOList.add(classCourseBindingInfoRespVO);
                                        }
                                    }
                                }
                            }else{
                                //2、没有格子则给该班级增加一个排课且发布非暂存（没有格子则新增一个格子，需要判断时间冲突，直接调用原来的新增午别方法，并排课相同的课，注意授课教师也要更换）
                                ClassCourseCreateReqVO createReqVO = new ClassCourseCreateReqVO();
                                createReqVO.setClassId(updateReqVO.getClassId());
                                createReqVO.setBeginTime(classCourseDO.getBeginTime());
                                createReqVO.setEndTime(classCourseDO.getEndTime());
                                createReqVO.setDate(classCourseDO.getDate());
                                createReqVO.setPeriod(classCourseDO.getPeriod());
                                //查询该班级对应查询到的课表格子的日期，是否存在教学计划包围其中
                                LambdaQueryWrapper<PlanDO> planDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                                planDOLambdaQueryWrapper.eq(PlanDO::getClassId, updateReqVO.getClassId());
                                List<PlanDO> planDOList = planMapper.selectList(planDOLambdaQueryWrapper);
                                //筛选planDOList中每个对象的beginDate与endDate字段，筛选出与classCourseDO的date字段在期间的planDO
                                List<PlanDO> planDOListFiltered = planDOList.stream().filter(planDO -> {
                                    try {
                                        return planDO.getBeginDate().compareTo(classCourseDO.getDate()) <= 0 && planDO.getEndDate().compareTo(classCourseDO.getDate()) >= 0;
                                    } catch (Exception e) {
                                        log.error(e.getMessage());
                                    }
                                    return false;
                                }).collect(Collectors.toList());
                                if(!planDOListFiltered.isEmpty()){
                                    createReqVO.setPlanId(planDOListFiltered.get(0).getId());
                                }

                                // 检测插入
                                // 校验是否有冲突时段
                                List<ClassCourseDO> classCourseDOListTemp = this.lambdaQuery()
                                        .lt(ClassCourseDO::getBeginTime, createReqVO.getEndTime())
                                        .gt(ClassCourseDO::getEndTime, createReqVO.getBeginTime())
                                        .list();
                                classCourseDOListTemp = classCourseDOListTemp.stream().filter(classCourseDOTemp -> createReqVO.getClassId().equals(classCourseDOTemp.getClassId())).collect(Collectors.toList());
                                if (!classCourseDOListTemp.isEmpty()) {
                                    classNameList.add(classManagementDO.getClassName());

                                    ClassCourseBindingInfoRespVO classCourseBindingInfoRespVO = new ClassCourseBindingInfoRespVO();
                                    classCourseBindingInfoRespVO.setClassName(classManagementDO.getClassName());
                                    classCourseBindingInfoRespVO.setClassCourseDOList(classCourseDOListTemp);
                                    classCourseBindingInfoRespVOList.add(classCourseBindingInfoRespVO);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }finally {
                    latch.countDown();
                }
            });
        });
        // 3. 主线程等待所有任务完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        // 4. 关闭线程池
        executorService.shutdown();

        //获取课程详细信息，如教师、教室、课程名等
        if(CollectionUtil.isNotEmpty(classCourseBindingInfoRespVOList)){
            getClassCourseInfo(classCourseBindingInfoRespVOList);
        }

        return classCourseBindingInfoRespVOList;
    }

    /**
     * 获取班级课程信息并补充相关细节信息
     * 此方法首先收集所有班级课程信息，然后提取所有相关的classroomId、courseId和teacherId
     * 接着，批量查询教室、课程和教师的详细信息，并将这些信息补充到班级课程信息中
     *
     * @param classCourseBindingInfoRespVOList 班级课程绑定信息响应VO列表
     */
    private void getClassCourseInfo(List<ClassCourseBindingInfoRespVO> classCourseBindingInfoRespVOList) {
        // ====== 1. 全量收集所有班级课程信息 ======
        List<ClassCourseDO> allCourses = classCourseBindingInfoRespVOList.stream()
                .flatMap(resp -> resp.getClassCourseDOList().stream())
                .collect(Collectors.toList());
        // ====== 2. 提取所有 classroomId、courseId、teacherId ======
        Set<Long> allClassroomIds = allCourses.stream()
                .map(ClassCourseDO::getClassroomId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Set<Long> allCourseIds = allCourses.stream()
                .map(ClassCourseDO::getCourseId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Set<Long> allTeacherIds = allCourses.stream()
                .map(ClassCourseDO::getTeacherIdString)
                .filter(StringUtils::isNotBlank)
                .flatMap(s -> Arrays.stream(s.split(",")))
                .map(String::trim)
                .filter(s -> {
                    try {
                        Long.parseLong(s);
                        return true;
                    } catch (NumberFormatException e) {
                        return false;
                    }
                })
                .map(Long::valueOf)
                .collect(Collectors.toSet());
        // ====== 3. 执行一次性批量查询 ======
        Map<Integer, ClassroomLibraryDO> classroomLibraryMap = CollectionUtils.isEmpty(allClassroomIds)
                ? new HashMap<>()
                : classroomLibraryService.listByIds(allClassroomIds).stream()
                .collect(Collectors.toMap(ClassroomLibraryDO::getId, cmDO -> cmDO));

        Map<Long, CoursesRespVO> coursesInfoMap = CollectionUtils.isEmpty(allCourseIds)
                ? new HashMap<>()
                : getCoursesInfoMap(new ArrayList<>(allCourseIds));

        Map<Long, TeacherInformationDO> teacherInformationMap = CollectionUtils.isEmpty(allTeacherIds)
                ? new HashMap<>()
                : teacherInformationService.listByIds(new ArrayList<>(allTeacherIds)).stream()
                .collect(Collectors.toMap(TeacherInformationDO::getId, cmDO -> cmDO));
        //对返回类的每一个课表格子数组进行信息补充，如教师名称等  todo 替换为多线程或者批量查询
        for (ClassCourseBindingInfoRespVO classCourseBindingInfoRespVO : classCourseBindingInfoRespVOList) {
            List<ClassCourseDO> list = classCourseBindingInfoRespVO.getClassCourseDOList();
//            // 教室
//            List<Long> classRoomIds = list.stream().map(ClassCourseDO::getClassroomId).filter(Objects::nonNull).collect(Collectors.toList());
//            // 添加常用教室信息
//            List<ClassroomLibraryDO> classroomLibraryList = !classRoomIds.isEmpty() ? classroomLibraryService.listByIds(classRoomIds) : new ArrayList<>();
//            Map<Integer, ClassroomLibraryDO> classroomLibraryMap = classroomLibraryList.stream()
//                    .collect(Collectors.toMap(ClassroomLibraryDO::getId, cmDO -> cmDO));
//            // 课程
//            List<Long> courseList = list.stream().map(ClassCourseDO::getCourseId).filter(Objects::nonNull).collect(Collectors.toList());
//            // 带有字典值的课程
//            Map<Long, CoursesRespVO> coursesInfoMap = getCoursesInfoMap(courseList);
//
//            List<Long> teacherIds = list.stream()
//                    .map(ClassCourseDO::getTeacherIdString)
//                    .filter(Objects::nonNull)
//                    .flatMap(teacherIdString -> Arrays.stream(teacherIdString.split(",")))
//                    .map(String::trim)
//                    .filter(id -> {
//                        try {
//                            // 尝试转换为 Long
//                            Long.parseLong(id);
//                            return true;
//                        } catch (NumberFormatException e) {
//                            // 如果抛出异常，则过滤掉
//                            return false;
//                        }
//                    })
//                    .map(Long::valueOf)
//                    .distinct() // 去除重复的 ID
//                    .collect(Collectors.toList());
//            List<TeacherInformationDO> teacherInformationList = !teacherIds.isEmpty() ? teacherInformationService.listByIds(teacherIds) : new ArrayList<>();
//            Map<Long, TeacherInformationDO> teacherInformationMap = teacherInformationList.stream()
//                    .collect(Collectors.toMap(TeacherInformationDO::getId, cmDO -> cmDO));
            // 补充其他信息
            for (ClassCourseDO classCourse : list) {
                try {
                    if (classCourse.getBeginTime() == null) {
                        int a = 1;
                    }
                    // 非选修课情况
                    if (!OPTIONAL_COURSE_ID.equals(classCourse.getCourseId())) {
                        if (classCourse.getClassroomId() != null) {
                            ClassroomLibraryDO classroomLibraryDO = classroomLibraryMap.get(classCourse.getClassroomId().intValue());
                            // 补充教室信息
                            if (classroomLibraryDO != null) {
                                classCourse.setClassroomName(classroomLibraryDO.getClassName());
                            }
                        }
                        // 补充老师信息
                        if (!classCourse.getDepartment()) {
                            if (StringUtils.isNotEmpty(classCourse.getTeacherIdString())) {
                                // 以逗号分隔的教师ID字符串拆分并获取每个教师的信息
                                String[] teacherIdList = classCourse.getTeacherIdString().split(",");
                                StringBuilder teacherNames = new StringBuilder();

                                for (String teacherIdStr : teacherIdList) {
                                    try {
                                        // 转换为Long类型
                                        Long teacherId = Long.valueOf(teacherIdStr.trim());
                                        TeacherInformationDO teacherInformationDO = teacherInformationMap.get(teacherId);
                                        if (teacherInformationDO != null) {
                                            if (teacherNames.length() > 0) {
                                                // 如果不是第一个教师，添加逗号
                                                teacherNames.append("，");
                                            }
                                            teacherNames.append(teacherInformationDO.getName());
                                        }
                                    } catch (NumberFormatException e) {
                                        log.error(e.getMessage());
                                    }
                                }
                                // 设置教师名称，多个教师的名字用逗号分隔
                                classCourse.setTeacherName(teacherNames.toString());
                            }
                        } else {
                            //部门授课
                            classCourse.setTeacherName(classCourse.getTeacherIdString());
                        }
                        // 补充课程信息
                        CoursesRespVO coursesRespVO = coursesInfoMap.get(classCourse.getCourseId());
                        if (coursesRespVO != null) {
                            classCourse.setCourseName(coursesRespVO.getName());
                            classCourse.setCourseType(CoursesTypeEnum.getDescByType(coursesRespVO.getCoursesType()));
                            classCourse.setCourseTypeId(coursesRespVO.getCoursesType().toString());
                            classCourse.setEducateFormId(coursesRespVO.getEducateFormId());
                            classCourse.setEducateFormName(coursesRespVO.getEducateForm());
                            classCourse.setThemeId(coursesRespVO.getThemeId());
                            classCourse.setThemeName(coursesRespVO.getTheme());
                            if (CoursesTypeEnum.TEACHING_ACTIVITY.getType().equals(coursesRespVO.getCoursesType())) {
                                classCourse.setEducateFormName(CoursesTypeEnum.TEACHING_ACTIVITY.getDesc());
                                classCourse.setActivityType(CoursesTypeEnum.TEACHING_ACTIVITY.getDesc());
                            }

                            // 时间范围设置回显
                            if(classCourse.getBeginTime() != null && classCourse.getEndTime() != null){
                                List<LocalDateTime> localDateTimeList = new ArrayList<>();
                                localDateTimeList.add(classCourse.getBeginTime());
                                localDateTimeList.add(classCourse.getEndTime());
                                classCourse.setLocalDateTimeList(localDateTimeList);
                                String result = localDateTimeList.stream()
                                        .map(dt -> String.format("%02d:%02d", dt.getHour(), dt.getMinute()))
                                        .collect(Collectors.joining("-"));
                                classCourse.setTimeRange(result);
                            }

                        }
                    } else {
                        // 为选修课时
                        classCourse.setCourseType(CoursesTypeEnum.OPTIONAL_COURSE.getDesc());
                        classCourse.setCourseTypeId(CoursesTypeEnum.OPTIONAL_COURSE.getType().toString());
                    }

                    classCourseBindingInfoRespVO.setClassCourseDOList(list);

                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
        }
    }

    /**
     * 删除绑定关系
     * 当更新班级课程绑定信息时，此方法用于删除原课程时间表中的特定绑定关系
     * 它通过比较日期、班级ID以及开始和结束时间来定位需要更新的课程绑定信息
     *
     * @param updateReqVO 包含更新请求信息的对象，包括班级ID等信息
     * @param classCourseDO 原课程时间表对象，包含日期、开始时间、结束时间等信息
     */
    private void deleteBindingRelation(ClassCourseBindingUpdateReqVO updateReqVO, ClassCourseDO classCourseDO) {
        LambdaQueryWrapper<ClassCourseDO> classCourseDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 查询给定班级下是否有授课时间与原课表格子开始时间、结束时间的小时、分钟相等的格子
        classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getDate, classCourseDO.getDate())
                .eq(ClassCourseDO::getClassId, updateReqVO.getClassId())
                .apply("to_char(begin_time, 'HH24:MI') = to_char({0}, 'HH24:MI')", classCourseDO.getBeginTime())
                .apply("to_char(end_time, 'HH24:MI') = to_char({0}, 'HH24:MI')", classCourseDO.getEndTime());
        List<ClassCourseDO> classCourseDOList = classCourseMapper.selectList(classCourseDOLambdaQueryWrapper);
        if(!classCourseDOList.isEmpty()){
            ClassCourseDO classCourseUpdateDO = classCourseDOList.get(0);
            classCourseUpdateDO.setIsMerge(false);
            classCourseMapper.updateById(classCourseUpdateDO);
        }
    }

    /**
     * 添加排课关联关系
     *
     * @param updateReqVO 更新请求对象，包含需要更新的班级课程信息
     * @param classCourseDO 班级课程数据对象，代表当前操作的课程信息
     * @param classManagementDO 班级管理数据对象，提供班级名称等信息
     */
    private void addBindingRelation(ClassCourseBindingUpdateReqVO updateReqVO, ClassCourseDO classCourseDO, ClassManagementDO classManagementDO) {
        LambdaQueryWrapper<ClassCourseDO> classCourseDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 查询给定班级下是否有授课时间与原课表格子开始时间、结束时间的小时、分钟相等的格子
        if(classCourseDO.getBeginTime() != null && classCourseDO.getEndTime() != null){
            classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getDate, classCourseDO.getDate())
                    .eq(ClassCourseDO::getClassId, updateReqVO.getClassId())
                    .apply("to_char(begin_time, 'HH24:MI') = to_char({0}, 'HH24:MI')", classCourseDO.getBeginTime())
                    .apply("to_char(end_time, 'HH24:MI') = to_char({0}, 'HH24:MI')", classCourseDO.getEndTime());
        }else{
            classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getDate, classCourseDO.getDate())
                    .eq(ClassCourseDO::getClassId, updateReqVO.getClassId())
                    .eq(ClassCourseDO::getDate, classCourseDO.getDate())
                    .eq(ClassCourseDO::getPeriod, classCourseDO.getPeriod())
                    .isNull(ClassCourseDO::getBeginTime)
                    .isNull(ClassCourseDO::getEndTime);
            // 1.4.0暂时的逻辑：只有有时间安排的格子才能合班，否则无法合班，后续不排除解开上面四行的注释，在无时间安排时根据午别合班
//            return;
        }

        List<ClassCourseDO> classCourseDOList = classCourseMapper.selectList(classCourseDOLambdaQueryWrapper);
        if(!classCourseDOList.isEmpty()){
            //1、有格子则判断是否已排课，或者已排课则不更换，未排课则把教师、课程等信息都同步过去
            ClassCourseDO classCourseTargetDO = classCourseDOList.get(0);
            if(classCourseTargetDO.getCourseId()!=null){
                //1.1、已排课
                if(!classCourseDOList.get(0).getCourseId().equals(classCourseDO.getCourseId())){
                    //1.1.1、已排课且排课不是同一个课程，则提示冲突
                    throw exception(CLASS_COURSE_CONFLICT, classManagementDO.getClassName(), classManagementDO.getClassName());
                }else{
                    //已排课、同一个课程，但教师不同，也提示冲突
                    if(!isSameTeachers(classCourseDOList.get(0),classCourseDO)){
                        throw exception(CLASS_COURSE_CONFLICT, classManagementDO.getClassName(), classManagementDO.getClassName());
                    }
                    //1.1.2、已排课且排课是同一个课程，则打开合班按钮
                    classCourseTargetDO.setIsMerge(true);
                }
            }else {
                //1.2、未排课（将对应必要属性复制过来，更新后需要新增关联关系）
                classCourseTargetDO.setCourseId(classCourseDO.getCourseId());
                classCourseTargetDO.setTeacherId(classCourseDO.getTeacherId());
                classCourseTargetDO.setClassroomId(classCourseDO.getClassroomId());
                classCourseTargetDO.setIsMerge(true);
                classCourseTargetDO.setDepartment(classCourseDO.getDepartment());
                classCourseTargetDO.setTeacherIdString(classCourseDO.getTeacherIdString());
                classCourseTargetDO.setDeptId(classCourseDO.getDeptId());
                //更新授课教师对应绑定关系
                List<ClassCourseDO> changeTeacherList = new ArrayList<>();
                changeTeacherList.add(classCourseTargetDO);
                changeClassCourseTeacher(changeTeacherList);
            }
//            classCourseTargetDO.setIsTemporary(false);
            classCourseMapper.updateById(classCourseTargetDO);
        }else{
            //2、没有格子则给该班级增加一个排课且发布非暂存（没有格子则新增一个格子，需要判断时间冲突，直接调用原来的新增午别方法，并排课相同的课，注意授课教师也要更换）
            ClassCourseCreateReqVO classCourseCreateReqVO = new ClassCourseCreateReqVO();
            classCourseCreateReqVO.setClassId(updateReqVO.getClassId());
            classCourseCreateReqVO.setBeginTime(classCourseDO.getBeginTime());
            classCourseCreateReqVO.setEndTime(classCourseDO.getEndTime());
            classCourseCreateReqVO.setDate(classCourseDO.getDate());
            classCourseCreateReqVO.setPeriod(classCourseDO.getPeriod());
            //查询该班级对应查询到的课表格子的日期，是否存在教学计划包围其中
            LambdaQueryWrapper<PlanDO> planDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            planDOLambdaQueryWrapper.eq(PlanDO::getClassId, updateReqVO.getClassId());
            List<PlanDO> planDOList = planMapper.selectList(planDOLambdaQueryWrapper);
            //筛选planDOList中每个对象的beginDate与endDate字段，筛选出与classCourseDO的date字段在期间的planDO
            List<PlanDO> planDOListFiltered = planDOList.stream().filter(planDO -> {
                try {
                    return planDO.getBeginDate().compareTo(classCourseDO.getDate()) <= 0 && planDO.getEndDate().compareTo(classCourseDO.getDate()) >= 0;
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
                return false;
            }).collect(Collectors.toList());
            if(!planDOListFiltered.isEmpty()){
                classCourseCreateReqVO.setPlanId(planDOListFiltered.get(0).getId());
            }
            Long targetId = createClassCourse(classCourseCreateReqVO);
            ClassCourseDO classCourseUpdateDO = new ClassCourseDO();
            //1.2、未排课（将对应必要属性复制过来，更新后需要新增关联关系）
            classCourseUpdateDO.setId(targetId);
            classCourseUpdateDO.setCourseId(classCourseDO.getCourseId());
            classCourseUpdateDO.setTeacherId(classCourseDO.getTeacherId());
            classCourseUpdateDO.setClassroomId(classCourseDO.getClassroomId());
            classCourseUpdateDO.setIsMerge(true);
            classCourseUpdateDO.setDepartment(classCourseDO.getDepartment());
            classCourseUpdateDO.setTeacherIdString(classCourseDO.getTeacherIdString());
            classCourseUpdateDO.setDeptId(classCourseDO.getDeptId());
//            classCourseUpdateDO.setIsTemporary(false);
            classCourseMapper.updateById(classCourseUpdateDO);
            //更新授课教师对应绑定关系
            List<ClassCourseDO> changeTeacherList = new ArrayList<>();
            changeTeacherList.add(classCourseUpdateDO);
            changeClassCourseTeacher(changeTeacherList);
        }
    }

    /**
     * 判断两个课程对象是否由相同的教师团队教授
     * 此方法主要用于比较两个课程对象的教师ID集合是否完全一致
     * 它首先将每个课程对象的教师ID字符串解析为一个集合，然后比较这两个集合是否相等
     *
     * @param a 第一个课程对象，用于比较教师团队
     * @param b 第二个课程对象，与第一个课程对象比较教师团队
     * @return 如果两个课程由相同的教师团队教授，则返回true；否则返回false
     */
    public boolean isSameTeachers(ClassCourseDO a, ClassCourseDO b) {
        Set<String> setA = parseTeacherIdString(a.getTeacherIdString());
        Set<String> setB = parseTeacherIdString(b.getTeacherIdString());
        return setA.equals(setB);
    }

    /**
     * 将教师ID字符串解析为教师ID的集合
     * 此方法处理一个包含教师ID的逗号分隔字符串，将其转换为一个去空格且无重复的字符串集合
     * 如果输入字符串为空或null，将返回一个空集合
     *
     * @param teacherIdString 一个包含教师ID的逗号分隔字符串
     * @return 一个包含教师ID的字符串集合，已经去除了前后空格并且没有重复项
     */
    private Set<String> parseTeacherIdString(String teacherIdString) {
        if (teacherIdString == null || teacherIdString.trim().isEmpty()) {
            return Collections.emptySet();
        }
        return Arrays.stream(teacherIdString.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet());
    }

    @Override
    public List<ClassManagementRespVO> getClassIdMergeSelect(Long classCourseId){
        ClassCourseDO classCourseDO = classCourseMapper.selectById(classCourseId);
        if (classCourseDO == null) {
            throw exception(CLASS_COURSE_NOT_EXISTS);
        }
        //查询classCourseDO的date处于开班日期和结业日期之间的班级
        LocalDate courseDate = LocalDate.parse(classCourseDO.getDate());
        LambdaQueryWrapper<ClassManagementDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.le(ClassManagementDO::getClassOpenTime, courseDate.atTime(LocalTime.MAX));
        wrapper.ge(ClassManagementDO::getCompletionTime, courseDate.atStartOfDay());
        wrapper.eq(ClassManagementDO::getPublish, PublishEnum.ON.getCode());
        // 查询班级列表
        List<ClassManagementDO> classList = classManagementMapper.selectList(wrapper);
        return ClassManagementConvert.INSTANCE.convertList(classList);
    }

    /**
     * 获取一个排课时段的合班授课的班级列表
     *
     * @param reqVO 合班信息信息
     * @return 合班选课的班级列表
     */
    @Override
    public List<ClassInfoRespVO> getMergedClassListByReqVO(ClassMergeReqVO reqVO) {
        // 截取时分秒为0，只保留年月日时分
        if(reqVO.getBeginTime() != null && reqVO.getEndTime() != null){
            reqVO.setBeginTime(truncateToMinute(reqVO.getBeginTime()));
            reqVO.setEndTime(truncateToMinute(reqVO.getEndTime()));
        }
        List<ClassCourseClassNameRespDTO> classNameRespDTOS = classCourseMapper.getMergedClassListByReqVO(reqVO);
        return classNameRespDTOS.stream().map(o -> {
            ClassInfoRespVO classInfoRespVO = new ClassInfoRespVO();
            classInfoRespVO.setId(o.getClassId());
            classInfoRespVO.setName(o.getClassName());
            return classInfoRespVO;
        }).collect(Collectors.toList());
    }

    /**
     * 撤销已发布的课程
     * 该方法将根据班级ID撤销尚未开始的课程发布状态，将其标记为暂存状态
     *
     * @param classId 班级ID，用于标识需要撤销发布的班级
     */
    @Override
    public void undoPublish(Long classId){
        LambdaQueryWrapper<ClassCourseDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ClassCourseDO::getClassId, classId);
        //筛选课程开始时间晚于当前的课表格子
        wrapper.ge(ClassCourseDO::getBeginTime, LocalDateTime.now());
        List<ClassCourseDO> classCourseDOList = classCourseMapper.selectList(wrapper);
        //撤销发布，把所有筛选出来的课表格子的发布状态变为暂存并更新
        List<Long> classCourseIdList = classCourseDOList.stream().map(ClassCourseDO::getId).collect(Collectors.toList());
        LambdaUpdateWrapper<ClassCourseDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ClassCourseDO::getIsTemporary, true);
        if(!classCourseIdList.isEmpty()){
            updateWrapper.in(ClassCourseDO::getId, classCourseIdList);
            classCourseMapper.update(null, updateWrapper);
        }

    }

    /**
     * 获取班级课程安排列表
     * 该方法用于获取班级课程安排列表，根据传入的分页参数和查询条件返回符合条件的班级课程安排列表
     *
     * @param pageVO 分页参数，用于控制返回的班级课程安排列表的分页信息
     * @return List<ClassCourseByClassManagementRespVO> 班级课程安排列表
     *         返回一个班级课程安排列表，列表中的每个元素都包含有关该班级课程安排信息的字段
     */
    @Override
    public List<ClassCourseByClassManagementRespVO> getClassCoursePageByClassManagement(ClassCoursePageReqVO pageVO){
        List<ClassCourseByClassManagementRespVO> resultList = new ArrayList<>();
        //获取当前租户下的所有班级信息（根据校区进行筛选）
        LambdaQueryWrapper<ClassManagementDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ClassManagementDO::getPublish, PublishEnum.ON.getCode());
        if(pageVO.getCampus() != null){
//            wrapper.eq(ClassManagementDO::getCampus, pageVO.getCampus());
            wrapper.apply("position(',' || {0} || ',' in ',' || campus_ids || ',') > 0", pageVO.getCampus());
        }
        LocalDateTime[] betweenTime = pageVO.getBetweenTime();
        if (betweenTime != null && betweenTime.length == 2) {
            LocalDateTime startTime = betweenTime[0];
            LocalDateTime endTime = betweenTime[1];
            wrapper.le(ClassManagementDO::getClassOpenTime, endTime)
                    .ge(ClassManagementDO::getCompletionTime, startTime);
        }

        //加上按 id 升序排序
        wrapper.orderByAsc(ClassManagementDO::getId);

        List<ClassManagementDO> classManagementDOList = classManagementMapper.selectList(wrapper);

        List<Long> allTeacherIds = new ArrayList<>();

        // 获取需要用到的所有老师Id
        classManagementDOList.forEach(item -> {
            if (item.getClassTeacherLead() != null) {
                allTeacherIds.add(item.getClassTeacherLead());
            }
            if (StringUtils.isNotBlank(item.getCoachTeacher())) {
                // 使用逗号分割字符串
                Arrays.stream(item.getCoachTeacher().split(",")).map(Long::parseLong).forEach(allTeacherIds::add);
            }
            if (StringUtils.isNotBlank(item.getAdministrativeTeachers())) {
                // 使用逗号分割字符串
                Arrays.stream(item.getAdministrativeTeachers().split(",")).map(Long::parseLong).forEach(allTeacherIds::add);
            }
        });
        // 获取所有老师信息，并以id为key，名字为value的map
        Map<Long, String> IdToTeacherName;
        if (!allTeacherIds.isEmpty()){
            IdToTeacherName = teacherInformationMapper.selectDOListByIds(allTeacherIds).stream()
                    .collect(Collectors.toMap(TeacherInformationDO::getId, TeacherInformationDO::getName));
        }else{
            IdToTeacherName = new HashMap<>();
        }

        List<String> dict = new ArrayList<>();
        dict.add(ClassManageDictTypeEnum.CLASS_TYPE.getType());
        List<DictDataRespDTO> dictList = dictDataApi.getByDictTypes(dict).getCheckedData();
        Map<Long, String> classTypeMap = dictList.stream()
                .collect(Collectors.toMap(DictDataRespDTO::getId, DictDataRespDTO::getLabel, (k1, k2) -> k1));

        // 1、创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        // 2、多线程计数器
        CountDownLatch latch = new CountDownLatch(classManagementDOList.size());
        classManagementDOList.forEach(classManagementDO -> {
            executorService.submit(() -> {
                try {
                    ClassCoursePageReqVO pageVOTemp = new ClassCoursePageReqVO();
                    BeanUtils.copyProperties(pageVO,pageVOTemp);

                    //betweenTime赋值给timeRange并将betweenTime置空，因为课程的beginTime和endTime为空，所以不能根据betweenTime查找beginTime和endTime的数据，重新增加timeRange字段对date进行范围查找
                    pageVOTemp.setTimeRange(pageVO.getBetweenTime());
                    pageVOTemp.setBetweenTime(null);
                    ClassCourseByClassManagementRespVO classCourseByClassManagementRespVO = new ClassCourseByClassManagementRespVO();
                    //补充班级属性信息
                    classCourseByClassManagementRespVO.setClassId(classManagementDO.getId());
                    classCourseByClassManagementRespVO.setClassName(classManagementDO.getClassName());
                    classCourseByClassManagementRespVO.setClassTeacherLeadId(classManagementDO.getClassTeacherLead());
                    classCourseByClassManagementRespVO.setClassOpenTime(classManagementDO.getClassOpenTime());
                    classCourseByClassManagementRespVO.setCompletionTime(classManagementDO.getCompletionTime());
                    //班主任可能为空
                    String classTeacherLead = null;
                    if (classManagementDO.getClassTeacherLead() != null) {
                        classTeacherLead = IdToTeacherName.getOrDefault(classManagementDO.getClassTeacherLead(), null);
                    }
                    classCourseByClassManagementRespVO.setClassTeacherLeadName(classTeacherLead);
                    classCourseByClassManagementRespVO.setClassTeacherLeadId(classManagementDO.getClassTeacherLead());
                    // 教务老师
                    classCourseByClassManagementRespVO.setEduTeacherIds(classManagementDO.getAdministrativeTeachers());
                    List<Long> administrativeTeacherIds = new ArrayList<>();
                    if (StringUtils.isNotBlank(classManagementDO.getAdministrativeTeachers())) {
                        // 使用逗号分割字符串
                        Arrays.stream(classManagementDO.getAdministrativeTeachers().split(",")).map(Long::parseLong).forEach(administrativeTeacherIds::add);
                    }
                    if (!administrativeTeacherIds.isEmpty()){
                        classCourseByClassManagementRespVO.setEduTeacherName(administrativeTeacherIds.stream().filter(IdToTeacherName::containsKey)
                                .map(IdToTeacherName::get).collect(Collectors.joining(", ")));
                    }
                    classCourseByClassManagementRespVO.setClassType(classManagementDO.getClassType());
                    //办班类型
                    Long classTypeDictId = classManagementDO.getClassTypeDictId() != null ? Long.valueOf(classManagementDO.getClassTypeDictId()) : null;
                    classCourseByClassManagementRespVO.setClassTypeDictName(classTypeMap.getOrDefault(classTypeDictId, StrUtil.EMPTY));
                    classCourseByClassManagementRespVO.setClassPeopleCount(classManagementMapper.getClassPeopleCount(classManagementDO.getId()));
                    //办学地点 即校区
                    // 设置校区名称
                    classCourseByClassManagementRespVO.setSchoolLocation(eduCommonService.getCampusNamesByIds(classManagementDO.getCampusIds()));
                    //获取课表信息
                    pageVOTemp.setClassId(classManagementDO.getId());
                    PageResult<ClassCourseDO> classCoursePage = getClassCoursePage(pageVOTemp);
                    classCourseByClassManagementRespVO.setClassCourseVOList(ClassCourseConvert.INSTANCE.convertList(classCoursePage.getList()));
                    if(CollectionUtil.isNotEmpty(pageVOTemp.getTeacherIds())){
                        classCourseByClassManagementRespVO.setClassCourseVOList(
                                classCourseByClassManagementRespVO.getClassCourseVOList().stream()
                                        .filter(o -> {
                                            if (CollectionUtils.isEmpty(pageVOTemp.getTeacherIds())) {
                                                // 若没有筛选条件，则不过滤
                                                return true;
                                            }
                                            if (StrUtil.isNotBlank(o.getTeacherIdString())) {
                                                Set<Long> teacherIdSet = Arrays.stream(o.getTeacherIdString().split(","))
                                                        .map(String::trim)
                                                        .filter(StrUtil::isNotBlank)
                                                        .map(Long::parseLong)
                                                        .collect(Collectors.toSet());
                                                for (Long id : pageVOTemp.getTeacherIds()) {
                                                    if (teacherIdSet.contains(id)) {
                                                        return true;
                                                    }
                                                }
                                            }
                                            return false;
                                        }).collect(Collectors.toList())
                        );
                    }
                    resultList.add(classCourseByClassManagementRespVO);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }finally {
                    latch.countDown();
                }
            });
        });
        // 3. 主线程等待所有任务完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        // 4. 关闭线程池
        executorService.shutdown();
        return resultList;
    }

    /**
     * 获取与课程班级相关的人员信息，包括班主任、任课教师等。
     *
     * @param list 课程班级信息列表，用于提取班级ID和教师ID字符串
     * @return 包含相关教师信息的VO对象，如班主任ID、姓名及所有任课教师ID和姓名列表
     */
    @Override
    public ClassCourseRelatedPeopleInfoVO getRelatedPeopleInfo(List<ClassCourseDO> list){
        ClassCourseRelatedPeopleInfoVO relatedPeopleInfoVO = new ClassCourseRelatedPeopleInfoVO();
        if(CollectionUtil.isEmpty(list)){
            return relatedPeopleInfoVO;
        }

        // 班主任及教师信息
        ClassManagementDO classManagementDO = classManagementMapper.selectById(list.get(0).getClassId());
        if(classManagementDO != null && classManagementDO.getClassTeacherLead() != null){
            relatedPeopleInfoVO.setClassTeacherLead(classManagementDO.getClassTeacherLead());
            TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectById(classManagementDO.getClassTeacherLead());
            relatedPeopleInfoVO.setClassTeacherLeadName(teacherInformationDO.getName());
        }

        // 所有课表格子关联的教师信息，根据teacherIdString字段进行拆分
        // 收集所有教师ID（去重）
        Set<Long> allTeacherIds = new HashSet<>();
        for (ClassCourseDO course : list) {
            String teacherIdString = course.getTeacherIdString();
            if (StrUtil.isNotBlank(teacherIdString)) {
                String[] idArray = teacherIdString.split(",");
                for (String idStr : idArray) {
                    try {
                        Long teacherId = Long.valueOf(idStr.trim());
                        allTeacherIds.add(teacherId);
                    } catch (NumberFormatException e) {
                        // 非数字字符串跳过（例如部门名称）
                        log.warn("无效的教师ID: {}", idStr);
                    }
                }
            }
        }

        // 批量查询教师信息并转为Map
        Map<Long, TeacherInformationDO> teacherMap = CollectionUtil.isNotEmpty(allTeacherIds)
                ? teacherInformationMapper.selectBatchIds(allTeacherIds)
                .stream()
                .collect(Collectors.toMap(TeacherInformationDO::getId, Function.identity()))
                : Collections.emptyMap();

        // 匹配教师名称列表
        Set<String> teacherNames = new LinkedHashSet<>();
        for (Long teacherId : allTeacherIds) {
            TeacherInformationDO teacher = teacherMap.get(teacherId);
            if (teacher != null) {
                teacherNames.add(teacher.getName());
            }
        }

        relatedPeopleInfoVO.setTeacherIdList(new ArrayList<>(allTeacherIds));
        relatedPeopleInfoVO.setTeacherNameList(new ArrayList<>(teacherNames));

        // 现场教学点信息 现场教学点信息在创建的时候会同步到教室管理库，并在教室管理库多一个是否现场教学点字段。这里可以利用所有的教室id查询到所有的教室，然后筛选出对应的现场教学点返回给前端，发短信的时候也是一样的，要看一下这里是不是现场教学点，如果是现场教学点且勾选了，则也要发短信给教学点的联系人
        // 匹配现场教学点信息
        Set<Integer> classroomIds = list.stream()
                .map(ClassCourseDO::getClassroomId)
                .filter(Objects::nonNull)
                .map(Long::intValue) // classroomId 是 Integer 类型
                .collect(Collectors.toSet());

        Map<Integer, ClassroomLibraryDO> classroomMap = CollectionUtil.isNotEmpty(classroomIds)
                ? classroomLibraryMapper.selectBatchIds(classroomIds)
                .stream()
                .collect(Collectors.toMap(ClassroomLibraryDO::getId, Function.identity()))
                : Collections.emptyMap();

        Set<Long> locationIdSet = new HashSet<>();
        Set<String> locationNameSet = new LinkedHashSet<>();

        for (Integer classroomId : classroomIds) {
            ClassroomLibraryDO classroom = classroomMap.get(classroomId);
            if (classroom != null && Boolean.TRUE.equals(classroom.getTeachingPoint())) {
                locationIdSet.add(classroom.getId().longValue());
                locationNameSet.add(classroom.getClassName());
            }
        }

        relatedPeopleInfoVO.setLocationIdList(new ArrayList<>(locationIdSet));
        relatedPeopleInfoVO.setLocationNameList(new ArrayList<>(locationNameSet));

        return relatedPeopleInfoVO;

    }

}
