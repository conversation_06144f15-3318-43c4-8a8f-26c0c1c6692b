package com.unicom.swdx.module.edu.controller.admin.teachercourseinformation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("管理后台 - 师资-任课信息中间批量创建 Request VO")
@Data

public class TeacherCourseInformationBatchCreateReqVO {

    @ApiModelProperty(value = "课程ID", required = true)
    @NotNull(message = "课程ID不能为空")
    private List<Long> coursesIds;

    @ApiModelProperty(value = "教师ID", required = true)
    @NotNull(message = "教师ID不能为空")
    private Long teacherId;

}
