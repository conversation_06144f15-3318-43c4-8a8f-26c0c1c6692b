package com.unicom.swdx.module.edu.service.classcourseteacher;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.unicom.swdx.framework.test.core.ut.BaseDbUnitTest;

import com.unicom.swdx.module.edu.controller.admin.classcourseteacher.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classcourseteacher.ClassCourseTeacherDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourseteacher.ClassCourseTeacherMapper;
import com.unicom.swdx.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.framework.test.core.util.AssertUtils.*;
import static com.unicom.swdx.framework.test.core.util.RandomUtils.*;
import static com.unicom.swdx.framework.common.util.object.ObjectUtils.*;
import static com.unicom.swdx.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link ClassCourseTeacherServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(ClassCourseTeacherServiceImpl.class)
public class ClassCourseTeacherServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ClassCourseTeacherServiceImpl classCourseTeacherService;

    @Resource
    private ClassCourseTeacherMapper classCourseTeacherMapper;

    @Test
    public void testCreateClassCourseTeacher_success() {
        // 准备参数
        ClassCourseTeacherCreateReqVO reqVO = randomPojo(ClassCourseTeacherCreateReqVO.class);

        // 调用
        Long classCourseTeacherId = classCourseTeacherService.createClassCourseTeacher(reqVO);
        // 断言
        assertNotNull(classCourseTeacherId);
        // 校验记录的属性是否正确
        ClassCourseTeacherDO classCourseTeacher = classCourseTeacherMapper.selectById(classCourseTeacherId);
        assertPojoEquals(reqVO, classCourseTeacher);
    }

    @Test
    public void testUpdateClassCourseTeacher_success() {
        // mock 数据
        ClassCourseTeacherDO dbClassCourseTeacher = randomPojo(ClassCourseTeacherDO.class);
        classCourseTeacherMapper.insert(dbClassCourseTeacher);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ClassCourseTeacherUpdateReqVO reqVO = randomPojo(ClassCourseTeacherUpdateReqVO.class, o -> {
            o.setId(dbClassCourseTeacher.getId()); // 设置更新的 ID
        });

        // 调用
        classCourseTeacherService.updateClassCourseTeacher(reqVO);
        // 校验是否更新正确
        ClassCourseTeacherDO classCourseTeacher = classCourseTeacherMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, classCourseTeacher);
    }

    @Test
    public void testUpdateClassCourseTeacher_notExists() {
        // 准备参数
        ClassCourseTeacherUpdateReqVO reqVO = randomPojo(ClassCourseTeacherUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> classCourseTeacherService.updateClassCourseTeacher(reqVO), CLASS_COURSE_TEACHER_NOT_EXISTS);
    }

    @Test
    public void testDeleteClassCourseTeacher_success() {
        // mock 数据
        ClassCourseTeacherDO dbClassCourseTeacher = randomPojo(ClassCourseTeacherDO.class);
        classCourseTeacherMapper.insert(dbClassCourseTeacher);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbClassCourseTeacher.getId();

        // 调用
        classCourseTeacherService.deleteClassCourseTeacher(id);
       // 校验数据不存在了
       assertNull(classCourseTeacherMapper.selectById(id));
    }

    @Test
    public void testDeleteClassCourseTeacher_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> classCourseTeacherService.deleteClassCourseTeacher(id), CLASS_COURSE_TEACHER_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetClassCourseTeacherPage() {
       // mock 数据
       ClassCourseTeacherDO dbClassCourseTeacher = randomPojo(ClassCourseTeacherDO.class, o -> { // 等会查询到
           o.setClassCourseId(null);
           o.setTeacherId(null);
           o.setCreateTime(null);
       });
       classCourseTeacherMapper.insert(dbClassCourseTeacher);
       // 测试 classCourseId 不匹配
       classCourseTeacherMapper.insert(cloneIgnoreId(dbClassCourseTeacher, o -> o.setClassCourseId(null)));
       // 测试 teacherId 不匹配
       classCourseTeacherMapper.insert(cloneIgnoreId(dbClassCourseTeacher, o -> o.setTeacherId(null)));
       // 测试 createTime 不匹配
       classCourseTeacherMapper.insert(cloneIgnoreId(dbClassCourseTeacher, o -> o.setCreateTime(null)));
       // 准备参数
       ClassCourseTeacherPageReqVO reqVO = new ClassCourseTeacherPageReqVO();
       reqVO.setClassCourseId(null);
       reqVO.setTeacherId(null);
//       reqVO.setCreateTime((new LocalDateTime[]{}));

       // 调用
       PageResult<ClassCourseTeacherDO> pageResult = classCourseTeacherService.getClassCourseTeacherPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbClassCourseTeacher, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetClassCourseTeacherList() {
       // mock 数据
       ClassCourseTeacherDO dbClassCourseTeacher = randomPojo(ClassCourseTeacherDO.class, o -> { // 等会查询到
           o.setClassCourseId(null);
           o.setTeacherId(null);
           o.setCreateTime(null);
       });
       classCourseTeacherMapper.insert(dbClassCourseTeacher);
       // 测试 classCourseId 不匹配
       classCourseTeacherMapper.insert(cloneIgnoreId(dbClassCourseTeacher, o -> o.setClassCourseId(null)));
       // 测试 teacherId 不匹配
       classCourseTeacherMapper.insert(cloneIgnoreId(dbClassCourseTeacher, o -> o.setTeacherId(null)));
       // 测试 createTime 不匹配
       classCourseTeacherMapper.insert(cloneIgnoreId(dbClassCourseTeacher, o -> o.setCreateTime(null)));
       // 准备参数
       ClassCourseTeacherExportReqVO reqVO = new ClassCourseTeacherExportReqVO();
       reqVO.setClassCourseId(null);
       reqVO.setTeacherId(null);
//       reqVO.setCreateTime((new LocalDateTime[]{}));

       // 调用
       List<ClassCourseTeacherDO> list = classCourseTeacherService.getClassCourseTeacherList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbClassCourseTeacher, list.get(0));
    }

}
