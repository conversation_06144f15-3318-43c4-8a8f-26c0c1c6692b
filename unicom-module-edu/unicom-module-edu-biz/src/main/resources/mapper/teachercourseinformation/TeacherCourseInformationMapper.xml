<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.teachercourseinformation.TeacherCourseInformationMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getSimpleListByCourseId"
            resultType="com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationSimpleRespVO">
        select
            eti.id id,
            eti."name"
        from
            edu_teacher_course_information etci
                join edu_teacher_information eti on etci.teacher_id = eti.id and eti.deleted = 0
        where
            etci.deleted =0
          and etci.courses_id = #{coursesId}
    </select>
    <select id="selectListByCourseType"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.teachercourseinformation.TeacherCourseInformationDO"
            parameterType="java.lang.Integer">
        select
            etci.id,
            etci.courses_id,
            etci.teacher_id
        from
            edu_teacher_course_information etci
                join edu_courses ec on etci.courses_id = ec.id and ec.deleted = 0 and ec.courses_type = #{coursesType}
        where etci.deleted = 0
    </select>
</mapper>
