package com.unicom.swdx.module.edu.service.traineeleave;

import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassSimpleInfo;
import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.*;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgSendReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveDO;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveProcessDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.traineeleave.TraineeLeaveMapper;
import com.unicom.swdx.module.edu.dal.mysql.traineeleave.TraineeLeaveProcessMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.service.clockininfo.ClockInInfoService;
import com.unicom.swdx.module.edu.service.todoitems.TodoItemsService;
import com.unicom.swdx.module.edu.service.xcxMsg.XcxMsgService;
import com.unicom.swdx.module.infra.api.file.FileApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TraineeLeaveServiceImplTest {

    @Mock
    private TraineeLeaveMapper mockLeaveMapper;
    @Mock
    private TraineeLeaveProcessMapper mockLeaveProcessMapper;
    @Mock
    private ClassManagementMapper mockClassManagementMapper;
    @Mock
    private TraineeMapper mockTraineeMapper;
    @Mock
    private AdminUserApi mockUserApi;
    @Mock
    private FileApi mockFileApi;
    @Mock
    private TodoItemsService mockTodoItemsService;
    @Mock
    private ClockInInfoService mockClockInInfoService;
    @Mock
    private XcxMsgService mockXcxMsgService;

    @InjectMocks
    private TraineeLeaveServiceImpl traineeLeaveServiceImplUnderTest;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(traineeLeaveServiceImplUnderTest, "MID_BASE_URI", "MID_BASE_URI");
    }

    @Test
    public void testGetDraft() {
        // Setup
        final TraineeLeaveRespVO expectedResult = new TraineeLeaveRespVO();
        expectedResult.setStartTimeStr("startTimeStr");
        expectedResult.setEndTimeStr("endTimeStr");
        expectedResult.setClassId(0L);
        expectedResult.setAccessory("accessory");
        expectedResult.setTraineeName("traineeName");
        expectedResult.setClassTeacherLeadName("userName");
        final TraineeLeaveProcessVO traineeLeaveProcessVO = new TraineeLeaveProcessVO();
        expectedResult.setProcessList(Arrays.asList(traineeLeaveProcessVO));

        // Configure TraineeMapper.selectById(...).
        final TraineeDO traineeDO = TraineeDO.builder()
                .id(0L)
                .name("name")
                .classId(0L)
                .status(0)
                .userId(0L)
                .build();
        when(mockTraineeMapper.selectById(0L)).thenReturn(traineeDO);

        // Configure ClassManagementMapper.selectSimpleClassInfo(...).
        final ClassSimpleInfo classSimpleInfo = new ClassSimpleInfo();
        classSimpleInfo.setId(0L);
        classSimpleInfo.setClassName("className");
        classSimpleInfo.setClassNameCode("classNameCode");
        classSimpleInfo.setClassTeacherLeadName("userName");
        classSimpleInfo.setTeacherUserId(0L);
        when(mockClassManagementMapper.selectSimpleClassInfo(0L)).thenReturn(classSimpleInfo);

        // Run the test
        final TraineeLeaveRespVO result = traineeLeaveServiceImplUnderTest.getDraft(0L);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSaveDraft() {
        // Setup
        final TraineeLeaveBaseVO reqVO = new TraineeLeaveBaseVO();
        reqVO.setStartTimeStr("startTimeStr");
        reqVO.setEndTimeStr("endTimeStr");
        reqVO.setTitle("title");
        reqVO.setClassId(0L);
        reqVO.setAccessory("accessory");

        when(mockTraineeMapper.getTraineeId(0L, 0L)).thenReturn(0L);

        // Run the test
        final Long result = traineeLeaveServiceImplUnderTest.saveDraft(reqVO);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
        verify(mockLeaveMapper).delete(any(LambdaQueryWrapperX.class));
    }

    @Test
    public void testCreateLeave() {
        // Setup
        final TraineeLeaveCreateReqVO reqVO = new TraineeLeaveCreateReqVO();
        reqVO.setStartTimeStr("startTimeStr");
        reqVO.setEndTimeStr("endTimeStr");
        reqVO.setClassId(0L);
        reqVO.setAccessory("accessory");
        reqVO.setId(0L);

        // Configure TraineeMapper.selectOne(...).
        final TraineeDO traineeDO = TraineeDO.builder()
                .id(0L)
                .name("name")
                .classId(0L)
                .status(0)
                .userId(0L)
                .build();
        when(mockTraineeMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(traineeDO);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("userName");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure ClassManagementMapper.selectSimpleClassInfo(...).
        final ClassSimpleInfo classSimpleInfo = new ClassSimpleInfo();
        classSimpleInfo.setId(0L);
        classSimpleInfo.setClassName("className");
        classSimpleInfo.setClassNameCode("classNameCode");
        classSimpleInfo.setClassTeacherLeadName("userName");
        classSimpleInfo.setTeacherUserId(0L);
        when(mockClassManagementMapper.selectSimpleClassInfo(0L)).thenReturn(classSimpleInfo);

        // Run the test
        final Long result = traineeLeaveServiceImplUnderTest.createLeave(reqVO);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
        verify(mockLeaveMapper).delete(any(LambdaQueryWrapperX.class));
        verify(mockLeaveMapper).deleteById(0L);
        verify(mockLeaveProcessMapper).insertBatch(Arrays.asList(TraineeLeaveProcessDO.builder()
                .leaveId(0L)
                .userId(0L)
                .userName("userName")
                .taskName("taskName")
                .taskStatus(0)
                .taskComment("taskComment")
                .userType("userType")
                .operateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build()));

        // Confirm TodoItemsService.addTraineeLeaveTodoItem(...).
        final TraineeLeaveCreateReqVO reqVO1 = new TraineeLeaveCreateReqVO();
        reqVO1.setStartTimeStr("startTimeStr");
        reqVO1.setEndTimeStr("endTimeStr");
        reqVO1.setClassId(0L);
        reqVO1.setAccessory("accessory");
        reqVO1.setId(0L);
        verify(mockTodoItemsService).addTraineeLeaveTodoItem(reqVO1);
    }

    @Test
    public void testCreateLeave_AdminUserApiReturnsError() {
        // Setup
        final TraineeLeaveCreateReqVO reqVO = new TraineeLeaveCreateReqVO();
        reqVO.setStartTimeStr("startTimeStr");
        reqVO.setEndTimeStr("endTimeStr");
        reqVO.setClassId(0L);
        reqVO.setAccessory("accessory");
        reqVO.setId(0L);

        // Configure TraineeMapper.selectOne(...).
        final TraineeDO traineeDO = TraineeDO.builder()
                .id(0L)
                .name("name")
                .classId(0L)
                .status(0)
                .userId(0L)
                .build();
        when(mockTraineeMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(traineeDO);

        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure ClassManagementMapper.selectSimpleClassInfo(...).
        final ClassSimpleInfo classSimpleInfo = new ClassSimpleInfo();
        classSimpleInfo.setId(0L);
        classSimpleInfo.setClassName("className");
        classSimpleInfo.setClassNameCode("classNameCode");
        classSimpleInfo.setClassTeacherLeadName("userName");
        classSimpleInfo.setTeacherUserId(0L);
        when(mockClassManagementMapper.selectSimpleClassInfo(0L)).thenReturn(classSimpleInfo);

        // Run the test
        final Long result = traineeLeaveServiceImplUnderTest.createLeave(reqVO);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
        verify(mockLeaveMapper).delete(any(LambdaQueryWrapperX.class));
        verify(mockLeaveMapper).deleteById(0L);
        verify(mockLeaveProcessMapper).insertBatch(Arrays.asList(TraineeLeaveProcessDO.builder()
                .leaveId(0L)
                .userId(0L)
                .userName("userName")
                .taskName("taskName")
                .taskStatus(0)
                .taskComment("taskComment")
                .userType("userType")
                .operateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build()));

        // Confirm TodoItemsService.addTraineeLeaveTodoItem(...).
        final TraineeLeaveCreateReqVO reqVO1 = new TraineeLeaveCreateReqVO();
        reqVO1.setStartTimeStr("startTimeStr");
        reqVO1.setEndTimeStr("endTimeStr");
        reqVO1.setClassId(0L);
        reqVO1.setAccessory("accessory");
        reqVO1.setId(0L);
        verify(mockTodoItemsService).addTraineeLeaveTodoItem(reqVO1);
    }

    @Test
    public void testGetNumber() {
        assertEquals(Integer.valueOf(0), traineeLeaveServiceImplUnderTest.getNumber());
    }

    @Test
    public void testCancelLeave() {
        // Setup
        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("userName");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Run the test
        final Boolean result = traineeLeaveServiceImplUnderTest.cancelLeave(0L);

        // Verify the results
        assertTrue(result);
        verify(mockLeaveProcessMapper).delete(any(LambdaQueryWrapperX.class));
        verify(mockTodoItemsService).updateTraineeLeaveTodoItem(0L, 0);
    }

    @Test
    public void testCancelLeave_AdminUserApiReturnsError() {
        // Setup
        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Run the test
        final Boolean result = traineeLeaveServiceImplUnderTest.cancelLeave(0L);

        // Verify the results
        assertTrue(result);
        verify(mockLeaveProcessMapper).delete(any(LambdaQueryWrapperX.class));
        verify(mockTodoItemsService).updateTraineeLeaveTodoItem(0L, 0);
    }

    @Test
    public void testUpdateAccessory() {
        // Setup
        final TraineeLeaveCreateReqVO reqVO = new TraineeLeaveCreateReqVO();
        reqVO.setStartTimeStr("startTimeStr");
        reqVO.setEndTimeStr("endTimeStr");
        reqVO.setClassId(0L);
        reqVO.setAccessory("accessory");
        reqVO.setId(0L);

        // Run the test
        final Boolean result = traineeLeaveServiceImplUnderTest.updateAccessory(reqVO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testPageList() {
        // Setup
        final TraineeLeavePageReqVO reqVO = new TraineeLeavePageReqVO();
        reqVO.setClassTeacherLeadId(0L);
        reqVO.setApplyTimeStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setApplyTimeEnd(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setTag(0);
        reqVO.setSeq(0);

        final TraineeLeaveRespVO respVO = new TraineeLeaveRespVO();
        respVO.setStartTimeStr("startTimeStr");
        respVO.setEndTimeStr("endTimeStr");
        respVO.setClassId(0L);
        respVO.setAccessory("accessory");
        respVO.setTraineeName("traineeName");
        respVO.setClassTeacherLeadName("userName");
        final TraineeLeaveProcessVO traineeLeaveProcessVO = new TraineeLeaveProcessVO();
        respVO.setProcessList(Arrays.asList(traineeLeaveProcessVO));
        final PageResult<TraineeLeaveRespVO> expectedResult = new PageResult<>(Arrays.asList(respVO), 0L);

        // Configure TraineeLeaveMapper.selectPageByReqVO(...).
        final TraineeLeaveRespVO respVO1 = new TraineeLeaveRespVO();
        respVO1.setStartTimeStr("startTimeStr");
        respVO1.setEndTimeStr("endTimeStr");
        respVO1.setClassId(0L);
        respVO1.setAccessory("accessory");
        respVO1.setTraineeName("traineeName");
        respVO1.setClassTeacherLeadName("userName");
        final TraineeLeaveProcessVO traineeLeaveProcessVO1 = new TraineeLeaveProcessVO();
        respVO1.setProcessList(Arrays.asList(traineeLeaveProcessVO1));
        final List<TraineeLeaveRespVO> traineeLeaveRespVOS = Arrays.asList(respVO1);
        final TraineeLeavePageReqVO reqVO1 = new TraineeLeavePageReqVO();
        reqVO1.setClassTeacherLeadId(0L);
        reqVO1.setApplyTimeStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setApplyTimeEnd(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setTag(0);
        reqVO1.setSeq(0);
        when(mockLeaveMapper.selectPageByReqVO(any(IPage.class), eq(reqVO1))).thenReturn(traineeLeaveRespVOS);

        // Run the test
        final PageResult<TraineeLeaveRespVO> result = traineeLeaveServiceImplUnderTest.pageList(reqVO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testPageList_TraineeLeaveMapperReturnsNoItems() {
        // Setup
        final TraineeLeavePageReqVO reqVO = new TraineeLeavePageReqVO();
        reqVO.setClassTeacherLeadId(0L);
        reqVO.setApplyTimeStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setApplyTimeEnd(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setTag(0);
        reqVO.setSeq(0);

        final TraineeLeaveRespVO respVO = new TraineeLeaveRespVO();
        respVO.setStartTimeStr("startTimeStr");
        respVO.setEndTimeStr("endTimeStr");
        respVO.setClassId(0L);
        respVO.setAccessory("accessory");
        respVO.setTraineeName("traineeName");
        respVO.setClassTeacherLeadName("userName");
        final TraineeLeaveProcessVO traineeLeaveProcessVO = new TraineeLeaveProcessVO();
        respVO.setProcessList(Arrays.asList(traineeLeaveProcessVO));
        final PageResult<TraineeLeaveRespVO> expectedResult = new PageResult<>(Arrays.asList(respVO), 0L);

        // Configure TraineeLeaveMapper.selectPageByReqVO(...).
        final TraineeLeavePageReqVO reqVO1 = new TraineeLeavePageReqVO();
        reqVO1.setClassTeacherLeadId(0L);
        reqVO1.setApplyTimeStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setApplyTimeEnd(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setTag(0);
        reqVO1.setSeq(0);
        when(mockLeaveMapper.selectPageByReqVO(any(IPage.class), eq(reqVO1))).thenReturn(Collections.emptyList());

        // Run the test
        final PageResult<TraineeLeaveRespVO> result = traineeLeaveServiceImplUnderTest.pageList(reqVO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetLeaveExportList() {
        // Setup
        final TraineeLeaveExportReqVO reqVO = new TraineeLeaveExportReqVO();
        reqVO.setApplyTimeStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setApplyTimeEnd(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setTag(0);
        reqVO.setSeq(0);
        reqVO.setIncludeColumnIndexes(new HashSet<>(Arrays.asList(0)));

        final TraineeLeaveExcelVO traineeLeaveExcelVO = new TraineeLeaveExcelVO();
        traineeLeaveExcelVO.setClassName("className");
        traineeLeaveExcelVO.setTitle("title");
        traineeLeaveExcelVO.setTraineeName("traineeName");
        traineeLeaveExcelVO.setPictures(new WriteCellData<>(CellDataTypeEnum.STRING, "stringValue"));
        traineeLeaveExcelVO.setAccessory("accessory");
        final List<TraineeLeaveExcelVO> expectedResult = Arrays.asList(traineeLeaveExcelVO);

        // Configure TraineeLeaveMapper.selectListByReqVO(...).
        final TraineeLeaveRespVO respVO = new TraineeLeaveRespVO();
        respVO.setStartTimeStr("startTimeStr");
        respVO.setEndTimeStr("endTimeStr");
        respVO.setClassId(0L);
        respVO.setAccessory("accessory");
        respVO.setTraineeName("traineeName");
        respVO.setClassTeacherLeadName("userName");
        final TraineeLeaveProcessVO traineeLeaveProcessVO = new TraineeLeaveProcessVO();
        respVO.setProcessList(Arrays.asList(traineeLeaveProcessVO));
        final List<TraineeLeaveRespVO> traineeLeaveRespVOS = Arrays.asList(respVO);
        final TraineeLeaveExportReqVO reqVO1 = new TraineeLeaveExportReqVO();
        reqVO1.setApplyTimeStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setApplyTimeEnd(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setTag(0);
        reqVO1.setSeq(0);
        reqVO1.setIncludeColumnIndexes(new HashSet<>(Arrays.asList(0)));
        when(mockLeaveMapper.selectListByReqVO(reqVO1)).thenReturn(traineeLeaveRespVOS);

        when(mockFileApi.getByteMap(new HashMap<>())).thenReturn(CommonResult.success(new HashMap<>()));

        // Run the test
        final List<TraineeLeaveExcelVO> result = traineeLeaveServiceImplUnderTest.getLeaveExportList(reqVO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetLeaveExportList_TraineeLeaveMapperReturnsNoItems() {
        // Setup
        final TraineeLeaveExportReqVO reqVO = new TraineeLeaveExportReqVO();
        reqVO.setApplyTimeStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setApplyTimeEnd(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setTag(0);
        reqVO.setSeq(0);
        reqVO.setIncludeColumnIndexes(new HashSet<>(Arrays.asList(0)));

        // Configure TraineeLeaveMapper.selectListByReqVO(...).
        final TraineeLeaveExportReqVO reqVO1 = new TraineeLeaveExportReqVO();
        reqVO1.setApplyTimeStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setApplyTimeEnd(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setTag(0);
        reqVO1.setSeq(0);
        reqVO1.setIncludeColumnIndexes(new HashSet<>(Arrays.asList(0)));
        when(mockLeaveMapper.selectListByReqVO(reqVO1)).thenReturn(Collections.emptyList());

        when(mockFileApi.getByteMap(new HashMap<>())).thenReturn(CommonResult.success(new HashMap<>()));

        // Run the test
        final List<TraineeLeaveExcelVO> result = traineeLeaveServiceImplUnderTest.getLeaveExportList(reqVO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetLeaveExportList_FileApiReturnsError() {
        // Setup
        final TraineeLeaveExportReqVO reqVO = new TraineeLeaveExportReqVO();
        reqVO.setApplyTimeStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setApplyTimeEnd(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setTag(0);
        reqVO.setSeq(0);
        reqVO.setIncludeColumnIndexes(new HashSet<>(Arrays.asList(0)));

        final TraineeLeaveExcelVO traineeLeaveExcelVO = new TraineeLeaveExcelVO();
        traineeLeaveExcelVO.setClassName("className");
        traineeLeaveExcelVO.setTitle("title");
        traineeLeaveExcelVO.setTraineeName("traineeName");
        traineeLeaveExcelVO.setPictures(new WriteCellData<>(CellDataTypeEnum.STRING, "stringValue"));
        traineeLeaveExcelVO.setAccessory("accessory");
        final List<TraineeLeaveExcelVO> expectedResult = Arrays.asList(traineeLeaveExcelVO);

        // Configure TraineeLeaveMapper.selectListByReqVO(...).
        final TraineeLeaveRespVO respVO = new TraineeLeaveRespVO();
        respVO.setStartTimeStr("startTimeStr");
        respVO.setEndTimeStr("endTimeStr");
        respVO.setClassId(0L);
        respVO.setAccessory("accessory");
        respVO.setTraineeName("traineeName");
        respVO.setClassTeacherLeadName("userName");
        final TraineeLeaveProcessVO traineeLeaveProcessVO = new TraineeLeaveProcessVO();
        respVO.setProcessList(Arrays.asList(traineeLeaveProcessVO));
        final List<TraineeLeaveRespVO> traineeLeaveRespVOS = Arrays.asList(respVO);
        final TraineeLeaveExportReqVO reqVO1 = new TraineeLeaveExportReqVO();
        reqVO1.setApplyTimeStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setApplyTimeEnd(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setTag(0);
        reqVO1.setSeq(0);
        reqVO1.setIncludeColumnIndexes(new HashSet<>(Arrays.asList(0)));
        when(mockLeaveMapper.selectListByReqVO(reqVO1)).thenReturn(traineeLeaveRespVOS);

        when(mockFileApi.getByteMap(new HashMap<>()))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Run the test
        final List<TraineeLeaveExcelVO> result = traineeLeaveServiceImplUnderTest.getLeaveExportList(reqVO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetLeaveCount() {
        // Setup
        when(mockLeaveMapper.getLeaveCountOfTeacher(0L, 0L)).thenReturn(0);

        // Run the test
        final Integer result = traineeLeaveServiceImplUnderTest.getLeaveCount(0L);

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testTeacherList() {
        // Setup
        final TraineeLeaveRespVO respVO = new TraineeLeaveRespVO();
        respVO.setStartTimeStr("startTimeStr");
        respVO.setEndTimeStr("endTimeStr");
        respVO.setClassId(0L);
        respVO.setAccessory("accessory");
        respVO.setTraineeName("traineeName");
        respVO.setClassTeacherLeadName("userName");
        final TraineeLeaveProcessVO traineeLeaveProcessVO = new TraineeLeaveProcessVO();
        respVO.setProcessList(Arrays.asList(traineeLeaveProcessVO));
        final List<TraineeLeaveRespVO> expectedResult = Arrays.asList(respVO);

        // Configure TraineeLeaveMapper.selectTeacherList(...).
        final TraineeLeaveRespVO respVO1 = new TraineeLeaveRespVO();
        respVO1.setStartTimeStr("startTimeStr");
        respVO1.setEndTimeStr("endTimeStr");
        respVO1.setClassId(0L);
        respVO1.setAccessory("accessory");
        respVO1.setTraineeName("traineeName");
        respVO1.setClassTeacherLeadName("userName");
        final TraineeLeaveProcessVO traineeLeaveProcessVO1 = new TraineeLeaveProcessVO();
        respVO1.setProcessList(Arrays.asList(traineeLeaveProcessVO1));
        final List<TraineeLeaveRespVO> traineeLeaveRespVOS = Arrays.asList(respVO1);
        when(mockLeaveMapper.selectTeacherList(0L, 0)).thenReturn(traineeLeaveRespVOS);

        // Run the test
        final List<TraineeLeaveRespVO> result = traineeLeaveServiceImplUnderTest.teacherList(0L, 0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testTeacherList_TraineeLeaveMapperReturnsNoItems() {
        // Setup
        when(mockLeaveMapper.selectTeacherList(0L, 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<TraineeLeaveRespVO> result = traineeLeaveServiceImplUnderTest.teacherList(0L, 0);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetMyList() {
        // Setup
        final TraineeLeaveMyReqVO reqVO = new TraineeLeaveMyReqVO();
        reqVO.setClassId(0L);
        reqVO.setApplyTimeStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setApplyTimeEnd(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setTraineeUserId(0L);
        reqVO.setTraineeId(0L);

        final TraineeLeaveRespVO respVO = new TraineeLeaveRespVO();
        respVO.setStartTimeStr("startTimeStr");
        respVO.setEndTimeStr("endTimeStr");
        respVO.setClassId(0L);
        respVO.setAccessory("accessory");
        respVO.setTraineeName("traineeName");
        respVO.setClassTeacherLeadName("userName");
        final TraineeLeaveProcessVO traineeLeaveProcessVO = new TraineeLeaveProcessVO();
        respVO.setProcessList(Arrays.asList(traineeLeaveProcessVO));
        final List<TraineeLeaveRespVO> expectedResult = Arrays.asList(respVO);

        // Configure TraineeMapper.selectOne(...).
        final TraineeDO traineeDO = TraineeDO.builder()
                .id(0L)
                .name("name")
                .classId(0L)
                .status(0)
                .userId(0L)
                .build();
        when(mockTraineeMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(traineeDO);

        // Configure TraineeLeaveMapper.selectMyList(...).
        final TraineeLeaveRespVO respVO1 = new TraineeLeaveRespVO();
        respVO1.setStartTimeStr("startTimeStr");
        respVO1.setEndTimeStr("endTimeStr");
        respVO1.setClassId(0L);
        respVO1.setAccessory("accessory");
        respVO1.setTraineeName("traineeName");
        respVO1.setClassTeacherLeadName("userName");
        final TraineeLeaveProcessVO traineeLeaveProcessVO1 = new TraineeLeaveProcessVO();
        respVO1.setProcessList(Arrays.asList(traineeLeaveProcessVO1));
        final List<TraineeLeaveRespVO> traineeLeaveRespVOS = Arrays.asList(respVO1);
        final TraineeLeaveMyReqVO reqVO1 = new TraineeLeaveMyReqVO();
        reqVO1.setClassId(0L);
        reqVO1.setApplyTimeStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setApplyTimeEnd(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setTraineeUserId(0L);
        reqVO1.setTraineeId(0L);
        when(mockLeaveMapper.selectMyList(reqVO1)).thenReturn(traineeLeaveRespVOS);

        // Run the test
        final List<TraineeLeaveRespVO> result = traineeLeaveServiceImplUnderTest.getMyList(reqVO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetMyList_TraineeLeaveMapperReturnsNoItems() {
        // Setup
        final TraineeLeaveMyReqVO reqVO = new TraineeLeaveMyReqVO();
        reqVO.setClassId(0L);
        reqVO.setApplyTimeStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setApplyTimeEnd(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setTraineeUserId(0L);
        reqVO.setTraineeId(0L);

        // Configure TraineeMapper.selectOne(...).
        final TraineeDO traineeDO = TraineeDO.builder()
                .id(0L)
                .name("name")
                .classId(0L)
                .status(0)
                .userId(0L)
                .build();
        when(mockTraineeMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(traineeDO);

        // Configure TraineeLeaveMapper.selectMyList(...).
        final TraineeLeaveMyReqVO reqVO1 = new TraineeLeaveMyReqVO();
        reqVO1.setClassId(0L);
        reqVO1.setApplyTimeStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setApplyTimeEnd(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setTraineeUserId(0L);
        reqVO1.setTraineeId(0L);
        when(mockLeaveMapper.selectMyList(reqVO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<TraineeLeaveRespVO> result = traineeLeaveServiceImplUnderTest.getMyList(reqVO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetTraineeLeaveDetail() {
        // Setup
        final TraineeLeaveRespVO expectedResult = new TraineeLeaveRespVO();
        expectedResult.setStartTimeStr("startTimeStr");
        expectedResult.setEndTimeStr("endTimeStr");
        expectedResult.setClassId(0L);
        expectedResult.setAccessory("accessory");
        expectedResult.setTraineeName("traineeName");
        expectedResult.setClassTeacherLeadName("userName");
        final TraineeLeaveProcessVO traineeLeaveProcessVO = new TraineeLeaveProcessVO();
        expectedResult.setProcessList(Arrays.asList(traineeLeaveProcessVO));

        // Configure TraineeLeaveMapper.getDetailById(...).
        final TraineeLeaveRespVO respVO = new TraineeLeaveRespVO();
        respVO.setStartTimeStr("startTimeStr");
        respVO.setEndTimeStr("endTimeStr");
        respVO.setClassId(0L);
        respVO.setAccessory("accessory");
        respVO.setTraineeName("traineeName");
        respVO.setClassTeacherLeadName("userName");
        final TraineeLeaveProcessVO traineeLeaveProcessVO1 = new TraineeLeaveProcessVO();
        respVO.setProcessList(Arrays.asList(traineeLeaveProcessVO1));
        when(mockLeaveMapper.getDetailById(0L)).thenReturn(respVO);

        // Configure TraineeLeaveProcessMapper.selectList(...).
        final List<TraineeLeaveProcessDO> list = Arrays.asList(TraineeLeaveProcessDO.builder()
                .leaveId(0L)
                .userId(0L)
                .userName("userName")
                .taskName("taskName")
                .taskStatus(0)
                .taskComment("taskComment")
                .userType("userType")
                .operateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockLeaveProcessMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(list);

        // Run the test
        final TraineeLeaveRespVO result = traineeLeaveServiceImplUnderTest.getTraineeLeaveDetail(0L);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetTraineeLeaveDetail_TraineeLeaveProcessMapperReturnsNoItems() {
        // Setup
        final TraineeLeaveRespVO expectedResult = new TraineeLeaveRespVO();
        expectedResult.setStartTimeStr("startTimeStr");
        expectedResult.setEndTimeStr("endTimeStr");
        expectedResult.setClassId(0L);
        expectedResult.setAccessory("accessory");
        expectedResult.setTraineeName("traineeName");
        expectedResult.setClassTeacherLeadName("userName");
        final TraineeLeaveProcessVO traineeLeaveProcessVO = new TraineeLeaveProcessVO();
        expectedResult.setProcessList(Arrays.asList(traineeLeaveProcessVO));

        // Configure TraineeLeaveMapper.getDetailById(...).
        final TraineeLeaveRespVO respVO = new TraineeLeaveRespVO();
        respVO.setStartTimeStr("startTimeStr");
        respVO.setEndTimeStr("endTimeStr");
        respVO.setClassId(0L);
        respVO.setAccessory("accessory");
        respVO.setTraineeName("traineeName");
        respVO.setClassTeacherLeadName("userName");
        final TraineeLeaveProcessVO traineeLeaveProcessVO1 = new TraineeLeaveProcessVO();
        respVO.setProcessList(Arrays.asList(traineeLeaveProcessVO1));
        when(mockLeaveMapper.getDetailById(0L)).thenReturn(respVO);

        when(mockLeaveProcessMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.emptyList());

        // Run the test
        final TraineeLeaveRespVO result = traineeLeaveServiceImplUnderTest.getTraineeLeaveDetail(0L);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetTraineeLeaveProcessList() {
        // Setup
        final TraineeLeaveProcessVO traineeLeaveProcessVO = new TraineeLeaveProcessVO();
        traineeLeaveProcessVO.setLeaveId(0L);
        traineeLeaveProcessVO.setUserName("userName");
        traineeLeaveProcessVO.setTaskName("taskName");
        traineeLeaveProcessVO.setTaskStatus(0);
        traineeLeaveProcessVO.setTaskComment("taskComment");
        final List<TraineeLeaveProcessVO> expectedResult = Arrays.asList(traineeLeaveProcessVO);

        // Configure TraineeLeaveProcessMapper.selectList(...).
        final List<TraineeLeaveProcessDO> list = Arrays.asList(TraineeLeaveProcessDO.builder()
                .leaveId(0L)
                .userId(0L)
                .userName("userName")
                .taskName("taskName")
                .taskStatus(0)
                .taskComment("taskComment")
                .userType("userType")
                .operateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockLeaveProcessMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(list);

        // Run the test
        final List<TraineeLeaveProcessVO> result = traineeLeaveServiceImplUnderTest.getTraineeLeaveProcessList(0L);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetTraineeLeaveProcessList_TraineeLeaveProcessMapperReturnsNoItems() {
        // Setup
        when(mockLeaveProcessMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<TraineeLeaveProcessVO> result = traineeLeaveServiceImplUnderTest.getTraineeLeaveProcessList(0L);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testModify() {
        // Setup
        final TraineeLeaveModifyReqVO reqVO = new TraineeLeaveModifyReqVO();
        reqVO.setId(0L);
        reqVO.setLeaveType(0);
        reqVO.setStartTimeStr("startTimeStr");
        reqVO.setEndTimeStr("endTimeStr");
        reqVO.setDays(0.0f);
        reqVO.setReason("reason");

        // Run the test
        final Boolean result = traineeLeaveServiceImplUnderTest.modify(reqVO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testDeal() {
        // Setup
        final TraineeLeaveDealReqVO reqVO = new TraineeLeaveDealReqVO();
        reqVO.setLeaveId(0L);
        reqVO.setIsAgree(false);
        reqVO.setOpinion("opinion");
        reqVO.setTransferUserId(0L);

        // Configure TraineeLeaveMapper.selectById(...).
        final TraineeLeaveDO leaveDO = TraineeLeaveDO.builder()
                .id(0L)
                .traineeUserId(0L)
                .traineeId(0L)
                .classId(0L)
                .leaveType(0)
                .reason("reason")
                .startTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .days(0.0f)
                .title("title")
                .accessory("accessory")
                .status(0)
                .applyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();
        when(mockLeaveMapper.selectById(0L)).thenReturn(leaveDO);

        // Configure TraineeMapper.selectById(...).
        final TraineeDO traineeDO = TraineeDO.builder()
                .id(0L)
                .name("name")
                .classId(0L)
                .status(0)
                .userId(0L)
                .build();
        when(mockTraineeMapper.selectById(0L)).thenReturn(traineeDO);

        // Run the test
        final Boolean result = traineeLeaveServiceImplUnderTest.deal(reqVO);

        // Verify the results
        assertFalse(result);
        verify(mockLeaveProcessMapper).update(any(LambdaUpdateWrapper.class));
        verify(mockTodoItemsService).updateTraineeLeaveTodoItem(0L, 0);
        verify(mockClockInInfoService).modifyLeaveClockIn(TraineeLeaveDO.builder()
                .id(0L)
                .traineeUserId(0L)
                .traineeId(0L)
                .classId(0L)
                .leaveType(0)
                .reason("reason")
                .startTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .days(0.0f)
                .title("title")
                .accessory("accessory")
                .status(0)
                .applyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        verify(mockXcxMsgService).sendBatchXcxMsg(
                new XcxMsgSendReqVO(Arrays.asList(0L), 0, "informItem", "informPeriod", "redirectParam"));
        verify(mockLeaveProcessMapper).insert(TraineeLeaveProcessDO.builder()
                .leaveId(0L)
                .userId(0L)
                .userName("userName")
                .taskName("taskName")
                .taskStatus(0)
                .taskComment("taskComment")
                .userType("userType")
                .operateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
    }

    @Test
    public void testTransferBack() {
        // Setup
        final TraineeLeaveDealReqVO reqVO = new TraineeLeaveDealReqVO();
        reqVO.setLeaveId(0L);
        reqVO.setIsAgree(false);
        reqVO.setOpinion("opinion");
        reqVO.setTransferUserId(0L);

        // Configure TraineeLeaveMapper.selectById(...).
        final TraineeLeaveDO leaveDO = TraineeLeaveDO.builder()
                .id(0L)
                .traineeUserId(0L)
                .traineeId(0L)
                .classId(0L)
                .leaveType(0)
                .reason("reason")
                .startTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .days(0.0f)
                .title("title")
                .accessory("accessory")
                .status(0)
                .applyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();
        when(mockLeaveMapper.selectById(0L)).thenReturn(leaveDO);

        // Run the test
        final Boolean result = traineeLeaveServiceImplUnderTest.transferBack(reqVO);

        // Verify the results
        assertTrue(result);
        verify(mockLeaveProcessMapper).update(any(LambdaUpdateWrapper.class));
        verify(mockTodoItemsService).updateTraineeLeaveTodoItem(0L, 0);
        verify(mockClockInInfoService).modifyLeaveClockIn(TraineeLeaveDO.builder()
                .id(0L)
                .traineeUserId(0L)
                .traineeId(0L)
                .classId(0L)
                .leaveType(0)
                .reason("reason")
                .startTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .days(0.0f)
                .title("title")
                .accessory("accessory")
                .status(0)
                .applyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        verify(mockXcxMsgService).sendBatchXcxMsg(
                new XcxMsgSendReqVO(Arrays.asList(0L), 0, "informItem", "informPeriod", "redirectParam"));
    }
}
