<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.ruletemplate.RuleTemplateMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <insert id="insertLocalInfo">
        INSERT INTO
            "edu_rule_location"
        (
            "rule_template_id",
            "location_name",
            "longitude",
            "latitude",
            "range"
        )
        VALUES
        (
            #{reqVO.ruleTemplateId},
            #{reqVO.locationName},
            #{reqVO.longitude},
            #{reqVO.latitude},
            #{reqVO.range}
        );
    </insert>


    <select id="getCampusDefaultRule" resultType="java.lang.Integer">
        select
            count(1)
        from
            "edu_rule_template" ert
        WHERE
            ert ."deleted" = '0'
          AND
            ert ."default_rule" = '0'
          AND
            ert ."campus" = #{campusId}
          AND
            ert. "rule_type" = #{ruleType}
    </select>

    <select id="getCampusDefaultRules" resultType="com.unicom.swdx.module.edu.dal.dataobject.ruletemplate.RuleTemplateDO">
        SELECT
            "id",
            "rule_name",
            "rule_type",
            "locations",
            "before_class_time",
            "after_class_time",
            "breakfast_start_time",
            "breakfast_end_time",
            "lunch_start_time",
            "lunch_end_time",
            "dinner_start_time",
            "dinner_end_time",
            "put_up_start_time",
            "put_up_end_time",
            "status",
            "campus",
            "default_rule"
        FROM
            edu_rule_template
        where
            "deleted" = '0'
          AND
            "default_rule" = '0'
          AND
            "campus" = #{campusId}
          AND
            "rule_type" = #{ruleType}
    </select>

    <delete id="deletedRuleLocation">
        DELETE FROM edu_rule_location
        WHERE rule_template_id = #{ruleTemplateId}
    </delete>

    <select id="selectListTemplateLocation" resultType="com.unicom.swdx.module.edu.dal.dataobject.ruletemplate.RuleTemplateLocationDO">
        SELECT
            rule_template_id,
            location_name,
            longitude,
            latitude,
            "range",
            id
        FROM
            edu_rule_location
       where
            rule_template_id = #{id}
            and deleted = 0

    </select>
    <select id="getClassClockCount" resultType="java.lang.Integer">
        select
            count(1)
        from
            edu_class_clock_in
        where
            deleted = '0'
          and
            (
            attendance_check = #{id}
           or
            meal_attendance = #{id}
           or
            check_in = #{id}
            )
    </select>
    <select id="getClassDefaultRule" resultType="com.unicom.swdx.module.edu.dal.dataobject.ruletemplate.RuleTemplateDO">
        SELECT
            id,
            rule_type
        FROM
            edu_rule_template
        WHERE
            deleted = '0'
          AND
            default_rule = '0'
          AND
            campus = #{campusId}
    </select>
    <select id="getClassDefaultRuleSingle" resultType="com.unicom.swdx.module.edu.dal.dataobject.ruletemplate.RuleTemplateDO">
        SELECT
            id
        FROM
            edu_rule_template
        WHERE
            deleted = '0'
          AND
            default_rule = '0'
          AND
            campus = #{campusId}
          AND
            rule_type = #{type}
    </select>
    <select id="selectListByType" resultType="com.unicom.swdx.module.edu.controller.admin.ruletemplate.vo.RuleTemplateVO">
        SELECT
            id,
            rule_name,
            rule_type,
            locations,
            before_class_time,
            after_class_time,
            breakfast_start_time,
            breakfast_end_time,
            lunch_start_time,
            lunch_end_time,
            dinner_start_time,
            dinner_end_time,
            put_up_start_time,
            put_up_end_time,
            status,
            campus,
            default_rule,
            create_time
        FROM
            edu_rule_template
        WHERE
            deleted = '0'
          AND
            status = '0'
          AND
            rule_type = #{ruleType}
    </select>
</mapper>
