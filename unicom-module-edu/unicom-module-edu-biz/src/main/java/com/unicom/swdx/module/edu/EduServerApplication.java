package com.unicom.swdx.module.edu;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 * @date 2024/02/19 10:10
 **/
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
public class EduServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(EduServerApplication.class, args);
    }

}
