<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.electivereleasecourses.ElectiveReleaseCoursesMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectListByClassTimePeriod"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.electivereleasecourses.ElectiveReleaseCoursesDO">
        select
        eerc.id,
        eerc.release_id,
        eerc.course_id,
        eerc.teacher_id,
        eerc.classroom_id
        from edu_elective_release_courses eerc
        left join edu_elective_release eer on eerc.release_id = eer.id
        where
        eerc.deleted = 0
        and eer.deleted = 0
        and
        (eer.class_start_time between #{classStartDateTime} and #{classEndDateTime}
        or
        eer.class_end_time  between #{classStartDateTime} and #{classEndDateTime}
        or (eer.class_start_time &lt;= #{classStartDateTime} and eer.class_end_time &gt;= #{classEndDateTime}))
        <if test="excludeId != null">
            and eer.id != #{excludeId}
        </if>
    </select>
    <select id="selectReleaseCoursesListByReleaseIdList"
            resultType="com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesSubRespVO">
        select
            eerc.id id,
            eerc.release_id releaseId,
            ec.id courseId,
            ec."name" courseName,
            et.id teacherId,
            et."name" teacherName,
            ecr.id classroomId,
            ecr.class_name classroomName,
            eerc.create_time createTime
        from
            edu_elective_release_courses eerc
                left join edu_courses ec on
                eerc.course_id = ec.id
                left join edu_teacher_information et on
                eerc.teacher_id = et.id
                left join edu_classroom_library ecr on
                eerc.classroom_id = ecr.id
        where
            eerc.deleted = 0
          and ec.deleted = 0
          and et.deleted = 0
          and ecr.deleted = 0
          <if test="releaseIdList != null and releaseIdList.size() > 0">
            and eerc.release_id in
            <foreach collection="releaseIdList" item="releaseId" separator="," open="(" close=")">
                #{releaseId}
            </foreach>
          </if>
        order by eerc.create_time
    </select>
    <select id="getReleaseCourseSelectedNumInfo"
            resultType="com.unicom.swdx.module.edu.controller.admin.electiverelease.dto.ReleaseCourseSelectedInfoDTO">
        select
            eets.release_course_id releaseCourseId,
            count(eets.release_course_id ) selectedNum
        from edu_elective_trainee_selection eets
        left join edu_trainee et on eets.trainee_id = et.id and et.deleted = 0
        where
            eets.deleted = 0
            and eets.release_id = #{releaseId}
        <if test="classIdList != null and classIdList.size() > 0">
            and et.class_id in
            <foreach collection="classIdList" item="classId" separator="," open="(" close=")">
                #{classId}
            </foreach>
        </if>
        group by eets.release_course_id
    </select>
    <select id="selectCourseByReleaseCourseId" resultType="com.unicom.swdx.module.edu.dal.dataobject.courses.CoursesDO">
        select
            ec.id,
            ec.courses_type,
            ec."name",
            ec.short_name,
            ec.theme_id,
            ec.educate_form_id,
            ec.management_dept_id,
            ec.status,
            ec."date",
            ec.activity_type,
            ec.create_time
        from edu_elective_release_courses eerc
                 join edu_courses ec on ec.id = eerc.course_id and ec.deleted = 0
        where eerc.deleted = 0
          and eerc.id = #{releaseCourseId}
    </select>
    <select id="selectPageForCourseTeachingRecord"
            resultType="com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesTeachingRecordRespVO">
        select
            eerc2.course_id ,
            ec."name" courseName,
            eerc2.teacher_id teacherIds,
            eti."name" teacherNames,
            eer.class_date classDate,
            eer.class_start_time classStartTime,
            eer.class_end_time classEndTime,
            eer.day_period dayPeriod
        from edu_elective_release eer
            join edu_elective_release_courses eerc2 on eerc2.release_id = eer.id and eerc2.deleted = 0
            join edu_courses ec on ec.id = eerc2.course_id and ec.deleted = 0
            join edu_teacher_information eti on eti.id = eerc2.teacher_id and eti.deleted = 0
        where eer.deleted = 0
            and ec.id = #{reqVO.courseId}
        <if test="reqVO.teacherName != null">
            and eti."name" like concat('%',#{reqVO.teacherName},'%')
        </if>
        <if test="reqVO.classStartTime != null">
            and eer.class_start_time &gt;= #{reqVO.classStartTime}
        </if>
        <if test="reqVO.classEndTime != null">
            and eer.class_end_time &lt;= #{reqVO.classEndTime}
        </if>
            and eer.class_end_time &lt; #{currentTime}
        order by eer.class_end_time
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>
    </select>
    <select id="selectListForTeachingRecordByReqVO"
            resultType="com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesTeachingRecordRespVO">
        select
            eerc2.course_id ,
            ec."name" courseName,
            eerc2.teacher_id teacherIds ,
            eti."name" teacherNames,
            eer.class_date classDate,
            eer.class_start_time classStartTime,
            eer.class_end_time classEndTime,
            eer.day_period dayPeriod
        from edu_elective_release eer
            join edu_elective_release_courses eerc2 on eerc2.release_id = eer.id and eerc2.deleted = 0
            join edu_courses ec on ec.id = eerc2.course_id and ec.deleted = 0
            join edu_teacher_information eti on eti.id = eerc2.teacher_id and eti.deleted = 0
        where eer.deleted = 0
        and ec.id in
        <foreach collection="reqVO.ids" item="courseId" open="(" separator="," close=")">
            #{courseId}
        </foreach>
        and eer.class_end_time &lt; #{currentTime}
        order by
        <if test="reqVO.sortField == null or reqVO.sortField == 0">
            ec.create_time
        </if>
        <if test="reqVO.sortField != null and reqVO.sortField == 1">
            ec."name"
        </if>
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>
            , eer.class_end_time
    </select>
</mapper>
