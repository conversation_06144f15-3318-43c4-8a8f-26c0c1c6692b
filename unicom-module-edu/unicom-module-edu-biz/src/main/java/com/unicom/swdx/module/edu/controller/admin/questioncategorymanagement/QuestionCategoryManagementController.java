package com.unicom.swdx.module.edu.controller.admin.questioncategorymanagement;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;


import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.object.BeanUtils;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;



import com.unicom.swdx.module.edu.controller.admin.questioncategorymanagement.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questioncategorymanagement.QuestionCategoryManagementDO;
import com.unicom.swdx.module.edu.service.questioncategorymanagement.QuestionCategoryManagementService;

@Tag(name = "管理后台 - 题目类别管理")
@RestController
@RequestMapping("/edu/question-category-management")
@Validated
public class QuestionCategoryManagementController {

    @Resource
    private QuestionCategoryManagementService questionCategoryManagementService;

    @PostMapping("/create")
    @Operation(summary = "创建题目类别管理")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:create')")
    public CommonResult<Long> createQuestionCategoryManagement(@Valid @RequestBody QuestionCategoryManagementSaveReqVO createReqVO) {
        return success(questionCategoryManagementService.createQuestionCategoryManagement(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新题目类别管理")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:update')")
    public CommonResult<Boolean> updateQuestionCategoryManagement(@Valid @RequestBody QuestionCategoryManagementSaveReqVO updateReqVO) {
        questionCategoryManagementService.updateQuestionCategoryManagement(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除题目类别管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:delete')")
    public CommonResult<Boolean> deleteQuestionCategoryManagement(@RequestParam("id") Long id) {
        questionCategoryManagementService.deleteQuestionCategoryManagement(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得题目类别管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<QuestionCategoryManagementRespVO> getQuestionCategoryManagement(@RequestParam("id") Long id) {
        QuestionCategoryManagementDO questionCategoryManagement = questionCategoryManagementService.getQuestionCategoryManagement(id);
        return success(BeanUtils.toBean(questionCategoryManagement, QuestionCategoryManagementRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得题目类别管理列表")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<List<QuestionCategoryManagementRespVO>> getQuestionCategoryManagementList(@Valid QuestionCategoryManagementListReqVO listReqVO) {
        List<QuestionCategoryManagementDO> list = questionCategoryManagementService.getQuestionCategoryManagementList(listReqVO);
        return success(BeanUtils.toBean(list, QuestionCategoryManagementRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出题目类别管理 Excel")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:export')")

    public void exportQuestionCategoryManagementExcel(@Valid QuestionCategoryManagementListReqVO listReqVO,
              HttpServletResponse response) throws IOException {
        List<QuestionCategoryManagementDO> list = questionCategoryManagementService.getQuestionCategoryManagementList(listReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "题目类别管理.xls", "数据", QuestionCategoryManagementRespVO.class,
                        BeanUtils.toBean(list, QuestionCategoryManagementRespVO.class));
    }

}
