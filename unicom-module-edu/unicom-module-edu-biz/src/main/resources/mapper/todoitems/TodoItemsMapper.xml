<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.todoitems.TodoItemsMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="selectListByReq"
            resultType="com.unicom.swdx.module.edu.controller.admin.todoitems.vo.TodoItemsRespVO">

        SELECT
        id,
        "content",
        status,
        "type",
        class_id,
        create_time,
        leave_id,
        questionnaire_id
        FROM
        edu_todo_items
        <where>
            deleted = 0
            <if test="reqVO.statusList != null and reqVO.statusList.size() > 0">
                AND status IN
                <foreach item="status" index="index" collection="reqVO.statusList" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="reqVO.type != null">
                AND "type" = #{reqVO.type}
            </if>
            <if test="reqVO.classId != null">
                AND class_id = #{reqVO.classId}

            </if>
            <if test="reqVO.startDate != null">
                AND create_time &gt;= #{reqVO.startDate}
            </if>
            <if test="reqVO.endDate != null">
                AND create_time &lt;= #{reqVO.endDate}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper>
