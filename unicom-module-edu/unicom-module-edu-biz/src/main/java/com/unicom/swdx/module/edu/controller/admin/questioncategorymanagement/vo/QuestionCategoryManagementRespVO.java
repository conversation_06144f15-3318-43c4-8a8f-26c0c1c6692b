package com.unicom.swdx.module.edu.controller.admin.questioncategorymanagement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 题目类别管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class QuestionCategoryManagementRespVO {

    @Schema(description = "主键ID",  example = "4884")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "题目类别名称", example = "李四")
    @ExcelProperty("题目类别名称")
    private String fullName;

    @Schema(description = "创建部门")
    @ExcelProperty("创建部门")
    private Long createDept;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "父级", example = "2048")
    @ExcelProperty("父级")
    private Long parentId;

    @Schema(description = "是否内置", example = "false")
    private Boolean builtIn;

}
