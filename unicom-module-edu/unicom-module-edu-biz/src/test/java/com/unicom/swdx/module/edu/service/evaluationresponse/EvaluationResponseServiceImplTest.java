//package com.unicom.swdx.module.edu.service.evaluationresponse;
//
//import org.junit.jupiter.api.Disabled;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.mock.mockito.MockBean;
//
//import javax.annotation.Resource;
//
//import com.unicom.swdx.framework.test.core.ut.BaseDbUnitTest;
//
//import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.*;
//import com.unicom.swdx.module.edu.dal.dataobject.evaluationresponse.EvaluationResponseDO;
//import com.unicom.swdx.module.edu.dal.mysql.evaluationresponse.EvaluationResponseMapper;
//import com.unicom.swdx.framework.common.pojo.PageResult;
//
//import javax.annotation.Resource;
//import org.springframework.context.annotation.Import;
//import java.util.*;
//import java.time.LocalDateTime;
//
//import static cn.hutool.core.util.RandomUtil.*;
//import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
//import static com.unicom.swdx.framework.test.core.util.AssertUtils.*;
//import static com.unicom.swdx.framework.test.core.util.RandomUtils.*;
//
//import static com.unicom.swdx.framework.common.util.object.ObjectUtils.*;
//import static com.unicom.swdx.framework.common.util.date.DateUtils.*;
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.Mockito.*;
//
///**
// * {@link EvaluationResponseServiceImpl} 的单元测试类
// *
// * <AUTHOR>
// */
//@Import(EvaluationResponseServiceImpl.class)
//public class EvaluationResponseServiceImplTest extends BaseDbUnitTest {
//
//    @Resource
//    private EvaluationResponseServiceImpl evaluationResponseService;
//
//    @Resource
//    private EvaluationResponseMapper evaluationResponseMapper;
//
//    @Test
//    public void testCreateEvaluationResponse_success() {
//        // 准备参数
//        EvaluationResponseSaveReqVO createReqVO = randomPojo(EvaluationResponseSaveReqVO.class).setId(null);
//
//        // 调用
//        Integer evaluationResponseId = evaluationResponseService.createEvaluationResponse(createReqVO);
//        // 断言
//        assertNotNull(evaluationResponseId);
//        // 校验记录的属性是否正确
//        EvaluationResponseDO evaluationResponse = evaluationResponseMapper.selectById(evaluationResponseId);
//        assertPojoEquals(createReqVO, evaluationResponse, "id");
//    }
//
//    @Test
//    public void testUpdateEvaluationResponse_success() {
//        // mock 数据
//        EvaluationResponseDO dbEvaluationResponse = randomPojo(EvaluationResponseDO.class);
//        evaluationResponseMapper.insert(dbEvaluationResponse);// @Sql: 先插入出一条存在的数据
//        // 准备参数
//        EvaluationResponseSaveReqVO updateReqVO = randomPojo(EvaluationResponseSaveReqVO.class, o -> {
//            o.setId(dbEvaluationResponse.getId()); // 设置更新的 ID
//        });
//
//        // 调用
//        evaluationResponseService.updateEvaluationResponse(updateReqVO);
//        // 校验是否更新正确
//        EvaluationResponseDO evaluationResponse = evaluationResponseMapper.selectById(updateReqVO.getId()); // 获取最新的
//        assertPojoEquals(updateReqVO, evaluationResponse);
//    }
//
//    @Test
//    public void testUpdateEvaluationResponse_notExists() {
//        // 准备参数
//        EvaluationResponseSaveReqVO updateReqVO = randomPojo(EvaluationResponseSaveReqVO.class);
//
//        // 调用, 并断言异常
//        assertServiceException(() -> evaluationResponseService.updateEvaluationResponse(updateReqVO), EVALUATION_RESPONSE_NOT_EXISTS);
//    }
//
//    @Test
//    public void testDeleteEvaluationResponse_success() {
//        // mock 数据
//        EvaluationResponseDO dbEvaluationResponse = randomPojo(EvaluationResponseDO.class);
//        evaluationResponseMapper.insert(dbEvaluationResponse);// @Sql: 先插入出一条存在的数据
//        // 准备参数
//        Integer id = dbEvaluationResponse.getId();
//
//        // 调用
//        evaluationResponseService.deleteEvaluationResponse(id);
//       // 校验数据不存在了
//       assertNull(evaluationResponseMapper.selectById(id));
//    }
//
//    @Test
//    public void testDeleteEvaluationResponse_notExists() {
//        // 准备参数
//        Integer id = 1;
//
//        // 调用, 并断言异常
//        assertServiceException(() -> evaluationResponseService.deleteEvaluationResponse(id), EVALUATION_RESPONSE_NOT_EXISTS);
//    }
//
//    @Test
//    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
//    public void testGetEvaluationResponsePage() {
//       // mock 数据
//       EvaluationResponseDO dbEvaluationResponse = randomPojo(EvaluationResponseDO.class, o -> { // 等会查询到
//           o.setQuestionId(null);
//           o.setStudentId(null);
//           o.setIssuer(null);
//           o.setDeptId(null);
//           o.setCreateTime(null);
//           o.setTeacherId(null);
//           o.setCourseId(null);
//           o.setScore(null);
//           o.setGrade(null);
//           o.setHandle(null);
//           o.setRemarktype(null);
//       });
//       evaluationResponseMapper.insert(dbEvaluationResponse);
//       // 测试 questionId 不匹配
////       evaluationResponseMapper.insert(cloneIgnoreId(dbEvaluationResponse, o -> o.setQuestionId(null)));
//       // 测试 studentId 不匹配
//       evaluationResponseMapper.insert(cloneIgnoreId(dbEvaluationResponse, o -> o.setStudentId(null)));
//       // 测试 issuer 不匹配
//       evaluationResponseMapper.insert(cloneIgnoreId(dbEvaluationResponse, o -> o.setIssuer(null)));
//       // 测试 deptId 不匹配
//       evaluationResponseMapper.insert(cloneIgnoreId(dbEvaluationResponse, o -> o.setDeptId(null)));
//       // 测试 createTime 不匹配
//       evaluationResponseMapper.insert(cloneIgnoreId(dbEvaluationResponse, o -> o.setCreateTime(null)));
//       // 测试 teacherId 不匹配
//       evaluationResponseMapper.insert(cloneIgnoreId(dbEvaluationResponse, o -> o.setTeacherId(null)));
//       // 测试 courseId 不匹配
////       evaluationResponseMapper.insert(cloneIgnoreId(dbEvaluationResponse, o -> o.setCourseId(null)));
//       // 测试 score 不匹配
//       evaluationResponseMapper.insert(cloneIgnoreId(dbEvaluationResponse, o -> o.setScore(null)));
//       // 测试 grade 不匹配
//       evaluationResponseMapper.insert(cloneIgnoreId(dbEvaluationResponse, o -> o.setGrade(null)));
//       // 测试 handle 不匹配
//       evaluationResponseMapper.insert(cloneIgnoreId(dbEvaluationResponse, o -> o.setHandle(null)));
//       // 测试 remarktype 不匹配
//       evaluationResponseMapper.insert(cloneIgnoreId(dbEvaluationResponse, o -> o.setRemarktype(null)));
//       // 准备参数
//       EvaluationResponsePageReqVO reqVO = new EvaluationResponsePageReqVO();
//       reqVO.setQuestionId(null);
//       reqVO.setStudentId(null);
//       reqVO.setIssuer(null);
//       reqVO.setDeptId(null);
//
//       reqVO.setTeacherId(null);
//       reqVO.setCourseId(null);
//       reqVO.setScore(null);
//       reqVO.setGrade(null);
//       reqVO.setHandle(null);
//       reqVO.setRemarktype(null);
//
//       // 调用
//       PageResult<EvaluationResponseDO> pageResult = evaluationResponseService.getEvaluationResponsePage(reqVO);
//       // 断言
//       assertEquals(1, pageResult.getTotal());
//       assertEquals(1, pageResult.getList().size());
//       assertPojoEquals(dbEvaluationResponse, pageResult.getList().get(0));
//    }
//
//}
