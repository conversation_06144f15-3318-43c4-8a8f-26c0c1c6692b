<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <sql id="selectTeacherRespVO">
        SELECT
        eti.id id,
        eti.source source,
        eti.name name,
        eti.gender gender,
        eti.contact_information contactInformation,
        eti.professional_title professionalTitle,
        eti.dept_ids deptIds,
        eti.dept_names deptNames,
        eti.work_unit workUnit
        FROM
        edu_teacher_information eti
        <where>
            eti.deleted = 0
            <if test="reqVO.source != null">
                AND eti.source = #{reqVO.source}
            </if>
            <if test="reqVO.name != null and reqVO.name != ''">
                AND (eti."name" LIKE CONCAT('%',#{reqVO.name},'%')
                or eti.contact_information like CONCAT('%',#{reqVO.name},'%'))
            </if>
        </where>
        ORDER BY
        <if test="reqVO.sortField == null or reqVO.sortField == 0">
            eti.id
        </if>
        <if test="reqVO.sortField != null and reqVO.sortField == 1">
            eti.name
        </if>
        <if test="reqVO.isDesc != null and reqVO.isDesc == false">
            ASC
        </if>
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>
    </sql>


    <select id="selectExportList"
            resultType="com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationExcelVO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationExportReqVO">
        <include refid="selectTeacherRespVO"/>
    </select>
    <select id="selectPageByPageVO"
            resultType="com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationRespVO">
        <include refid="selectTeacherRespVO"/>
    </select>
    <select id="selectListByIds"
            resultType="com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationRespVO">
        SELECT
            eti.name name,
            eti.gender gender,
            eti.contact_information contactInformation,
            eti.dept_names deptNames,
            eti.work_unit workUnit
        FROM
            edu_teacher_information eti
        WHERE
        eti.deleted = 0
        AND eti.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY eti.id
    </select>

    <select id="selectTeachersDeleted"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO">
        select
            *
        from
            edu_teacher_information eti
        where eti.deleted =1 and eti.tenant_id=#{tenantId}
    </select>

    <select id="listForElectiveRelease"
            resultType="com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationSimpleRespVO">
        select
            eti.id id,
            eti."name"
        from
            edu_teacher_course_information etci
            join edu_teacher_information eti on etci.teacher_id = eti.id and eti.deleted = 0
        where
            etci.deleted =0
            and etci.courses_id = #{courseId}
            and not exists  (
                select
                    1
                from
                    edu_elective_release_courses eerc
                    left join edu_elective_release eer on eerc.release_id = eer.id
                where
                    eerc.deleted = 0
                    and eer.deleted = 0
                  and
                    (eer.class_start_time between #{classStartDateTime} and #{classEndDateTime}
                        or
                     eer.class_end_time  between #{classStartDateTime} and #{classEndDateTime}
                        or (eer.class_start_time &lt;= #{classStartDateTime} and eer.class_end_time &gt;= #{classEndDateTime}))
                    and eti.id = eerc.teacher_id
                    <if test="excludeReleaseId != null">
                        and eer.id != #{excludeReleaseId}
                    </if>
            )
            and not exists (
                select 1
                from edu_class_course ecc
                    left join edu_courses ec on ec.id = ecc.course_id and ec.deleted = 0
                    left join edu_plan ep on ecc.plan_id = ep.id and ep.deleted = 0
                where ecc.deleted = 0
--                     and ep.status = '1'
                    and ec.courses_type in (1,3)
                    and ecc.is_temporary = 0
                    and  (
                    ecc.begin_time between #{classStartDateTime} and #{classEndDateTime}
                    or
                    ecc.end_time  between #{classStartDateTime} and  #{classEndDateTime}
                    or (ecc.begin_time &lt;= #{classStartDateTime} and ecc.end_time &gt;=  #{classEndDateTime}))
                    and eti.id = ecc.teacher_id
                    <if test="excludeClassCourseId != null">
                        and ecc.id != #{excludeClassCourseId}
                    </if>
            )
        order by eti.create_time desc
    </select>
</mapper>
