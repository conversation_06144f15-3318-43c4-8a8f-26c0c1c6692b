package com.unicom.swdx.module.edu.service.plandetail;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.unicom.swdx.framework.test.core.ut.BaseDbUnitTest;

import com.unicom.swdx.module.edu.controller.admin.plandetail.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plandetail.PlanDetailDO;
import com.unicom.swdx.module.edu.dal.mysql.plandetail.PlanDetailMapper;
import com.unicom.swdx.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.framework.test.core.util.AssertUtils.*;
import static com.unicom.swdx.framework.test.core.util.RandomUtils.*;
import static com.unicom.swdx.framework.common.util.object.ObjectUtils.*;
import static com.unicom.swdx.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link PlanDetailServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(PlanDetailServiceImpl.class)
public class PlanDetailServiceImplTest extends BaseDbUnitTest {

    @Resource
    private PlanDetailServiceImpl planDetailService;

    @Resource
    private PlanDetailMapper planDetailMapper;

    @Test
    public void testCreatePlanDetail_success() {
        // 准备参数
        PlanDetailCreateReqVO reqVO = randomPojo(PlanDetailCreateReqVO.class);

        // 调用
        Long planDetailId = planDetailService.createPlanDetail(reqVO);
        // 断言
        assertNotNull(planDetailId);
        // 校验记录的属性是否正确
        PlanDetailDO planDetail = planDetailMapper.selectById(planDetailId);
        assertPojoEquals(reqVO, planDetail);
    }

    @Test
    public void testUpdatePlanDetail_success() {
        // mock 数据
        PlanDetailDO dbPlanDetail = randomPojo(PlanDetailDO.class);
        planDetailMapper.insert(dbPlanDetail);// @Sql: 先插入出一条存在的数据
        // 准备参数
        PlanDetailUpdateReqVO reqVO = randomPojo(PlanDetailUpdateReqVO.class, o -> {
            o.setId(dbPlanDetail.getId()); // 设置更新的 ID
        });

        // 调用
        planDetailService.updatePlanDetail(reqVO);
        // 校验是否更新正确
        PlanDetailDO planDetail = planDetailMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, planDetail);
    }

    @Test
    public void testUpdatePlanDetail_notExists() {
        // 准备参数
        PlanDetailUpdateReqVO reqVO = randomPojo(PlanDetailUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> planDetailService.updatePlanDetail(reqVO), PLAN_DETAIL_NOT_EXISTS);
    }

    @Test
    public void testDeletePlanDetail_success() {
        // mock 数据
        PlanDetailDO dbPlanDetail = randomPojo(PlanDetailDO.class);
        planDetailMapper.insert(dbPlanDetail);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlanDetail.getId();

        // 调用
        planDetailService.deletePlanDetail(id);
       // 校验数据不存在了
       assertNull(planDetailMapper.selectById(id));
    }

    @Test
    public void testDeletePlanDetail_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planDetailService.deletePlanDetail(id), PLAN_DETAIL_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanDetailPage() {
       // mock 数据
       PlanDetailDO dbPlanDetail = randomPojo(PlanDetailDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setDate(null);
           o.setPeriod(null);
           o.setBeginTime(null);
           o.setEndTime(null);
       });
       planDetailMapper.insert(dbPlanDetail);
       // 测试 planId 不匹配
       planDetailMapper.insert(cloneIgnoreId(dbPlanDetail, o -> o.setPlanId(null)));
       // 测试 date 不匹配
       planDetailMapper.insert(cloneIgnoreId(dbPlanDetail, o -> o.setDate(null)));
       // 测试 period 不匹配
       planDetailMapper.insert(cloneIgnoreId(dbPlanDetail, o -> o.setPeriod(null)));
       // 测试 beginTime 不匹配
       planDetailMapper.insert(cloneIgnoreId(dbPlanDetail, o -> o.setBeginTime(null)));
       // 测试 endTime 不匹配
       planDetailMapper.insert(cloneIgnoreId(dbPlanDetail, o -> o.setEndTime(null)));
       // 准备参数
       PlanDetailPageReqVO reqVO = new PlanDetailPageReqVO();
       reqVO.setPlanId(null);
//       reqVO.setDate((new LocalDateTime[]{}).toString());
       reqVO.setPeriod(null);
       reqVO.setBeginTime((new LocalDateTime[]{}));
       reqVO.setEndTime((new LocalDateTime[]{}));

       // 调用
       PageResult<PlanDetailDO> pageResult = planDetailService.getPlanDetailPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlanDetail, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanDetailList() {
       // mock 数据
       PlanDetailDO dbPlanDetail = randomPojo(PlanDetailDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setDate(null);
           o.setPeriod(null);
           o.setBeginTime(null);
           o.setEndTime(null);
       });
       planDetailMapper.insert(dbPlanDetail);
       // 测试 planId 不匹配
       planDetailMapper.insert(cloneIgnoreId(dbPlanDetail, o -> o.setPlanId(null)));
       // 测试 date 不匹配
       planDetailMapper.insert(cloneIgnoreId(dbPlanDetail, o -> o.setDate(null)));
       // 测试 period 不匹配
       planDetailMapper.insert(cloneIgnoreId(dbPlanDetail, o -> o.setPeriod(null)));
       // 测试 beginTime 不匹配
       planDetailMapper.insert(cloneIgnoreId(dbPlanDetail, o -> o.setBeginTime(null)));
       // 测试 endTime 不匹配
       planDetailMapper.insert(cloneIgnoreId(dbPlanDetail, o -> o.setEndTime(null)));
       // 准备参数
       PlanDetailExportReqVO reqVO = new PlanDetailExportReqVO();
       reqVO.setPlanId(null);
//       reqVO.setDate((new LocalDateTime[]{}));
       reqVO.setPeriod(null);
       reqVO.setBeginTime((new LocalDateTime[]{}));
       reqVO.setEndTime((new LocalDateTime[]{}));

       // 调用
       List<PlanDetailDO> list = planDetailService.getPlanDetailList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlanDetail, list.get(0));
    }

}
