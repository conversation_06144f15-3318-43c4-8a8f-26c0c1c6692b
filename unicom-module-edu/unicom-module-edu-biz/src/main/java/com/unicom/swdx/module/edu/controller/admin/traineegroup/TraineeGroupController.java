package com.unicom.swdx.module.edu.controller.admin.traineegroup;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.common.util.validation.ExcelValidator;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.GroupBaseReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupExcelVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupRespVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupImportResultVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupImportDataVO;
import com.unicom.swdx.module.edu.convert.traineegroup.TraineeGroupConvert;
import com.unicom.swdx.module.edu.dal.dataobject.traineegroup.TraineeGroupDO;
import com.unicom.swdx.module.edu.enums.trainee.TraineeDictTypeEnum;
import com.unicom.swdx.module.edu.service.traineegroup.TraineeGroupService;
import com.unicom.swdx.module.edu.service.training.TraineeDictConvertService;
import com.unicom.swdx.module.edu.service.training.TraineeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import org.springframework.web.multipart.MultipartFile;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 学员分组")
@RestController
@RequestMapping("/edu/group")
@Validated
public class TraineeGroupController {

    @Resource
    private TraineeService traineeService;

    @Resource
    private TraineeGroupService traineeGroupService;

    @Resource
    private TraineeDictConvertService traineeDictConvertService;
    /**
     * 新增学员分组
     * @param reqVO
     * @return
     */
    @PostMapping("/addGroup")
    @ApiOperation(value = "新增小组")
    @PreAuthorize("@ss.hasPermission('edu:trainee:add')")
    public CommonResult<Long> addGroup(@RequestBody GroupBaseReqVO reqVO) {
        return success(traineeGroupService.addGroup(reqVO));
    }


    /* 编辑小组
     * @param reqVO
     * @return
     */
    @PostMapping("/editGroup")
    @ApiOperation(value = "编辑小组")
    @PreAuthorize("@ss.hasPermission('edu:trainee:edit')")
    public CommonResult<Long> editGroup(@RequestBody GroupBaseReqVO reqVO) {
        return success(traineeGroupService.editGroup(reqVO));
    }

    /* 批量删除小组
     * @param reqVO
     * @return
     */
    @PostMapping("/deleteGroup")
    @ApiOperation(value = "批量删除小组")
    @PreAuthorize("@ss.hasPermission('edu:trainee:delete')")
    public CommonResult<Boolean> deleteGroup(@RequestBody List<Long> ids) {
        return success(traineeGroupService.deleteGroup(ids));
    }

    /* 学员分组分页
     * @param reqVO
     * @return
     */

    @GetMapping("/groupPage")
    @ApiOperation(value = "根据id获取学员信息")
    @PreAuthorize("@ss.hasPermission('edu:trainee:get')")
    public CommonResult<PageResult<TraineeGroupRespVO>> getTrainByGroup(TraineeGroupReqVO reqVO) {
        return success(traineeService.getTrainByGroup(reqVO));
    }

//    @GetMapping("/groupPage")
//    @ApiOperation(value = "分组分页接口")
//    @PreAuthorize("@ss.hasPermission('edu:trainee:page')")
//    public CommonResult<Page<TraineeGroupRespVO>> groupPage(TraineeGroupReqVO reqVO) {
//        return success(traineeGroupService.getPage(reqVO));
//    }

    /* 导出学员分组
     * @param reqVO
     * @return
     */
    @PostMapping("/exportGroup")
    @ApiOperation(value = "导出学员分组接口")
    @PreAuthorize("@ss.hasPermission('edu:trainee:export')")
    public void exportGroup(@RequestBody TraineeGroupReqVO reqVO, HttpServletResponse response) throws IOException {
        List<TraineeGroupRespVO> tmpList = traineeGroupService.exportGroup(reqVO);
        List<TraineeGroupExcelVO> list = TraineeGroupConvert.INSTANCE.convertList(tmpList);

        Map<Long, Map<String, String>> educationalLevel = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.EDUCATIONAL_LEVEL.getType());
        Map<Long, Map<String, String>> politicalIdentityMap = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.POLITICAL_IDENTITY.getType());
        int j =0;
        for (int i = 0; i < tmpList.size(); i++) {

            TraineeGroupRespVO vo = tmpList.get(i);
            TraineeGroupExcelVO excelVO = list.get(i);

            if (StrUtil.isBlank(excelVO.getGroupName())){
                excelVO.setGroupName("未分组");
            }
            excelVO.setIndex((long)++j);

            if (StrUtil.isNotBlank(excelVO.getEducationalLevel())){
                if (educationalLevel != null && educationalLevel.containsKey(Long.valueOf(excelVO.getEducationalLevel())) && educationalLevel.get(Long.valueOf(excelVO.getEducationalLevel())) != null) {
                    excelVO.setEducationalLevel(educationalLevel.get(Long.valueOf(excelVO.getEducationalLevel())).get("label"));
                } else {
                    excelVO.setEducationalLevel(null); // 或者设置一个默认值
                }
            }
            if (StrUtil.isNotBlank(excelVO.getPoliticalIdentity())){
                if (politicalIdentityMap != null && politicalIdentityMap.containsKey(Long.valueOf(excelVO.getPoliticalIdentity())) && politicalIdentityMap.get(Long.valueOf(excelVO.getPoliticalIdentity())) != null) {
                    excelVO.setPoliticalIdentity(politicalIdentityMap.get(Long.valueOf(excelVO.getPoliticalIdentity())).get("label"));
                } else {
                    excelVO.setPoliticalIdentity(null); // 或者设置一个默认值
                }
            }
//            if (StrUtil.isNotBlank(excelVO.getEducationalLevel())){
//                excelVO.setEducationalLevel(EducationalLevelEnum.getDescByStatus(Integer.parseInt(excelVO.getEducationalLevel())));
//            }
//            if (StrUtil.isNotBlank(excelVO.getPoliticalIdentity())){
//                excelVO.setPoliticalIdentity(PoliticalIdentityEnum.getDescByStatus(Integer.parseInt(excelVO.getPoliticalIdentity())));
//            }
        }

        ExcelUtils.writeByIncludeColumnIndexes(response, "学员分组.xls",
                "数据", TraineeGroupExcelVO.class,list, reqVO.getIncludeColumnIndexes());
    }

    /**
     * 移动端-学员录
     * @param classId
     * @return
     */
    @GetMapping("/traineeListByGroup")
    @ApiOperation(value = "移动端-学员录")
    @PreAuthorize("@ss.hasPermission('edu:trainee:list')")
    public CommonResult<List<AppTraineeGroupRespVO>> traineeListByGroup(@RequestParam("classId") Long classId) {
        return success(traineeGroupService.traineeListByGroup(classId));
    }

    /**
     * 获取小组
     */
    @GetMapping("/getGroupList")
    @ApiOperation(value = "获取小组")
    @PreAuthorize("@ss.hasPermission('edu:trainee:get')")
    public CommonResult<List<Map<String,String>>> getGroupList(@RequestParam("classId") Long classId) {
        return success(traineeGroupService.getGroupList(classId));
    }

    /**
     * 根据id获取分组
     * @param
     * @return
     */
    @GetMapping("/getGroupById")
    @ApiOperation(value = "根据id获取分组")
    @PreAuthorize("@ss.hasPermission('edu:trainee:get')")
    public CommonResult<TraineeGroupDO> getGroupById(@RequestParam("id") Long id) {
        return success(traineeGroupService.getGroupById(id));
    }

    /**
     * 自动创建分组
     * @param
     * @return
     */
    @PostMapping("/autoCreateGroup")
    @ApiOperation(value = "自动创建分组")
    @PreAuthorize("@ss.hasPermission('edu:trainee:create')")
    public CommonResult<Boolean> autoCreateGroup(Long classId,Long number) {
        return success(traineeGroupService.autoCreateGroup(classId,number));
    }

    /**
     * 判断是否有学员
     * @param
     * @return
     */
    @GetMapping("/get")
    @ApiOperation(value = "判断分组是否有学员")
    @PreAuthorize("@ss.hasPermission('edu:trainee:get')")
    public CommonResult<Boolean> getTrainee(Long groupId) {
        return success(traineeGroupService.getTrainee(groupId));
    }


    /**
     * 判断班级是否分组
     * @param classId 班级id
     * @return true已分组 false未分组
     */
    @GetMapping("/hasGroup")
    @ApiOperation(value = "判断班级是否分组")
    public CommonResult<Boolean> hasGroup(Long classId) {
        return success(traineeGroupService.hasGroup(classId));
    }

    /**
     * 下载学员分组导入模板
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    @GetMapping("/downloadImportTemplate")
    @ApiOperation(value = "下载学员分组导入模板")
    @PreAuthorize("@ss.hasPermission('edu:trainee:import')")
    public void downloadImportTemplate(HttpServletResponse response) throws IOException {
        traineeGroupService.downloadImportTemplate(response);
    }

    /**
     * 导入学员分组
     * @param file Excel文件
     * @param classId 班级ID
     * @return 导入结果
     * @throws Exception 异常
     */
    @PostMapping("/importTraineeGroup")
    @ApiOperation(value = "导入学员分组")
    @PreAuthorize("@ss.hasPermission('edu:trainee:import')")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class),
            @ApiImplicitParam(name = "classId", value = "班级ID", required = true, dataTypeClass = Long.class)
    })
    public CommonResult<TraineeGroupImportResultVO> importTraineeGroup(@RequestParam("file") MultipartFile file,
                                                                      @RequestParam("classId") Long classId) throws Exception {
        // 读取 Excel 文件
        List<TraineeGroupImportDataVO> importDataList = ExcelUtils.readFirstSheetAndCheckHead(file, TraineeGroupImportDataVO.class);
        
        // 验证 Excel 数据
        ExcelValidator.valid(importDataList, 1);
        
        // 调用服务层，导入学员分组
        return success(traineeGroupService.importTraineeGroup(importDataList, classId));
    }
}
