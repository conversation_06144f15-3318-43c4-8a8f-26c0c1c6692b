package com.unicom.swdx.module.edu.controller.admin.graduationcertificatenumbers;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.graduationcertificatenumbers.vo.GraduationCertificateNumbersPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.graduationcertificatenumbers.vo.GraduationCertificateNumbersPageRespVO;
import com.unicom.swdx.module.edu.service.graduationcertificatenumbers.GraduationCertificateNumbersService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 学员结业证书编号")
@RestController
@RequestMapping("/edu/graduation-certificate-numbers")
@Validated
@Slf4j
public class GraduationCertificateNumbersController {

    @Resource
    private GraduationCertificateNumbersService graduationCertificateNumbersService;

    @PostMapping("/page")
    @ApiOperation("学员结业证书编号-分页查询")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<PageResult<GraduationCertificateNumbersPageRespVO>> getPage(@Valid @RequestBody GraduationCertificateNumbersPageReqVO reqVO) {
        PageResult<GraduationCertificateNumbersPageRespVO> pageResult = graduationCertificateNumbersService.selectPage(reqVO);
        return success(pageResult);
    }

    @PostMapping("/generateGraduationCertificateNumbers")
    @ApiOperation("生成班级学员结业证书编号")
    @ApiImplicitParam(name = "classId", value = "班级ID", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:update')")
    public CommonResult<Boolean> generateGraduationCertificateNumbers(@RequestParam("classId") Long classId) {
        graduationCertificateNumbersService.generateGraduationCertificateNumbers(classId);
        return success(true);
    }

}
