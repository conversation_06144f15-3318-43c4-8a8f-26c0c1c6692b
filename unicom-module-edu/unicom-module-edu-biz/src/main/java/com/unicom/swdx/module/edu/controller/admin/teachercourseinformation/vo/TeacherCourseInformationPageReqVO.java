package com.unicom.swdx.module.edu.controller.admin.teachercourseinformation.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - 师资-任课信息中间分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeacherCourseInformationPageReqVO extends PageParam {

    @ApiModelProperty(value = "课程ID")
    private Long coursesId;

    @ApiModelProperty(value = "教师ID")
    private Long teacherId;

    @ApiModelProperty(value = "部门 ID")
    private Long deptId;

}
