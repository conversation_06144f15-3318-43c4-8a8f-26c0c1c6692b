package com.unicom.swdx.module.edu.service.questionnairemanagement;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.unicom.swdx.framework.test.core.ut.BaseDbUnitTest;

import com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairemanagement.QuestionnaireManagementDO;
import com.unicom.swdx.module.edu.dal.mysql.questionnairemanagement.QuestionnaireManagementMapper;
import com.unicom.swdx.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.framework.test.core.util.AssertUtils.*;
import static com.unicom.swdx.framework.test.core.util.RandomUtils.*;

import static com.unicom.swdx.framework.common.util.object.ObjectUtils.*;
import static com.unicom.swdx.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link QuestionnaireManagementServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(QuestionnaireManagementServiceImpl.class)
public class QuestionnaireManagementServiceImplTest extends BaseDbUnitTest {

    @Resource
    private QuestionnaireManagementServiceImpl questionnaireManagementService;

    @Resource
    private QuestionnaireManagementMapper questionnaireManagementMapper;

    @Test
    public void testCreateQuestionnaireManagement_success() {
        // 准备参数
        QuestionnaireManagementSaveReqVO createReqVO = randomPojo(QuestionnaireManagementSaveReqVO.class).setId(null);

        // 调用
        Long questionnaireManagementId = questionnaireManagementService.createQuestionnaireManagement(createReqVO);
        // 断言
        assertNotNull(questionnaireManagementId);
        // 校验记录的属性是否正确
        QuestionnaireManagementDO questionnaireManagement = questionnaireManagementMapper.selectById(questionnaireManagementId);
        assertPojoEquals(createReqVO, questionnaireManagement, "id");
    }

    @Test
    public void testUpdateQuestionnaireManagement_success() {
        // mock 数据
        QuestionnaireManagementDO dbQuestionnaireManagement = randomPojo(QuestionnaireManagementDO.class);
        questionnaireManagementMapper.insert(dbQuestionnaireManagement);// @Sql: 先插入出一条存在的数据
        // 准备参数
        QuestionnaireManagementSaveReqVO updateReqVO = randomPojo(QuestionnaireManagementSaveReqVO.class, o -> {
            o.setId(dbQuestionnaireManagement.getId()); // 设置更新的 ID
        });

        // 调用
        questionnaireManagementService.updateQuestionnaireManagement(updateReqVO);
        // 校验是否更新正确
        QuestionnaireManagementDO questionnaireManagement = questionnaireManagementMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, questionnaireManagement);
    }

    @Test
    public void testUpdateQuestionnaireManagement_notExists() {
        // 准备参数
        QuestionnaireManagementSaveReqVO updateReqVO = randomPojo(QuestionnaireManagementSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> questionnaireManagementService.updateQuestionnaireManagement(updateReqVO), QUESTIONNAIRE_MANAGEMENT_NOT_EXISTS);
    }

    @Test
    public void testDeleteQuestionnaireManagement_success() {
        // mock 数据
        QuestionnaireManagementDO dbQuestionnaireManagement = randomPojo(QuestionnaireManagementDO.class);
        questionnaireManagementMapper.insert(dbQuestionnaireManagement);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbQuestionnaireManagement.getId();

        // 调用
        questionnaireManagementService.deleteQuestionnaireManagement(id);
       // 校验数据不存在了
       assertNull(questionnaireManagementMapper.selectById(id));
    }

    @Test
    public void testDeleteQuestionnaireManagement_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> questionnaireManagementService.deleteQuestionnaireManagement(id), QUESTIONNAIRE_MANAGEMENT_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetQuestionnaireManagementPage() {
       // mock 数据
       QuestionnaireManagementDO dbQuestionnaireManagement = randomPojo(QuestionnaireManagementDO.class, o -> { // 等会查询到
           o.setTitle(null);
           o.setIsDefault(null);
           o.setStatus(null);
           o.setCreateDept(null);
           o.setCreateTime(null);
           o.setCreator(null);
           o.setRemark(null);
           o.setTopicEducateForm(null);
           o.setLowscore(null);
           o.setLowscoreTag(null);
           o.setLowword(null);
           o.setLowwordTag(null);
           o.setTimeTag(null);
           o.setTimeLimit(null);
       });
       questionnaireManagementMapper.insert(dbQuestionnaireManagement);
       // 测试 title 不匹配
       questionnaireManagementMapper.insert(cloneIgnoreId(dbQuestionnaireManagement, o -> o.setTitle(null)));
       // 测试 isDefault 不匹配
       questionnaireManagementMapper.insert(cloneIgnoreId(dbQuestionnaireManagement, o -> o.setIsDefault(null)));
       // 测试 status 不匹配
       questionnaireManagementMapper.insert(cloneIgnoreId(dbQuestionnaireManagement, o -> o.setStatus(null)));
       // 测试 createDept 不匹配
       questionnaireManagementMapper.insert(cloneIgnoreId(dbQuestionnaireManagement, o -> o.setCreateDept(null)));
       // 测试 createTime 不匹配
       questionnaireManagementMapper.insert(cloneIgnoreId(dbQuestionnaireManagement, o -> o.setCreateTime(null)));
       // 测试 creator 不匹配
       questionnaireManagementMapper.insert(cloneIgnoreId(dbQuestionnaireManagement, o -> o.setCreator(null)));
       // 测试 remark 不匹配
       questionnaireManagementMapper.insert(cloneIgnoreId(dbQuestionnaireManagement, o -> o.setRemark(null)));
       // 测试 topicEducateForm 不匹配
       questionnaireManagementMapper.insert(cloneIgnoreId(dbQuestionnaireManagement, o -> o.setTopicEducateForm(null)));
       // 测试 lowscore 不匹配
       questionnaireManagementMapper.insert(cloneIgnoreId(dbQuestionnaireManagement, o -> o.setLowscore(null)));
       // 测试 lowscoreTag 不匹配
       questionnaireManagementMapper.insert(cloneIgnoreId(dbQuestionnaireManagement, o -> o.setLowscoreTag(null)));
       // 测试 lowword 不匹配
       questionnaireManagementMapper.insert(cloneIgnoreId(dbQuestionnaireManagement, o -> o.setLowword(null)));
       // 测试 lowwordTag 不匹配
       questionnaireManagementMapper.insert(cloneIgnoreId(dbQuestionnaireManagement, o -> o.setLowwordTag(null)));
       // 测试 timeTag 不匹配
       questionnaireManagementMapper.insert(cloneIgnoreId(dbQuestionnaireManagement, o -> o.setTimeTag(null)));
       // 测试 timeLimit 不匹配
       questionnaireManagementMapper.insert(cloneIgnoreId(dbQuestionnaireManagement, o -> o.setTimeLimit(null)));
       // 准备参数
       QuestionnaireManagementPageReqVO reqVO = new QuestionnaireManagementPageReqVO();
       reqVO.setTitle(null);
       reqVO.setIsDefault(null);
       reqVO.setStatus(null);
       reqVO.setCreateDept(null);

       reqVO.setCreator(null);
       reqVO.setRemark(null);
       reqVO.setTopicEducateForm(null);
       reqVO.setLowscore(null);
       reqVO.setLowscoreTag(null);
       reqVO.setLowword(null);
       reqVO.setLowwordTag(null);
       reqVO.setTimeTag(null);
       reqVO.setTimeLimit(null);

       // 调用
   //    PageResult<QuestionnaireManagementDO> pageResult = questionnaireManagementService.getQuestionnaireManagementPage(reqVO);
       // 断言
  //     assertEquals(1, pageResult.getTotal());
   //    assertEquals(1, pageResult.getList().size());
    //   assertPojoEquals(dbQuestionnaireManagement, pageResult.getList().get(0));
    }

}
