package com.unicom.swdx.module.edu.service.classcourse;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.unicom.swdx.framework.test.core.ut.BaseDbUnitTest;

import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.framework.test.core.util.AssertUtils.*;
import static com.unicom.swdx.framework.test.core.util.RandomUtils.*;
import static com.unicom.swdx.framework.common.util.object.ObjectUtils.*;
import static com.unicom.swdx.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link ClassCourseServiceImpl} 的单元测试类
*/
@Import(ClassCourseServiceImpl.class)
public class ClassCourseServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ClassCourseServiceImpl classCourseService;

    @Resource
    private ClassCourseMapper classCourseMapper;

    @Test
    public void testCreateClassCourse_success() {
        // 准备参数
        ClassCourseCreateReqVO reqVO = randomPojo(ClassCourseCreateReqVO.class);

        // 调用
        Long classCourseId = classCourseService.createClassCourse(reqVO);
        // 断言
        assertNotNull(classCourseId);
        // 校验记录的属性是否正确
        ClassCourseDO classCourse = classCourseMapper.selectById(classCourseId);
        assertPojoEquals(reqVO, classCourse);
    }

    @Test
    public void testUpdateClassCourse_success() {
        // mock 数据
        ClassCourseDO dbClassCourse = randomPojo(ClassCourseDO.class);
        classCourseMapper.insert(dbClassCourse);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ClassCourseUpdateReqVO reqVO = randomPojo(ClassCourseUpdateReqVO.class, o -> {
            o.setId(dbClassCourse.getId()); // 设置更新的 ID
        });

        // 调用
        classCourseService.updateClassCourse(reqVO);
        // 校验是否更新正确
        ClassCourseDO classCourse = classCourseMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, classCourse);
    }

    @Test
    public void testUpdateClassCourse_notExists() {
        // 准备参数
        ClassCourseUpdateReqVO reqVO = randomPojo(ClassCourseUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> classCourseService.updateClassCourse(reqVO), CLASS_COURSE_NOT_EXISTS);
    }

    @Test
    public void testDeleteClassCourse_success() {
        // mock 数据
        ClassCourseDO dbClassCourse = randomPojo(ClassCourseDO.class);
        classCourseMapper.insert(dbClassCourse);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbClassCourse.getId();

        // 调用
        classCourseService.deleteClassCourse(id);
       // 校验数据不存在了
       assertNull(classCourseMapper.selectById(id));
    }

    @Test
    public void testDeleteClassCourse_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> classCourseService.deleteClassCourse(id), CLASS_COURSE_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetClassCoursePage() {
       // mock 数据
       ClassCourseDO dbClassCourse = randomPojo(ClassCourseDO.class, o -> { // 等会查询到
           o.setClassId(null);
           o.setCourseId(null);
           o.setBeginTime(null);
           o.setEndTime(null);
           o.setTeacherId(null);
           o.setClassroomId(null);
           o.setIsTemporary(null);
           o.setIsMerge(null);
           o.setIsChange(null);
       });
       classCourseMapper.insert(dbClassCourse);
       // 测试 classId 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setClassId(null)));
       // 测试 courseId 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setCourseId(null)));
       // 测试 beginTime 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setBeginTime(null)));
       // 测试 endTime 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setEndTime(null)));
       // 测试 teacherId 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setTeacherId(null)));
       // 测试 classroomId 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setClassroomId(null)));
       // 测试 isTemporary 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setIsTemporary(null)));
       // 测试 isMerge 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setIsMerge(null)));
       // 测试 isChange 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setIsChange(null)));
       // 准备参数
       ClassCoursePageReqVO reqVO = new ClassCoursePageReqVO();
       reqVO.setClassId(null);
       reqVO.setCourseId(null);
//       reqVO.setBeginTime((new LocalDateTime[]{}));
//       reqVO.setEndTime((new LocalDateTime[]{}));
       reqVO.setTeacherId(null);
       reqVO.setClassroomId(null);
       reqVO.setIsTemporary(null);
       reqVO.setIsMerge(null);
       reqVO.setIsChange(null);

       // 调用
       PageResult<ClassCourseDO> pageResult = classCourseService.getClassCoursePage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbClassCourse, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetClassCourseList() {
       // mock 数据
       ClassCourseDO dbClassCourse = randomPojo(ClassCourseDO.class, o -> { // 等会查询到
           o.setClassId(null);
           o.setCourseId(null);
           o.setBeginTime(null);
           o.setEndTime(null);
           o.setTeacherId(null);
           o.setClassroomId(null);
           o.setIsTemporary(null);
           o.setIsMerge(null);
           o.setIsChange(null);
       });
       classCourseMapper.insert(dbClassCourse);
       // 测试 classId 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setClassId(null)));
       // 测试 courseId 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setCourseId(null)));
       // 测试 beginTime 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setBeginTime(null)));
       // 测试 endTime 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setEndTime(null)));
       // 测试 teacherId 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setTeacherId(null)));
       // 测试 classroomId 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setClassroomId(null)));
       // 测试 isTemporary 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setIsTemporary(null)));
       // 测试 isMerge 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setIsMerge(null)));
       // 测试 isChange 不匹配
       classCourseMapper.insert(cloneIgnoreId(dbClassCourse, o -> o.setIsChange(null)));
       // 准备参数
       ClassCourseExportReqVO reqVO = new ClassCourseExportReqVO();
       reqVO.setClassId(null);
       reqVO.setCourseId(null);
//       reqVO.setBeginTime((new LocalDateTime[]{}));
//       reqVO.setEndTime((new LocalDateTime[]{}));
       reqVO.setTeacherId(null);
       reqVO.setClassroomId(null);
       reqVO.setIsTemporary(null);
       reqVO.setIsMerge(null);
       reqVO.setIsChange(null);

       // 调用
       List<ClassCourseDO> list = classCourseService.getClassCourseList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbClassCourse, list.get(0));
    }

}
