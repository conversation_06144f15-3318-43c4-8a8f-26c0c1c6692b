package com.unicom.swdx.module.edu.controller.admin.traineegroup.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 委托班学员分组导入模板 VO
 */
@Data
public class TraineeGroupDelegateImportTemplateVO {

    @ExcelProperty(value = "序号（黄色为必填项灰色为非必填项）", index = 0)
    private String index;

    @ExcelProperty(value = "姓名", index = 1)
    private String name;

    @ExcelProperty(value = "联系方式", index = 2)
    private String phone;

    @ExcelProperty(value = "性别", index = 3)
    private String gender;

    @ExcelProperty(value = "身份证", index = 4)
    private String idCard;

    @ExcelProperty(value = "所在小组", index = 5)
    private String groupName;

    @ExcelProperty(value = "负责人分配", index = 6)
    private String responsiblePerson;
}
