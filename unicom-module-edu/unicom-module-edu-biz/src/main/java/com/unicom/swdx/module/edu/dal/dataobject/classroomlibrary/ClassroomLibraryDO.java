package com.unicom.swdx.module.edu.dal.dataobject.classroomlibrary;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

/**
 * EduClassroomLibrary DO
 *
 * <AUTHOR>
 */
@TableName("edu_classroom_library")
@KeySequence("edu_classroom_library_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassroomLibraryDO extends TenantBaseDO {

    /**
     * 主键id,自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 教室名称
     */
    private String className;
//    /**
//     * 校区名称
//     */
//    private String campusName;
    /**
     * 建筑名称
     */
    private String buildingName;
    /**
     * 容纳人数
     */
    private Integer capacity;
    /**
     * 校区字典id
     */
    private Integer dictDataId;

    /**
     * 是否现场教学点
     */
    private Boolean teachingPoint;

}
