<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherDeptMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <update id="syncTeacherDeptForTenantId" parameterType="java.lang.Long">
        INSERT INTO edu_teacher_dept (teacher_id, teacher_name, dept_id,dept_name,tenant_id)
        select
            t1.id teacher_id,
            t1."name" teacher_name,
            t1.dept_id dept_id,
            t1.dept_name dept_name,
            t1.tenant_id
        from
            (SELECT  eti.id ,
                     eti."name" ,
                     eti.tenant_id,
                     cast(UNNEST(string_to_array(eti.dept_ids, ',')) as varchar) AS dept_id,
                     cast(UNNEST(string_to_array(eti.dept_names, ','))as varchar) AS dept_name
             FROM edu_teacher_information eti
             where eti.deleted = 0
               and eti.dept_ids is not null) t1
        where t1.dept_name is not null
          and t1.dept_id is not null
          and t1."name" is not null
          and t1.tenant_id = #{tenantId}
    </update>
</mapper>
