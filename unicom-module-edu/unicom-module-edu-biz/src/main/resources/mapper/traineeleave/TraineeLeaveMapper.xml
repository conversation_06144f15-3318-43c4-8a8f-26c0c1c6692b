<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.traineeleave.TraineeLeaveMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <sql id="selectlist">
        SELECT etl.id,
        etl.title,
        et.name as traineeName,
        eti.name as classTeacherLeadName,
        etl.status,
        etl.apply_time applyTimeStr,
        etl.class_id,
        ecm.class_name,
        ecm.class_attribute,
        etl.leave_type,
        etl.start_time startTimeStr,
        etl.end_time endTimeStr,
        etl.days,
        etl.reason,
        etl.accessory,
        etl.trainee_id
        FROM edu_trainee_leave etl
        LEFT JOIN edu_trainee et ON etl.trainee_id = et.id
        LEFT JOIN edu_class_management ecm ON etl.class_id = ecm.id
        LEFT JOIN edu_teacher_information eti ON ecm.class_teacher_lead = eti.id
        LEFT JOIN system_users su ON eti.user_id = su.id
        where etl.deleted = 0 AND etl.status IN (2, 3, 4, 5)
        <if test="reqVO.classTeacherLeadId != null">
            AND su.system_id = #{reqVO.classTeacherLeadId}
        </if>
        <if test="reqVO.campus != null">
            AND (
            ecm.campus_ids like CONCAT('%,',CAST(#{reqVO.campus} AS VARCHAR), ',%')
            or
            ecm.campus_ids = CAST(#{reqVO.campus} AS VARCHAR)
            or
            ecm.campus_ids like CONCAT(CAST(#{reqVO.campus} AS VARCHAR), ',%')
            or
            ecm.campus_ids like CONCAT('%,',CAST(#{reqVO.campus} AS VARCHAR))
            )
        </if>
        <if test="reqVO.classStatus != null">
            --   开班中
            <if test="reqVO.classStatus == 2">
                AND ecm."class_open_time" &lt; NOW()
                AND ecm."completion_time" > NOW()
                AND ecm."publish" = '1'
            </if>
            --   已结业
            <if test="reqVO.classStatus == 3">
                AND ecm."completion_time" &lt; NOW()
                AND ecm."publish" = '1'
            </if>
            --  已发布 未开始的班级
            <if test="reqVO.classStatus == 4">
                AND NOW() &lt; ecm."class_open_time"
                AND ecm."publish" = '1'
            </if>
        </if>
        <if test="reqVO.classId != null">
            AND etl.class_id = #{reqVO.classId}
        </if>
        <if test="reqVO.className != null and reqVO.className !=''">
            AND ecm.class_name LIKE CONCAT('%',#{reqVO.className},'%')
        </if>
        <if test="reqVO.classAttribute != null and reqVO.classAttribute !=''">
            AND ecm.class_attribute LIKE CONCAT('%',#{reqVO.classAttribute},'%')
        </if>
        <if test="reqVO.applyTimeStart != null">
            AND etl.apply_time &gt;= #{reqVO.applyTimeStart}
        </if>
        <if test="reqVO.applyTimeEnd != null">
            AND etl.apply_time &lt;= #{reqVO.applyTimeEnd}
        </if>
        <if test="reqVO.traineeName != null and reqVO.traineeName != ''">
            AND et.name LIKE CONCAT('%',#{reqVO.traineeName},'%')
        </if>
        <if test="reqVO.status != null">
            AND etl.status = #{reqVO.status}
        </if>
        <if test="reqVO.leaveType != null">
            AND etl.leave_type = #{reqVO.leaveType}
        </if>
        <!-- 根据点击字段排序 -->
        <if test="reqVO.tag != null and reqVO.seq != null">
            <if test="reqVO.tag == 0">
                order by etl.apply_time
            </if>
            <if test="reqVO.tag == 1">
                order by ecm.class_name
            </if>
            <if test="reqVO.tag == 2">
                order by etl.start_time
            </if>
            <if test="reqVO.tag == 3">
                order by etl.end_time
            </if>
            <choose>
                <when test="reqVO.seq == 1">
                    ASC
                </when>
                <when test="reqVO.seq == 2">
                    DESC
                </when>
            </choose>
        </if>
    </sql>

    <select id="selectPageByReqVO"
            resultType="com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeaveRespVO">
        <include refid="selectlist"/>
    </select>

    <select id="selectListByReqVO"
            resultType="com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeaveRespVO">
        <include refid="selectlist"/>
    </select>

    <select id="selectMyList"
            resultType="com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeaveRespVO">
        SELECT etl.id,
        etl.title,
        etl.status,
        etl.apply_time applyTimeStr,
        etl.leave_type,
        etl.start_time startTimeStr,
        etl.end_time endTimeStr,
        etl.days
        FROM edu_trainee_leave etl
        where etl.deleted = 0 AND etl.status != 0
        AND etl.trainee_user_id = #{reqVO.traineeUserId}
        <if test="reqVO.classId != null">
            AND etl.class_id = #{reqVO.classId}
        </if>
        <if test="reqVO.traineeId != null">
            AND etl.trainee_id = #{reqVO.traineeId}
        </if>
        <if test="reqVO.applyTimeStart != null and reqVO.applyTimeEnd != null">
            AND not (etl.start_time &gt; #{reqVO.applyTimeEnd} or etl.end_time &lt; #{reqVO.applyTimeStart})
        </if>
        <if test="reqVO.status != null">
            AND etl.status = #{reqVO.status}
        </if>
        <if test="reqVO.leaveType != null">
            AND etl.leave_type = #{reqVO.leaveType}
        </if>
        order by etl.apply_time DESC
    </select>

    <sql id="selectBase">
        SELECT etl.id,
               etl.title,
               et.name as traineeName,
               eti.name as classTeacherLeadName,
               etl.status,
               etl.apply_time applyTimeStr,
               etl.class_id,
               ecm.class_name,
               etl.leave_type,
               etl.start_time startTimeStr,
               etl.end_time endTimeStr,
               etl.days,
               etl.reason,
               etl.tenant_id,
               etl.accessory
        FROM edu_trainee_leave etl
                 LEFT JOIN edu_trainee et ON etl.trainee_id = et.id
                 LEFT JOIN edu_class_management ecm ON etl.class_id = ecm.id
                 LEFT JOIN edu_teacher_information eti ON ecm.class_teacher_lead = eti.id
    </sql>
    <select id="getDetailById"
            resultType="com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeaveRespVO">
        <include refid="selectBase"/>
        where etl.id = #{leaveId}
    </select>
    <select id="selectListByTraineeIdListAndTime"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveDO">
        select
            id,
            trainee_user_id,
            class_id,
            status,
            leave_type,
            reason,
            start_time,
            end_time,
            days,
            title,
            accessory,
            create_time,
            apply_time,
            trainee_id
        from
            edu_trainee_leave etl
        where etl.deleted = 0
            <if test="traineeIdList != null and traineeIdList.size() > 0">
                and etl.trainee_id in
                <foreach collection="traineeIdList" item="traineeId" separator="," open="(" close=")">
                    #{traineeId}
                </foreach>
            </if>
            <if test="traineeIdList == null or traineeIdList.size() == 0">
                and 1!=1
            </if>
            and etl.start_time &lt;= #{beginTime}
            and etl.end_time &gt;= #{endTime}
            <if test="leaveStatusList != null and leaveStatusList.size() > 0">
                and etl.status in
                <foreach collection="leaveStatusList" item="leaveStatus" separator="," open="(" close=")">
                    #{leaveStatus}
                </foreach>
            </if>
        order by etl.end_time
    </select>

    <select id="selectListByTraineeIdListAndStartTime"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveDO">
        select
        id,
        trainee_user_id,
        class_id,
        status,
        leave_type,
        reason,
        start_time,
        end_time,
        days,
        title,
        accessory,
        create_time,
        apply_time,
        trainee_id
        from
        edu_trainee_leave etl
        where etl.deleted = 0
        <if test="traineeIdList != null and traineeIdList.size() > 0">
            and etl.trainee_id in
            <foreach collection="traineeIdList" item="traineeId" separator="," open="(" close=")">
                #{traineeId}
            </foreach>
        </if>
        <if test="traineeIdList == null or traineeIdList.size() == 0">
            and 1!=1
        </if>
        and #{beginTime} between etl.start_time and etl.end_time
        <if test="leaveStatusList != null and leaveStatusList.size() > 0">
            and etl.status in
            <foreach collection="leaveStatusList" item="leaveStatus" separator="," open="(" close=")">
                #{leaveStatus}
            </foreach>
        </if>
        order by etl.end_time
    </select>


    <select id="getLeaveCountOfTeacher" resultType="java.lang.Integer">
        select count(1)
        from edu_trainee_leave etl
        left join edu_class_management ecm on etl.class_id = ecm.id
        left join edu_teacher_information eti on ecm.class_teacher_lead = eti.id
        left join system_users su on eti.user_id = su.id
        where etl.deleted = 0
          and etl.status = 2
          and su.id = #{userId}
          and etl.class_id = #{classId}
    </select>
    <select id="selectTeacherList"
            resultType="com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeaveRespVO">
        SELECT etl.id,
            et.name as traineeName,
            etl.status,
            etl.apply_time applyTimeStr,
            etl.leave_type,
            etl.days
        FROM edu_trainee_leave etl
        LEFT JOIN edu_trainee et ON etl.trainee_id = et.id
        WHERE etl.deleted = 0
        AND etl.status != 0 AND etl.status != 1
        <if test="classId != null">
            AND etl.class_id = #{classId}
        </if>
        <if test="status == 0">
            AND etl.status = 2
        </if>
        <if test="status == 1">
            AND (etl.status = 3 or etl.status = 4)
        </if>
        <if test="status == 2">
            AND etl.status = 5
        </if>
        order by etl.apply_time DESC
    </select>
</mapper>
