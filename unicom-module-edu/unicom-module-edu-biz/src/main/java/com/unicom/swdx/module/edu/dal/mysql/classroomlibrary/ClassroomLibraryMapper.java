package com.unicom.swdx.module.edu.dal.mysql.classroomlibrary;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo.ClassroomLibraryExcelVO;
import com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo.ClassroomLibraryPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo.ClassroomLibraryParasVO;
import com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo.ClassroomLibrarySimpleRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.classroomlibrary.ClassroomLibraryDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;


/**
 * EduClassroomLibrary Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ClassroomLibraryMapper extends BaseMapperX<ClassroomLibraryDO> {


    default PageResult<ClassroomLibraryDO> selectPage(ClassroomLibraryPageReqVO reqVO) {
        LambdaQueryWrapperX<ClassroomLibraryDO> queryWrapper = new LambdaQueryWrapperX<ClassroomLibraryDO>()
                .likeIfPresent(ClassroomLibraryDO::getClassName, reqVO.getClassName())
                .likeIfPresent(ClassroomLibraryDO::getBuildingName, reqVO.getBuildingName())
                .eq(ClassroomLibraryDO::getTeachingPoint, false); // 排除教学点教室

        // 根据前端传的 Change 值来判断排序方式
        if (reqVO.getChange() != null) {
            if (reqVO.getChange().equals(0)) {
                // 升序
                queryWrapper.orderByAsc(ClassroomLibraryDO::getClassName);
            } else if (reqVO.getChange().equals(1)) {
                // 降序
                queryWrapper.orderByDesc(ClassroomLibraryDO::getClassName);
            }
        } else {
            // 如果没有提供 Change 值，默认升序
            queryWrapper.orderByAsc(ClassroomLibraryDO::getClassName);
        }

        return selectPage(reqVO, queryWrapper);
    }

    default ClassroomLibraryDO selectByClassName(String className) {
        return selectOne(ClassroomLibraryDO::getClassName, className);
    }


    ClassroomLibraryExcelVO getClassroomLibraryDataById(@Param("id") String id);

    List<ClassroomLibraryExcelVO> getClassroomLibraryDataAll(@Param("params") ClassroomLibraryParasVO params);

    /**
     * 根据选修课发布上课时间段获取空闲下拉教室数据
     * @param classStartDateTime 上课开始时间
     * @param classEndDateTime 上课结束时间
     * @param excludeReleaseId 排除的选修课发布id
     * @return 教室信息
     */
    List<ClassroomLibrarySimpleRespVO> listForElectiveRelease(@Param("classStartDateTime") LocalDateTime classStartDateTime,
                                                              @Param("classEndDateTime") LocalDateTime classEndDateTime,
                                                              @Param("excludeReleaseId") Long excludeReleaseId,
                                                              @Param("excludeClassCourseId") Long excludeClassCourseId);

    /**
     * 获取教室
     *   是否排课
     *
     */
    Integer getClassCourseByClassRoomId(@Param("classRoomId") Integer classRoomId);
}
