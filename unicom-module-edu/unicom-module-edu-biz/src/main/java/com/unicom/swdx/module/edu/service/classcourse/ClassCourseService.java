package com.unicom.swdx.module.edu.service.classcourse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.classcourse.dto.ClassMergeCourseGroupRespDTO;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.*;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoRespVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 班级课程安排 Service 接口
 *
 * <AUTHOR>
 */
public interface ClassCourseService extends IService<ClassCourseDO> {

    /**
     * 创建班级课程安排
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createClassCourse(@Valid ClassCourseCreateReqVO createReqVO);

    /**
     * 更新班级课程安排
     *
     * @param updateReqVO 更新信息
     */
    void updateClassCourse(@Valid ClassCourseUpdateReqVO updateReqVO);

    /**
     * 删除班级课程安排
     *
     * @param id 编号
     */
    void deleteClassCourse(Long id);

    /**
     * 获得班级课程安排
     *
     * @param id 编号
     * @return 班级课程安排
     */
    ClassCourseRespVO getClassCourse(Long id);

    /**
     * 获得班级课程安排列表
     *
     * @param ids 编号
     * @return 班级课程安排列表
     */
    List<ClassCourseDO> getClassCourseList(Collection<Long> ids);

    /**
     * 获得班级课程安排分页
     *
     * @param pageReqVO 分页查询
     * @return 班级课程安排分页
     */
    PageResult<ClassCourseDO> getClassCoursePage(ClassCoursePageReqVO pageReqVO);

    /**
     * 获得班级课程安排列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 班级课程安排列表
     */
    List<ClassCourseDO> getClassCourseList(ClassCourseExportReqVO exportReqVO);

    /**
     * 获取合班授课分组信息列表
     * @param classCourseIds 筛选出包含这些排课id的合班授课分组信息列表
     * @return 合班授课分组信息列表
     */
    List<ClassMergeCourseGroupRespDTO> getClassMergeCourseGroup(List<Long> classCourseIds);

    /**
     * 获取法定节假日
     *
     * @return 法定节假日安排JSON
     */
    Set<String> getVacation(int year, int month);

    /**
     * 批量更新或新增，对象中id字段不为空则为更新，为空则为新增
     *
     * @param updateReqVOList
     */
    void updateBatch(List<ClassCourseUpdateReqVO> updateReqVOList);

    /**
     * 课表批量更新
     *
     * @param updateReqVOList
     */
    void updateTableBatch(String status, List<ClassCourseUpdateReqVO> updateReqVOList);

    /**
     * 获得我的课表分页
     *
     * @param myClassScheduleParamsVO 分页查询
     * @return MyClassScheduleVO
     */
    List<ScheduleChartVO> getMyClassSchedule(MyClassScheduleParamsVO myClassScheduleParamsVO);

    ClassTableRespVO getTimeTable(ClassTimeTableReqVO reqVO);

    Workbook exportTimeTable(ClassTimeTableReqVO reqVO, List<ClassTimeTableRespVO> list, HttpServletResponse response);

    List<ScheduleChartVO> getClassLeaderSchedule(MyClassScheduleParamsVO myClassScheduleParamsVO);

    List<StatVO> getStat(ClassCoursePageReqVO pageVO);

    void updateClassCourseByClockingIn(ClassCourseClockingInVO clockingInVO);

    /**
     * 获取发布课表下拉列表
     *
     * @param reqVO 请求参数
     * @return 下拉列表
     */
    List<ClassCourseSimpleRespVO> getSimpleList(ClassCourseSimpleReqVO reqVO);

    /**
     * 根据午别、上课日期获取选修课上课时间段
     *
     * @param dayPeriod 午别
     * @param classDate 上课日期
     * @return 上课时段
     */
    List<String> getClassTimeByPeriodAndDate(Integer dayPeriod, String classDate);

    /**
     * 获取全校课表
     * @return 全校所有开课中班级课表信息
     */
    List<ClassTableAllRespVO> getTimeTableAll(ClassTimeTableReqVO reqVO);

    List<Long> getTimeTableAllOrderClassIdList(ClassTimeTableReqVO reqVO);

    Boolean updateTimeTableAllOrderClassIdList(String classOrder);

    Set<Map.Entry<String, Map<String, List<UserAppletCourseResponseVO.Data.Sql1>>>> getTimeTableAllApplet(ClassTimeTableReqVO reqVO);

    /**
     * 根据传入的userId返回teacherId
     * @param userId
     * @return userId
     */
    Long getTeacherIdByUserId(Long userId);

    /**
     * 导出全校课表
     * @param reqVO
     * @param list
     * @param response
     * @return 导出文件
     */
    Workbook exportTimeTableAll(ClassTimeTableReqVO reqVO, List<ClassTableAllRespVO> list, HttpServletResponse response);

    /**
     * 获得班级课程最后一节课的排课日期，用于调课时快速定位
     * @param classId 班级id
     * @return 最后一节课的日期
     */
    String getLatestDate(Long classId);

    /**
     * 获取一个排课的合班授课的班级列表
     * @param classCourseId 排课id
     * @return 合班授课的班级列表
     */
    List<ClassInfoRespVO> getMergedClassList(Long classCourseId);

    /**
     * 更新班级与课程绑定信息的方法
     * 此方法主要用于更新班级与课程之间的绑定关系，确保课程能够在指定的班级中显示和使用
     *
     * @param updateReqVO 包含更新信息的请求对象，包括班级ID列表、课程ID等必要信息
     */
    void updateClassCourseBinding(@Valid ClassCourseBindingUpdateReqVO updateReqVO);

    /**
     * 获取合班选课的班级列表
     * @param classCourseId 排课id
     * @return 合班选课的班级列表
     */
    List<ClassManagementRespVO> getClassIdMergeSelect(Long classCourseId);

    /**
     * 获取一个排课时段的合班授课的班级列表
     * @param reqVO 合班信息信息
     * @return 合班选课的班级列表
     */
    List<ClassInfoRespVO> getMergedClassListByReqVO(ClassMergeReqVO reqVO);

    /**
     * 批量创建班级课程安排
     *
     * @param createReqVOList 创建信息列表
     * @param classCourseId  班级课程安排id
     * @return 创建成功的编号
     */
    void createBatch(Long classCourseId , List<ClassCourseCreateReqVO> createReqVOList);

    /**
     * 获取合并信息
     * 该方法用于处理一组班级与课程绑定的更新请求，提取或生成有关这些绑定的合并信息
     * 主要用途是当需要对多个班级课程绑定进行更新时，通过此方法可以获取到所有绑定的合并信息，
     * 以便在更新前进行统一的处理或校验
     *
     * @param classCourseBindingUpdateReqVOList 班级课程绑定更新请求列表
     *              这是一个包含多个班级课程绑定更新请求的列表，每个请求包含了需要更新的班级课程绑定的信息
     * @return List<String> 合并信息列表
     *         返回一个包含所有合并信息的字符串列表，这些信息是根据输入的班级课程绑定更新请求生成的
     */
    List<ClassCourseBindingInfoRespVO> getMergeInfo(List<ClassCourseBindingUpdateReqVO> classCourseBindingUpdateReqVOList);

    /**
     * 撤销已发布的课程
     * 该方法将根据班级ID撤销尚未开始的课程发布状态，将其标记为暂存状态
     *
     * @param classId 班级ID，用于标识需要撤销发布的班级
     */
    void undoPublish(Long classId);

    /**
     * 获取班级课程安排列表
     * 该方法用于获取班级课程安排列表，根据传入的分页参数和查询条件返回符合条件的班级课程安排列表
     *
     * @param pageVO 分页参数，用于控制返回的班级课程安排列表的分页信息
     * @return List<ClassCourseByClassManagementRespVO> 班级课程安排列表
     *         返回一个班级课程安排列表，列表中的每个元素都包含有关该班级课程安排信息的字段
     */
    List<ClassCourseByClassManagementRespVO> getClassCoursePageByClassManagement(ClassCoursePageReqVO pageVO);

    /**
     * 获取与课程班级相关的人员信息，包括班主任、任课教师等。
     *
     * @param list 课程班级信息列表，用于提取班级ID和教师ID字符串
     * @return 包含相关教师信息的VO对象，如班主任ID、姓名及所有任课教师ID和姓名列表
     */
    ClassCourseRelatedPeopleInfoVO getRelatedPeopleInfo(List<ClassCourseDO> list);
}
