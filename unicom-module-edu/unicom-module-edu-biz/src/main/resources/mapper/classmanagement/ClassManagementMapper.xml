<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <resultMap id="TraineeReportPageResultMap" type="com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.TraineeReportPageRespVO">
        <id property="id" column="id" />
        <result property="campusIds" column="campus_ids" typeHandler="com.unicom.swdx.framework.mybatis.core.type.ListLongTypeHandler" />
    </resultMap>

    <resultMap id="getPageForElectiveReleaseCreateMap" type="com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementElectiveReleaseRespVO">
        <id property="id" column="id" />
        <result property="campusIds" column="campus_ids" typeHandler="com.unicom.swdx.framework.mybatis.core.type.ListLongTypeHandler" />
    </resultMap>

    <resultMap id="selectTraineeReportListMap" type="com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.TraineeReportPageRespVO">
        <id property="id" column="id" />
        <result property="campusIds" column="campus_ids" typeHandler="com.unicom.swdx.framework.mybatis.core.type.ListLongTypeHandler" />
    </resultMap>

    <resultMap id="classManagerDOMap" type="com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO">
        <id property="id" column="id" />
        <result property="campusIds" column="campus_ids" typeHandler="com.unicom.swdx.framework.mybatis.core.type.ListLongTypeHandler" />
    </resultMap>

    <sql id="traineeReportPageList">
        select
        ecm.id,
        ecm.class_name_code ,
        ecm.class_name ,
        ecm.registration_start_time ,
        ecm.registration_end_time ,
        ecm.class_open_time ,
        ecm.class_attribute,
        ecm.completion_time ,
        ecm.campus,
        ecm.campus_ids,
        ecm.publish,
        case
        when esuu.is_restrict = 0 then esuu.capacity
        else '无限制'
        end as capacity
        from
        edu_class_management ecm
        left join edu_sign_up_unit esuu on
        ecm.id = esuu.class_id
        and esuu.deleted = 0
        where
        ecm.deleted = 0
        and esuu.parent_id = #{reqVO.unitId}
        <if test="reqVO.className != null and reqVO.className != ''">
            and ecm.class_name like concat('%',#{reqVO.className},'%')
        </if>
        <if test="reqVO.year != null and reqVO.year != ''">
            and ecm.year = #{reqVO.year}
        </if>
        <if test="reqVO.classStatus != null">
            --  报名中
            <if test="reqVO.classStatus == 0">
                AND ecm."registration_start_time" &lt; NOW()
                AND ecm."registration_end_time" > NOW()
                AND ecm."publish" = '1'
                AND ecm."class_open_time" != ecm."registration_start_time"
            </if>
            --   报名结束
            <if test="reqVO.classStatus == 1">
                AND ecm."registration_end_time" &lt; NOW()
                AND ecm."class_open_time" > NOW()
                AND ecm."publish" = '1'
            </if>
            --   开班中
            <if test="reqVO.classStatus == 2">
                AND ecm."class_open_time" &lt; NOW()
                AND ecm."completion_time" > NOW()
                AND ecm."publish" = '1'
            </if>
            --   已结束
            <if test="reqVO.classStatus == 3">
                AND ecm."completion_time" &lt; NOW()
                AND ecm."publish" = '1'
            </if>
            --  已发布 未开始的班级
            <if test="reqVO.classStatus == 4">
                AND NOW() &lt; ecm."class_open_time"
                AND ecm."publish" = '1'
            </if>

        </if>
        order by
        <choose>
            <when test="reqVO.order == 1"> ecm.class_name_code </when>
            <when test="reqVO.order == 2"> ecm.class_name </when>
            <when test="reqVO.order == 3"> ecm.registration_start_time </when>
            <when test="reqVO.order == 4"> ecm.class_open_time </when>
        </choose>
        <choose>
            <when test="reqVO.orderType == 'asc'"> ASC </when>
            <when test="reqVO.orderType == 'desc'"> DESC </when>
        </choose>
    </sql>

    <select id="selectClassCount" resultType="java.lang.Integer">
        select
            count(1)
        from
            edu_class_management ecm
        where
            ecm."year" = #{year}
    </select>


    <select id="selectGroupedByTeacherLimit" resultType="java.lang.Integer">

        SELECT
            class_teacher_lead
        FROM
            edu_class_management
        WHERE class_teacher_lead >0 and deleted=0
        GROUP BY
            class_teacher_lead
            LIMIT 100

    </select>


    <select id="selectPageList" resultMap="classManagerDOMap">
        SELECT
           ecm."id",
           ecm."class_name_code",
           ecm."class_name",
           ecm."class_type_dict_id",
           ecm."class_attribute",
           ecm."year",
           ecm."semester",
           ecm."learning_system",
           ecm."training_object",
           ecm."people_number",
           ecm."turn",
           ecm."campus",
           ecm."campus_ids",
           ecm."reporting_time",
           ecm."class_open_time",
           ecm."completion_time",
           ecm."registration_start_time",
           ecm."payment_report",
           ecm."evaluate",
           ecm."accessory",
           ecm."sort",
           ecm."remark",
           ecm."publish",
           ecm."deleted",
           ecm."registration_end_time",
           ecm."class_teacher_lead",
           ecm."coach_teacher",
           ecm.administrative_teachers,
           ecm."attendance_check",
           ecm."meal_attendance",
           ecm."check_in",
           ecm.id_code,
           ecm.class_type,
           ecm.accommodation_location,
           ecm.report_period,
           ecm.return_period,
           ecm.program_initiator,
           ecm.program_closer,
            (
            select
            ecct.template_name
            from
            edu_class_completion_template ecct
            where
            ecct.deleted = 0
            and ecct.id_code = ecm.id_code
            limit 1)
            as idCodeName
        FROM
            "edu_class_management" ecm
        where
           ecm."deleted" = '0'
        <if test="reqVO.className != null and reqVO.className != ''">
            AND ecm."class_name" LIKE CONCAT('%', #{reqVO.className}, '%')
        </if>
        <if test="reqVO.year != null">
            AND ecm."year" =  #{reqVO.year}
        </if>
        <if test="reqVO.semester != null">
            AND ecm."semester" = #{reqVO.semester}
        </if>
        <if test="reqVO.campus != null">
            AND (
            ecm.campus_ids like CONCAT('%,',CAST(#{reqVO.campus} AS VARCHAR), ',%')
            or
            ecm.campus_ids = CAST(#{reqVO.campus} AS VARCHAR)
            or
            ecm.campus_ids like CONCAT(CAST(#{reqVO.campus} AS VARCHAR), ',%')
            or
            ecm.campus_ids like CONCAT('%,',CAST(#{reqVO.campus} AS VARCHAR))
            )
        </if>
        <if test="reqVO.publish != null">
            AND ecm."publish" = #{reqVO.publish}
        </if>

        <if test="reqVO.classAttribute != null">
            AND ecm."class_attribute" = #{reqVO.classAttribute}
        </if>

        <if test="reqVO.endTime != null and reqVO.endTime != '' and reqVO.startTime != null and reqVO.startTime != ''" >
            AND  #{reqVO.endTime} >= ecm."class_open_time"
            AND  ecm."class_open_time" >= #{reqVO.startTime}
        </if>

        <if test="reqVO.queryStartDate != null and reqVO.queryEndDate != null">
            AND (
                #{reqVO.queryEndDate} &gt;= ecm."class_open_time"
                AND #{reqVO.queryStartDate} &lt;= ecm."completion_time"
                )
        </if>

        <if test="reqVO.classTeacherLead != null">
            AND (
            ecm."class_teacher_lead" = #{reqVO.classTeacherLead}
            OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.classTeacherLead} AS VARCHAR), ',%')
            OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.classTeacherLead} AS VARCHAR))
            OR ecm."coach_teacher" LIKE CONCAT(CAST(#{reqVO.classTeacherLead} AS VARCHAR), ',%')
            OR ecm."coach_teacher" = CAST(#{reqVO.classTeacherLead} AS VARCHAR)
            )
        </if>

        <if test="reqVO.teacherId != null">
            AND (
            ecm."class_teacher_lead" = #{reqVO.teacherId}
            OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR))
            OR ecm."coach_teacher" LIKE CONCAT(CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
            OR ecm."coach_teacher" = CAST(#{reqVO.teacherId} AS VARCHAR)
            )
        </if>

        <if test="reqVO.classNameCode != null and reqVO.classNameCode != ''">
            AND ecm."class_name_code" = #{reqVO.classNameCode}
        </if>

        <if test="reqVO.isFiltrate != null">
            AND ecm."publish" = #{reqVO.isFiltrate}
        </if>

        <if test="reqVO.classStatus != null">
            --  报名中
            <if test="reqVO.classStatus == 0">
                AND ecm."registration_start_time" &lt; NOW()
                AND ecm."registration_end_time" > NOW()
                AND ecm."publish" = '1'
                AND ecm."class_open_time" != ecm."registration_start_time"
            </if>
            --   报名结束
            <if test="reqVO.classStatus == 1">
                AND ecm."registration_end_time" &lt; NOW()
                AND ecm."class_open_time" > NOW()
                AND ecm."publish" = '1'
            </if>
            --   开班中
            <if test="reqVO.classStatus == 2">
                AND ecm."class_open_time" &lt; NOW()
                AND ecm."completion_time" > NOW()
                AND ecm."publish" = '1'
            </if>
            --   已结束
            <if test="reqVO.classStatus == 3">
                AND ecm."completion_time" &lt; NOW()
                AND ecm."publish" = '1'
            </if>
            --  已发布 未开始的班级
            <if test="reqVO.classStatus == 4">
                AND NOW() &lt; ecm."class_open_time"
                AND ecm."publish" = '1'
            </if>

        </if>

        <!-- 如果 change 不为空，则进行排序 -->
        <if test="reqVO.change != null">

            <choose>
                <when test="reqVO.tag == 0">
                    ORDER BY ecm."sort"
                </when>
                <when test="reqVO.tag == 1">
                    ORDER BY ecm."class_name_code"
                </when>
                <when test="reqVO.tag == 2">
                    ORDER BY ecm."class_name"
                </when>
                <when test="reqVO.tag == 3">
                    ORDER BY ecm."class_open_time"
                </when>
                <when test="reqVO.tag == 4">
                    ORDER BY ecm."class_attribute"
                </when>
            </choose>

            <choose>
                <when test="reqVO.change == 0">
                    ASC
                </when>
                <when test="reqVO.change == 1">
                    DESC
                </when>
            </choose>

            <if test="reqVO.tag == 0">
                <choose>
                    <when test="reqVO.change == 0">
                        , ecm."create_time" ASC
                    </when>
                    <when test="reqVO.change == 1">
                        , ecm."create_time" DESC
                    </when>
                </choose>
            </if>
        </if>
    </select>

    <select id="selectClassManagementList" resultMap="classManagerDOMap">
        SELECT
        ecm."id",
        ecm."class_name_code",
        ecm."class_name",
        ecm."class_type_dict_id",
        ecm."class_attribute",
        ecm."year",
        ecm."semester",
        ecm."learning_system",
        ecm."training_object",
        ecm."people_number",
        ecm."turn",
        ecm."campus",
        ecm."campus_ids",
        ecm."reporting_time",
        ecm."class_open_time",
        ecm."completion_time",
        ecm."registration_start_time",
        ecm."payment_report",
        ecm."evaluate",
        ecm."accessory",
        ecm."sort",
        ecm."remark",
        ecm."publish",
        ecm."deleted",
        ecm."registration_end_time",
        ecm."class_teacher_lead",
        ecm."coach_teacher",
        ecm.administrative_teachers,
        ecm."attendance_check",
        ecm."meal_attendance",
        ecm."check_in",
        ecm.id_code,
        (
        select
        ecct.template_name
        from
        edu_class_completion_template ecct
        where
        ecct.deleted = 0
        and ecct.id_code = ecm.id_code
        limit 1)
        as idCodeName
        FROM
        "edu_class_management" ecm
        where
        ecm."deleted" = '0'
        <if test="reqVO.className != null and reqVO.className != ''">
            AND ecm."class_name" LIKE CONCAT('%', #{reqVO.className}, '%')
        </if>
        <if test="reqVO.year != null">
            AND ecm."year" =  #{reqVO.year}
        </if>
        <if test="reqVO.semester != null">
            AND ecm."semester" = #{reqVO.semester}
        </if>
        <if test="reqVO.campus != null">
            AND (
            ecm.campus_ids like CONCAT('%,',CAST(#{reqVO.campus} AS VARCHAR), ',%')
            or
            ecm.campus_ids = CAST(#{reqVO.campus} AS VARCHAR)
            or
            ecm.campus_ids like CONCAT(CAST(#{reqVO.campus} AS VARCHAR), ',%')
            or
            ecm.campus_ids like CONCAT('%,',CAST(#{reqVO.campus} AS VARCHAR))
            )
        </if>
        <if test="reqVO.publish != null">
            AND ecm."publish" = #{reqVO.publish}
        </if>

        <if test="reqVO.classAttribute != null">
            AND ecm."class_attribute" = #{reqVO.classAttribute}
        </if>

        <if test="reqVO.endTime != null and reqVO.endTime != '' and reqVO.startTime != null and reqVO.startTime != ''" >
            AND  #{reqVO.endTime} >= ecm."class_open_time"
            AND  ecm."class_open_time" >= #{reqVO.startTime}
        </if>

        <if test="reqVO.classTeacherLead != null">
            AND (
            ecm."class_teacher_lead" = #{reqVO.classTeacherLead}
            OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.classTeacherLead} AS VARCHAR), ',%')
            OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.classTeacherLead} AS VARCHAR))
            OR ecm."coach_teacher" LIKE CONCAT(CAST(#{reqVO.classTeacherLead} AS VARCHAR), ',%')
            OR ecm."coach_teacher" = CAST(#{reqVO.classTeacherLead} AS VARCHAR)
            )
        </if>

        <if test="reqVO.classNameCode != null and reqVO.classNameCode != ''">
            AND ecm."class_name_code" = #{reqVO.classNameCode}
        </if>

        <if test="reqVO.isFiltrate != null">
            AND ecm."publish" = #{reqVO.isFiltrate}
        </if>

        <if test="reqVO.classStatus != null">
            --  报名中
            <if test="reqVO.classStatus == 0">
                AND ecm."registration_start_time" &lt; NOW()
                AND ecm."registration_end_time" > NOW()
                AND ecm."publish" = '1'
                AND ecm."class_open_time" != ecm."registration_start_time"
            </if>
            --   报名结束
            <if test="reqVO.classStatus == 1">
                AND ecm."registration_end_time" &lt; NOW()
                AND ecm."class_open_time" > NOW()
                AND ecm."publish" = '1'
            </if>
            --   开班中
            <if test="reqVO.classStatus == 2">
                AND ecm."class_open_time" &lt; NOW()
                AND ecm."completion_time" > NOW()
                AND ecm."publish" = '1'
            </if>
            --   已结束
            <if test="reqVO.classStatus == 3">
                AND ecm."completion_time" &lt; NOW()
                AND ecm."publish" = '1'
            </if>
            --  已发布 未开始的班级
            <if test="reqVO.classStatus == 4">
                AND NOW() &lt; ecm."class_open_time"
                AND ecm."publish" = '1'
            </if>

        </if>

        <!-- 如果 change 不为空，则进行排序 -->
        <if test="reqVO.change != null">

            <choose>
                <when test="reqVO.tag == 0">
                    ORDER BY ecm."sort"
                </when>
                <when test="reqVO.tag == 1">
                    ORDER BY ecm."class_name_code"
                </when>
                <when test="reqVO.tag == 2">
                    ORDER BY ecm."class_name"
                </when>
                <when test="reqVO.tag == 3">
                    ORDER BY ecm."class_open_time"
                </when>
                <when test="reqVO.tag == 4">
                    ORDER BY ecm."class_attribute"
                </when>
            </choose>

            <choose>
                <when test="reqVO.change == 0">
                    ASC
                </when>
                <when test="reqVO.change == 1">
                    DESC
                </when>
            </choose>

            <if test="reqVO.tag == 0">
                <choose>
                    <when test="reqVO.change == 0">
                        , ecm."create_time" ASC
                    </when>
                    <when test="reqVO.change == 1">
                        , ecm."create_time" DESC
                    </when>
                </choose>
            </if>
        </if>
    </select>

    <update id="updateClassManagementSortById">
        update
            edu_class_management
        set
           sort = #{sort}
        where
            id = #{id}
    </update>

    <update id="updateClassManagementPublishById">
        update
            edu_class_management
        set
            "publish" = 1
        where
            id = #{id}
    </update>

    <update id="updateClassManagementCancelPublishById">
        update
            edu_class_management
        set
            "publish" = 2
        where
            id = #{id}
    </update>

    <select id="getClassManagementInfo" resultMap="classManagerDOMap">
        SELECT
        ecm."id",
        ecm."class_name_code",
        ecm."class_name",
        ecm."class_type_dict_id",
        ecm."class_attribute",
        ecm."year",
        ecm."semester",
        ecm."learning_system",
        ecm."training_object",
        ecm."people_number",
        ecm."turn",
        ecm."campus",
        ecm."campus_ids",
        ecm."reporting_time",
        ecm."class_open_time",
        ecm."completion_time",
        ecm."registration_start_time",
        ecm."payment_report",
        ecm."evaluate",
        ecm."accessory",
        ecm."sort",
        ecm."remark",
        ecm."publish",
        ecm."deleted",
        ecm."registration_end_time",
        ecm."class_teacher_lead",
        ecm."coach_teacher",
        ecm.administrative_teachers,
        ecm."attendance_check",
        ecm."meal_attendance",
        ecm."check_in"
        FROM
        "edu_class_management" ecm
        where
        ecm."deleted" = '0'
        <choose>
            <when test="reqVO.idList != null and reqVO.idList.size() > 0">
                and ecm.id in
                <foreach collection="reqVO.idList" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                <if test="reqVO.className != null and reqVO.className != ''">
                    AND ecm."class_name" LIKE CONCAT('%', #{reqVO.className}, '%')
                </if>
                <if test="reqVO.year != null">
                    AND ecm."year" =  #{reqVO.year}
                </if>
                <if test="reqVO.semester != null">
                    AND ecm."semester" = #{reqVO.semester}
                </if>
                <if test="reqVO.campus != null">
                    AND (
                    ecm.campus_ids like CONCAT('%,',CAST(#{reqVO.campus} AS VARCHAR), ',%')
                    or
                    ecm.campus_ids = CAST(#{reqVO.campus} AS VARCHAR)
                    or
                    ecm.campus_ids like CONCAT(CAST(#{reqVO.campus} AS VARCHAR), ',%')
                    or
                    ecm.campus_ids like CONCAT('%,',CAST(#{reqVO.campus} AS VARCHAR))
                    )
                </if>
                <if test="reqVO.publish != null">
                    AND ecm."publish" = #{reqVO.publish}
                </if>

                <if test="reqVO.classAttribute != null">
                    AND ecm."class_attribute" = #{reqVO.classAttribute}
                </if>
                <if test="reqVO.endTime != null and reqVO.endTime != '' and reqVO.startTime != null and reqVO.startTime != ''" >
                    AND  #{reqVO.endTime} >= ecm."class_open_time"
                    AND  ecm."class_open_time" >= #{reqVO.startTime}
                </if>
                <if test="reqVO.classTeacherLead != null">
                    AND (
                    ecm."class_teacher_lead" = #{reqVO.classTeacherLead}
                    OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.classTeacherLead} AS VARCHAR), ',%')
                    OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.classTeacherLead} AS VARCHAR))
                    OR ecm."coach_teacher" LIKE CONCAT(CAST(#{reqVO.classTeacherLead} AS VARCHAR), ',%')
                    OR ecm."coach_teacher" = CAST(#{reqVO.classTeacherLead} AS VARCHAR)
                    )
                </if>

                <if test="reqVO.teacherId != null">
                    AND (
                    ecm."class_teacher_lead" = #{reqVO.teacherId}
                    OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
                    OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.teacherId} AS VARCHAR))
                    OR ecm."coach_teacher" LIKE CONCAT(CAST(#{reqVO.teacherId} AS VARCHAR), ',%')
                    OR ecm."coach_teacher" = CAST(#{reqVO.teacherId} AS VARCHAR)
                    )
                </if>

                <if test="reqVO.classNameCode != null and reqVO.classNameCode != ''">
                    AND ecm."class_name_code" = #{reqVO.classNameCode}
                </if>
                <if test="reqVO.isFiltrate != null">
                    AND ecm."publish" = #{reqVO.isFiltrate}
                </if>
                <if test="reqVO.classStatus != null">
                    --  报名中
                    <if test="reqVO.classStatus == 0">
                        AND ecm."registration_start_time" &lt; NOW()
                        AND ecm."registration_end_time" > NOW()
                        AND ecm."publish" = '1'
                        AND ecm."class_open_time" != ecm."registration_start_time"
                    </if>
                    --   报名结束
                    <if test="reqVO.classStatus == 1">
                        AND ecm."registration_end_time" &lt; NOW()
                        AND ecm."class_open_time" > NOW()
                        AND ecm."publish" = '1'
                    </if>
                    --   开班中
                    <if test="reqVO.classStatus == 2">
                        AND ecm."class_open_time" &lt; NOW()
                        AND ecm."completion_time" > NOW()
                        AND ecm."publish" = '1'
                    </if>
                    --   已结束
                    <if test="reqVO.classStatus == 3">
                        AND ecm."completion_time" &lt; NOW()
                        AND ecm."publish" = '1'
                    </if>
                    --  已发布 未开始的班级
                    <if test="reqVO.classStatus == 4">
                        AND NOW() &lt; ecm."class_open_time"
                        AND ecm."publish" = '1'
                    </if>
                </if>
            </otherwise>
        </choose>
        <!-- 如果 change 不为空，则进行排序 -->
        <if test="reqVO.change != null">

            <if test="reqVO.tag == 0">
                ORDER BY
                ecm."sort"
            </if>
            <if test="reqVO.tag == 1">
                ORDER BY
                ecm."class_name_code"
            </if>
            <if test="reqVO.tag == 2">
                ORDER BY
                ecm."class_name"
            </if>
            <if test="reqVO.tag == 3">
                ORDER BY
                ecm."class_open_time"
            </if>
            <if test="reqVO.tag == 4">
                ORDER BY ecm."class_attribute"
            </if>
            <choose>
                <when test="reqVO.change == 0">
                    ASC
                </when>
                <when test="reqVO.change == 1">
                    DESC
                </when>
            </choose>
            <if test="reqVO.tag == 0 and reqVO.change == 1">
                , ecm."create_time" DESC
            </if>
            <if test="reqVO.tag == 0 and reqVO.change == 0">
                , ecm."create_time" ASC
            </if>
        </if>
    </select>

    <select id="getDictLabelById" resultType="java.lang.String">
        SELECT
            "label"
        FROM
            "system_dict_data"
        WHERE
            id = #{id}
        AND
          "deleted" = '0'
    </select>

    <select id="getIdByDictLabel" resultType="java.lang.Integer">
        SELECT
            "id"
        FROM
            "system_dict_data"
        WHERE
            "label" like #{label}
        <if test="type == 1">
            and "dict_type" like 'edu_class_type'
        </if>
        <if test="type == 2">
            and "dict_type" like 'edu_class_attribute'
        </if>
        <if test="type == 3">
            and "dict_type" like 'edu_classroom_campus'
        </if>
        <if test="type == 4">
            and "dict_type" like 'edu_sgy_class_type'
        </if>
          AND
            "deleted" = '0'
        ORDER BY
            "id"
        LIMIT 1
    </select>

    <select id="getValueByDictLabel" resultType="java.lang.Integer">
        SELECT
        "value"
        FROM
        "system_dict_data"
        WHERE
        "label" like #{label}
        <if test="type == 1">
            and "dict_type" like 'edu_class_type'
        </if>
        <if test="type == 2">
            and "dict_type" like 'edu_class_attribute'
        </if>
        <if test="type == 3">
            and "dict_type" like 'edu_classroom_campus'
        </if>
        <if test="type == 4">
            and "dict_type" like 'edu_sgy_class_type'
        </if>
        AND
        "deleted" = '0'
        ORDER BY
        "id"
        LIMIT 1
    </select>


    <select id="getIdByDictLabelcampus" resultType="java.lang.Integer">
        SELECT
        "id"
        FROM
        "system_dict_data"
        WHERE
        "label" like #{label}
        <if test="type == 1">
            and "dict_type" like 'edu_class_type'
        </if>
        <if test="type == 2">
            and "dict_type" like 'edu_class_attribute'
        </if>
        <if test="type == 3">
            and "dict_type" like 'edu_classroom_campus'
            <if test="tenantId != null">
                and "tenant_id" = #{tenantId}
            </if>
        </if>
        AND
        "deleted" = '0'
        ORDER BY
        "id"
        LIMIT 1
    </select>

    <select id="getClassPeopleCount" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            edu_trainee et
        WHERE
            et."class_id" = #{id}
          AND
            et ."deleted" = '0'

    </select>
    <select id="getClassTeacherLeadInfo" resultType="java.lang.String">
        SELECT
            "name"
        FROM
            edu_teacher_information eti
        WHERE
            eti."id" = #{id}
          AND
            eti ."deleted" = '0'
    </select>
    <select id="getDictTypeByDictLabel" resultType="com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoImportDictLableExcelVO">
        SELECT
            sdd."label"
        FROM
            system_dict_data sdd
        WHERE
            sdd ."deleted" = '0'

        <if test="type == 1">
            and "dict_type" like 'edu_class_type'
        </if>
        <if test="type == 2">
            and "dict_type" like 'edu_class_attribute'
        </if>
        <if test="type == 3">
            and "dict_type" like 'edu_classroom_campus'
            <if test="tenantId != null">
                and "tenant_id" = #{tenantId}
            </if>
        </if>
        <if test="type == 4">
            and "dict_type" like 'edu_sgy_class_type'
        </if>

        ORDER BY
            sdd."value"
    </select>
    <select id="selectPageListApplet" resultMap="classManagerDOMap">
        SELECT
        ecm."id",
        ecm."class_name_code",
        ecm."class_name",
        ecm."class_type_dict_id",
        ecm."class_attribute",
        ecm."year",
        ecm."semester",
        ecm."learning_system",
        ecm."training_object",
        ecm."people_number",
        ecm."turn",
        ecm."campus",
        ecm."campus_ids",
        ecm."reporting_time",
        ecm."class_open_time",
        ecm."completion_time",
        ecm."registration_start_time",
        ecm."payment_report",
        ecm."evaluate",
        ecm."accessory",
        ecm."sort",
        ecm."remark",
        ecm."publish",
        ecm."deleted",
        ecm."registration_end_time",
        ecm."class_teacher_lead",
        ecm.administrative_teachers,
        ecm."coach_teacher"
        FROM
        "edu_class_management" ecm
        where
        ecm."deleted" = '0'
        <if test="reqVO.className != null and reqVO.className != ''">
            AND ecm."class_name" LIKE CONCAT('%', #{reqVO.className}, '%')
        </if>
        <if test="reqVO.year != null">
            AND ecm."year" =  #{reqVO.year}
        </if>
        <if test="reqVO.semester != null">
            AND ecm."semester" = #{reqVO.semester}
        </if>
        <if test="reqVO.campus != null">
            AND (
            ecm.campus_ids like CONCAT('%,',CAST(#{reqVO.campus} AS VARCHAR), ',%')
            or
            ecm.campus_ids = CAST(#{reqVO.campus} AS VARCHAR)
            or
            ecm.campus_ids like CONCAT(CAST(#{reqVO.campus} AS VARCHAR), ',%')
            or
            ecm.campus_ids like CONCAT('%,',CAST(#{reqVO.campus} AS VARCHAR))
            )
        </if>
        <if test="reqVO.publish != null">
            AND ecm."publish" = #{reqVO.publish}
        </if>
        <if test="reqVO.endTime != null and reqVO.endTime != '' and reqVO.startTime != null and reqVO.startTime != ''" >
            AND  #{reqVO.endTime} >= ecm."class_open_time"
            AND  ecm."class_open_time" >= #{reqVO.startTime}
        </if>
        <if test="reqVO.classTeacherLead != null">
            AND (
            ecm."class_teacher_lead" = #{reqVO.classTeacherLead}
            OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.classTeacherLead} AS VARCHAR), ',%')
            OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{reqVO.classTeacherLead} AS VARCHAR))
            OR ecm."coach_teacher" LIKE CONCAT(CAST(#{reqVO.classTeacherLead} AS VARCHAR), ',%')
            OR ecm."coach_teacher" = CAST(#{reqVO.classTeacherLead} AS VARCHAR)
            )
        </if>
        <if test="reqVO.classNameCode != null and reqVO.classNameCode != ''">
            AND ecm."class_name_code" = #{reqVO.classNameCode}
        </if>
        <if test="reqVO.isFiltrate != null">
            AND ecm."publish" = #{reqVO.isFiltrate}
        </if>
        <if test="reqVO.classStatus != null">
            --  报名中
            <if test="reqVO.classStatus == 0">
                AND ecm."registration_start_time" &lt; NOW()
                AND ecm."registration_end_time" > NOW()
                AND ecm."publish" = '1'
            </if>
            --   报名结束
            <if test="reqVO.classStatus == 1">
                AND ecm."registration_end_time" &lt; NOW()
                AND ecm."class_open_time" > NOW()
                AND ecm."publish" = '1'
            </if>
            --   开班中
            <if test="reqVO.classStatus == 2">
                AND ecm."class_open_time" &lt; NOW()
                AND ecm."completion_time" > NOW()
                AND ecm."publish" = '1'
            </if>
            --   已结束
            <if test="reqVO.classStatus == 3">
                AND ecm."completion_time" &lt; NOW()
                AND ecm."publish" = '1'
            </if>
            --  已发布 未开始的班级
            <if test="reqVO.classStatus == 4">
                AND NOW() &lt; ecm."class_open_time"
                AND ecm."publish" = '1'
            </if>
        </if>
        <!-- 如果 change 不为空，则进行排序 -->
        <if test="reqVO.change != null">
            <if test="reqVO.tag == 0">
                ORDER BY
                ecm."sort"
            </if>
            <if test="reqVO.tag == 1">
                ORDER BY
                ecm."class_name_code"
            </if>
            <if test="reqVO.tag == 2">
                ORDER BY
                ecm."class_name"
            </if>
            <if test="reqVO.tag == 3">
                ORDER BY
                ecm."class_open_time"
            </if>
            <if test="reqVO.tag == 4">
                ORDER BY ecm."class_attribute"
            </if>
            <choose>
                <when test="reqVO.change == 0">
                    ASC
                </when>
                <when test="reqVO.change == 1">
                    DESC
                </when>
            </choose>
            <if test="reqVO.tag == 0 and reqVO.change == 1">
                , ecm."create_time" DESC
            </if>
            <if test="reqVO.tag == 0 and reqVO.change == 0">
                , ecm."create_time" ASC
            </if>
        </if>
    </select>

    <select id="getTeacherId" resultType="java.lang.Long">
        SELECT
            "id"
        FROM
            edu_teacher_information eti
        WHERE

            eti."user_id" = #{id}
          AND
            eti."deleted" = '0'
    </select>
    <select id="selectListByRegistrationStartTimeToday"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO">
        select
            id,
            class_name_code,
            class_name,
            class_type_dict_id,
            class_attribute,
            completion_time,
            reporting_time,
            registration_start_time,
            registration_end_time,
            class_teacher_lead
        from
            edu_class_management
        WHERE registration_start_time BETWEEN CONCAT(CURRENT_DATE(), ' 00:00:00') AND CONCAT(CURRENT_DATE(), ' 23:59:59')
        AND deleted = 0
    </select>
    <select id="getClassIdListByTeacherId" resultType="java.lang.Long">
        SELECT
        ecm."id"
        FROM
        "edu_class_management" ecm
        where
        ecm."deleted" = '0'
        AND (
        ecm."class_teacher_lead" = #{teacherId}
        OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{teacherId} AS VARCHAR), ',%')
        OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{teacherId} AS VARCHAR))
        OR ecm."coach_teacher" LIKE CONCAT(CAST(#{teacherId} AS VARCHAR), ',%')
        OR ecm."coach_teacher" = CAST(#{teacherId} AS VARCHAR)
        )
    </select>

    <insert id="insertClassClockIn">
        INSERT INTO
            "edu_class_clock_in"
            (
             "class_id",
             "attendance_check",
             "meal_attendance",
             "check_in"
             )
        VALUES
            (
             #{reqVO.classId},
             #{reqVO.attendanceCheck},
             #{reqVO.mealAttendance},
             #{reqVO.checkIn}
             );
    </insert>

    <update id="deleteClassClockById">
        update
            edu_class_clock_in
        set
            "deleted" = '1'
        where
            class_id = #{id}
    </update>

    <update id="updateClassClockingInRule">
        update
            edu_class_management ecm
        set
            ecm."attendance_check" = #{reqVO.attendanceCheck},
            ecm."meal_attendance" = #{reqVO.mealAttendance},
            ecm."check_in" = #{reqVO.checkIn}
        where
            ecm."id" = #{reqVO.classId}
    </update>

    <update id="updateClassClockingInRuleId">
        update
            edu_class_clock_in ecci
        set
            ecci."attendance_check" = #{reqVO.attendanceCheckId},
            ecci."meal_attendance" = #{reqVO.mealAttendanceId},
            ecci."check_in" = #{reqVO.checkInId}
        where
            ecci."class_id" = #{reqVO.classId}
    </update>

    <select id="getClassClockInByAttendanceCheck" resultType="com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassRuleClockingInVO">
        SELECT
            ecci."attendance_check"
        FROM
            edu_class_clock_in ecci LEFT JOIN edu_rule_template ert ON ert."id" =  ecci."attendance_check" AND ert.status = '0' AND ert."deleted" = '0'
        WHERE
            ecci ."class_id" = #{classId}
          AND
            ecci ."deleted" = '0'
    </select>

    <select id="getClassClockInByMealAttendance" resultType="com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassRuleClockingInVO">
        SELECT
            ecci ."meal_attendance"
        FROM
            edu_class_clock_in ecci LEFT JOIN edu_rule_template ert ON ert."id" =  ecci."meal_attendance" AND ert.status = '0' AND ert."deleted" = '0'
        WHERE
            ecci ."class_id" = #{classId}
          AND
            ecci ."deleted" = '0'
    </select>

    <select id="getClassClockInByCheckIn" resultType="com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassRuleClockingInVO">
        SELECT
            ecci."check_in"
        FROM
            edu_class_clock_in ecci LEFT JOIN edu_rule_template ert ON ert."id" =  ecci."check_in" AND ert.status = '0' AND ert."deleted" = '0'
        WHERE
            ecci ."class_id" = #{classId}
          AND
            ecci ."deleted" = '0'
    </select>
    <select id="getClassClockIn"
            resultType="com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassRuleClockingInVO">
        SELECT
            ecci.*
        FROM
            edu_class_clock_in ecci LEFT JOIN edu_rule_template ert ON ert."id" =  ecci."check_in" AND ert.status = '0' AND ert."deleted" = '0'
        WHERE
            ecci ."deleted" = '0'
    </select>

    <select id="selectSimpleClassInfo"
            resultType="com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassSimpleInfo">
        SELECT
            ecm."id",
            ecm."class_name",
            ecm."class_name_code",
            ecm."class_teacher_lead" as classTeacherLead,
            eti."name" as classTeacherLeadName,
            su."id" as teacherUserId
        FROM
            edu_class_management ecm
        LEFT JOIN edu_teacher_information eti ON eti."id" = ecm."class_teacher_lead"
        LEFT JOIN system_users su ON su."id" = eti."user_id"
        WHERE
            ecm."id" = #{id}
    </select>
    <select id="getClassIdListByTeacherId1" resultType="java.lang.Long">
        SELECT
            ecm."id"
        FROM
            "edu_class_management" ecm
        where
            ecm."deleted" = '0'
          AND (
            ecm."class_teacher_lead" = #{teacherId}
                OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{teacherId} AS VARCHAR), ',%')
                OR ecm."coach_teacher" LIKE CONCAT('%,', CAST(#{teacherId} AS VARCHAR))
                OR ecm."coach_teacher" LIKE CONCAT(CAST(#{teacherId} AS VARCHAR), ',%')
                OR ecm."coach_teacher" = CAST(#{teacherId} AS VARCHAR)
            )
            and (ecm.attendance_check = 0
                     or ecm.meal_attendance =0
                     or ecm.check_in = 0)
    </select>

    <select id="selectSimpleClassInfoList"
            resultType="com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassSimpleInfo">
        SELECT
            ecm."id",
            ecm."class_name"
        FROM
            edu_class_management ecm
        <if test="teacherId != null">
            LEFT JOIN edu_teacher_information eti ON eti."id" = ecm."class_teacher_lead"
            LEFT JOIN system_users su ON su."id" = eti."user_id"
        </if>
        WHERE ecm.deleted = '0'
        <if test="teacherId != null">
            AND su.system_id = #{teacherId}
        </if>
        <if test="className != null">
            AND ecm."class_name" LIKE CONCAT('%',#{className},'%')
        </if>
        order by ecm.create_time desc
    </select>
    <select id="getPageForElectiveReleaseCreate"
            resultMap="getPageForElectiveReleaseCreateMap">
        SELECT
        ecc."id" classCourseId,
        ecm."id",
        ecm."class_name_code",
        ecm."class_name",
        ecm."class_type_dict_id",
        ecm."class_attribute",
        ecm."year",
        ecm."semester",
        ecm."learning_system",
        ecm."training_object",
        ecm."people_number",
        ecm."turn",
        ecm."campus",
        ecm."campus_ids",
        ecm."reporting_time",
        ecm."class_open_time",
        ecm."completion_time",
        ecm."registration_start_time",
        ecm."payment_report",
        ecm."evaluate",
        ecm."accessory",
        ecm."sort",
        ecm."remark",
        ecm."publish",
        ecm."deleted",
        ecm."registration_end_time",
        ecm."class_teacher_lead",
        ecm."coach_teacher",
        ecm."attendance_check",
        ecm."meal_attendance",
        ecm."check_in"
        from edu_class_course ecc
        join edu_plan ep on ecc.plan_id = ep.id and ep.deleted = 0
        join edu_class_management ecm on ecm.id = ecc.class_id and ecm.deleted =0
        where ecc.deleted = 0
        and ep.status = '1'
        and ecc.is_temporary = 0
        and ecc.course_id = -1
        and ecc.period = #{reqVO.dayPeriod}
        and ecc."date" = #{reqVO.classDate}
        and TO_CHAR(ecc.begin_time, 'HH24:MI') = #{reqVO.classStartTimeStr}
        and TO_CHAR(ecc.end_time, 'HH24:MI') = #{reqVO.classEndTimeStr}
        <if test="reqVO.className != null and reqVO.className != ''">
            AND ecm."class_name" LIKE CONCAT('%', #{reqVO.className}, '%')
        </if>
        <if test="reqVO.endTime != null and reqVO.endTime != '' and reqVO.startTime != null and reqVO.startTime != ''" >
            AND  #{reqVO.endTime} >= ecm."class_open_time"
            AND  ecm."class_open_time" >= #{reqVO.startTime}
        </if>
        <if test="reqVO.classStatus != null">
            --  报名中
            <if test="reqVO.classStatus == 0">
                AND ecm."registration_start_time" &lt; NOW()
                AND ecm."registration_end_time" > NOW()
                AND ecm."publish" = '1'
                AND ecm."class_open_time" != ecm."registration_start_time"
            </if>
            --   报名结束
            <if test="reqVO.classStatus == 1">
                AND ecm."registration_end_time" &lt; NOW()
                AND ecm."class_open_time" > NOW()
                AND ecm."publish" = '1'
            </if>
            --   开班中
            <if test="reqVO.classStatus == 2">
                AND ecm."class_open_time" &lt; NOW()
                AND ecm."completion_time" > NOW()
                AND ecm."publish" = '1'
            </if>
            --   已结束
            <if test="reqVO.classStatus == 3">
                AND ecm."completion_time" &lt; NOW()
                AND ecm."publish" = '1'
            </if>
            --  已发布 未开始的班级
            <if test="reqVO.classStatus == 4">
                AND NOW() &lt; ecm."class_open_time"
                AND ecm."publish" = '1'
            </if>
        </if>
        ORDER BY ecm."sort"
    </select>
    <select id="getClassList"
            resultMap="classManagerDOMap">
        select
            ecm.*
        from
            edu_class_management ecm
        where
            ecm.deleted = 0
          and ecm.publish = 1
          and ecm.class_open_time > current_timestamp
    </select>
    <select id="getComplateClassList"
            resultMap="classManagerDOMap">
        select
        ecm.*
        from
        edu_class_management ecm
        where
        ecm.deleted = 0
        and ecm.publish = 1
        and ecm.completion_time &lt;= current_timestamp
        and year(completion_time) = year(current_date)

    </select>
    <select id="selectTraineeReportPage"
            resultMap="TraineeReportPageResultMap">
        <include refid="traineeReportPageList"/>
    </select>
    <select id="selectTraineeReportList"
            resultMap="selectTraineeReportListMap">
        <include refid="traineeReportPageList"/>
    </select>
    <update id="updateCompletionTemplate">
        update
            edu_class_management
        set
            id_code = #{reqVO.idCode}
        where
            id = #{reqVO.classId}
    </update>

    <select id="selectClassList"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO">
        SELECT *
        FROM edu_class_management ecm
        WHERE ecm.deleted = 0
          AND  ecm.completion_time > current_timestamp
    </select>
    <select id="simpleListForBusinessCenter"
            resultType="com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.businesscenter.ClassManagementSimpleForBusinessCenterRespVO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.businesscenter.ClassManagementSimpleForBusinessCenterReqVO">
        select
            id classId,
            class_name className
        from edu_class_management
        where deleted = 0
            and tenant_id = #{reqVO.tenantId}
            AND "class_open_time" &lt; NOW()
            AND "publish" = '1'
            <if test="reqVO.classTerm != null">
                and "semester" = #{reqVO.classTerm}
            </if>
            <if test="reqVO.endTime != null and reqVO.startTime != null">
                AND (
                "class_open_time" between #{reqVO.startTime} and #{reqVO.endTime}
                or "completion_time" between #{reqVO.startTime} and #{reqVO.endTime}
                or ("class_open_time" &lt;= #{reqVO.startTime} and "completion_time" &gt;= #{reqVO.endTime})
                )
            </if>
            order by "class_open_time" desc
    </select>
    <select id="getClassIdList" resultType="java.lang.Long">
        select distinct(class_id) from edu_class_course where deleted = 0
    </select>
    <select id="getOpenClass"
            resultType="com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.PublishScaleRespVO">
        select id classId, class_name className, 1 classStatus,class_teacher_lead ,class_name_code as classNameCode
        ,class_attribute as classAttribute,class_open_time as classOpenTime,completion_time as
        completionTime from edu_class_management

        <where>
            class_open_time &lt;= #{now} and completion_time &gt;= #{now} and deleted = 0
            <if test="reqVO.className != null and reqVO.className != ''">
                and class_name like CONCAT('%', #{reqVO.className}, '%')
            </if>
        </where>
    </select>
    <select id="getClosedClass"
            resultType="com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.PublishScaleRespVO">
        select id classId, class_name className, 2 classStatus,class_teacher_lead ,class_name_code as classNameCode
        ,class_attribute as classAttribute,class_open_time as classOpenTime,completion_time as
        completionTime from edu_class_management
        <where>
            completion_time &lt;= #{now} and completion_time &gt;= #{oneWeek} and deleted = 0
            <if test="reqVO.className != null and reqVO.className != ''">
                and class_name like CONCAT('%', #{reqVO.className}, '%')
            </if>
        </where>
    </select>
    <select id="getClassByCampus" resultType="java.lang.Long" parameterType="java.lang.Integer">
        select id
        from edu_class_management
        where deleted = 0
          and id_code is null
          AND (
            campus_ids like CONCAT('%,',CAST(#{campus} AS VARCHAR), ',%')
                or
            campus_ids = CAST(#{campus} AS VARCHAR)
                or
            campus_ids like CONCAT(CAST(#{campus} AS VARCHAR), ',%')
                or
            campus_ids like CONCAT('%,',CAST(#{campus} AS VARCHAR))
            )
    </select>

</mapper>
