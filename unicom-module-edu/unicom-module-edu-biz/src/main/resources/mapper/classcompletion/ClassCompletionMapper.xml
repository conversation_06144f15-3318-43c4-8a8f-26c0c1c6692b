<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.classcompletion.ClassCompletionMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="getInfoByClassId"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.classcompletion.ClassCompletionDO">
        select
        ecc2.id,
        et.id traineeId,
        et.name as traineeName,
        COALESCE(etg.group_name, '未分组') AS group_name,
        et."position" unitPosition,
        ecc.class_committee_name classPosition,
        et.class_id,
        ecct.assessment_name,
        ecct.serial_number,
        ecct.column_name,
        ecct.module_name,
        CASE
        WHEN ecct.acquisition_mode != 1 THEN COALESCE(ecc2.score, ecct.initial_score, 0)
        ELSE ecc2.score
        END AS score,
        ecct.save,
        ecct.acquisition_mode,
        ecct.data_source
        from
        edu_trainee et
        left join edu_trainee_group etg on
        et.group_id = etg.id
        left join edu_class_committee ecc on
        et.class_committee_id = ecc.id
        left join edu_class_completion_template ecct on
        et.class_id = ecct.class_id
        and ecct.save = 1
        and ecct.deleted = 0
        left join edu_class_completion ecc2 on
        et.id = ecc2.trainee_id
        and ecc2.serial_number = ecct.serial_number and ecc2.deleted =0
        where
        et.deleted = 0
        <if test="classId != null and classId != ''">
            and et.class_id = #{classId}
        </if>
        order by
        coalesce(etg.sort,
        0) asc,
        et.group_sort,
        (regexp_matches(ecct.serial_number, E'^([A-Za-z]*)([0-9]+)$'))[1] ASC,
        (regexp_matches(ecct.serial_number, E'^([A-Za-z]*)([0-9]+)$'))[2]::numeric ASC,
        et.create_time DESC
    </select>
    <select id="getDefaultRuleInfoByClassId"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.classcompletion.ClassCompletionDO">
        select
            ecc2.id,
            et.id traineeId,
            et.name as traineeName,
            coalesce(etg.group_name,
                     '未分组') as group_name,
            et."position" unitPosition,
            ecc.class_committee_name classPosition,
            et.class_id,
            ect.assessment_name,
            ect.serial_number,
            ect.column_name,
            ect.module_name,
            case
                when ect.acquisition_mode != 1 then coalesce(ecc2.score,
                                                             ect.initial_score,
                                                             0)
                else ecc2.score
                end as score,
            ect.acquisition_mode,
            ect.data_source
        from
            edu_trainee et
                left join edu_trainee_group etg on
                et.group_id = etg.id
                left join edu_class_committee ecc on
                et.class_committee_id = ecc.id
                left join edu_class_management ecm on et.class_id =ecm.id
                left join edu_completion_template ect on
                ect.campus = ect.campus and ect.deleted = 0
                left join edu_class_completion ecc2 on
                et.id = ecc2.trainee_id
                    and ecc2.serial_number = ect.serial_number
                    and ecc2.deleted = 0
        where
            et.deleted = 0
          and ect.default_rule = 0
        <if test="classDO.id != null and classDO.id != ''">
            and et.class_id = #{classDO.id}
        </if>
        <if test="classDO.campus != null and classDO.campus != ''">
            and ect.campus = #{classDO.campus}
        </if>
        order by
        coalesce(etg.sort,
        0) asc,
        et.group_sort,
        (regexp_matches(ect.serial_number, E'^([A-Za-z]*)([0-9]+)$'))[1] ASC,
        (regexp_matches(ect.serial_number, E'^([A-Za-z]*)([0-9]+)$'))[2]::numeric ASC,
        et.create_time DESC
    </select>

    <select id="getTemplateByIdCode"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.classcompletion.ClassCompletionDO">
        select
            id,
            serial_number,
            column_name,
            conversion_announcement,
            max_score,
            initial_score,
            acquisition_mode,
            data_source,
            assessment_name,
            campus,
            default_rule,
            template_name,
            create_time,
            update_time,
            deleted,
            tenant_id,
            creator,
            updater,
            module_name,
            id_code,
            class_id,
            save
        from
            edu_class_completion_template
        where
            deleted = 0
          and save = 1
        and id_code = #{idCode}
    </select>

    <select id="getScoreBySerials" resultType="java.util.Map">
        select COALESCE(ect.assessment_name, ect.column_name) AS assessment_name,
        a.score
        from (
        select distinct serial_number,
        score
        from edu_class_completion ecc
        where deleted = 0
        <if test="classId != null and classId != ''">
            and class_id = #{classId}
        </if>
        ) a
        left join edu_completion_template ect
        on a.serial_number = ect.serial_number
        and ect.deleted = 0
        and ect.id_code = #{idCode}
    </select>

    <select id="getScoreList"
            resultType="com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo.TraineeScoreVO">
        select a.trainee_id,COALESCE(ect.assessment_name, ect.column_name) AS assessment_name,
               a.score
        from (
                 select  trainee_id,serial_number,
                         score
                 from edu_class_completion ecc
                 where deleted = 0
                   and class_id = #{classId}
             ) a
                 left join edu_completion_template ect
            on a.serial_number = ect.serial_number
            and ect.deleted = 0
            and ect.id_code = #{idCode}
    </select>
</mapper>
