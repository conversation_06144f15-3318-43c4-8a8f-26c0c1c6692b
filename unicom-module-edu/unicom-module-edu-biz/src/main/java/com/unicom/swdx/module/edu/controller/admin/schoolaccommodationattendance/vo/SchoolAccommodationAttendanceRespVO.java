package com.unicom.swdx.module.edu.controller.admin.schoolaccommodationattendance.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;

@ApiModel("管理后台 - 全校就餐住宿考勤 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SchoolAccommodationAttendanceRespVO extends SchoolAccommodationAttendanceBaseVO {

    @ApiModelProperty(value = "主键id，自增", required = true)
    private Integer id;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;

}
