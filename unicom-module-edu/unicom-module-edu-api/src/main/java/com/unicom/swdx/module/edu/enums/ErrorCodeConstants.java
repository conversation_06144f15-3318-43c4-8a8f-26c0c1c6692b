package com.unicom.swdx.module.edu.enums;

import com.unicom.swdx.framework.common.exception.ErrorCode;

/**
 * System 字典类型的枚举类
 *
 * <AUTHOR>
 */
public interface ErrorCodeConstants {

    ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(1011000000, "登录失败，账号密码不正确");

    ErrorCode ACCESS_DENIED = new ErrorCode(1011999001, "没有访问权限");

    // ========== 教室库  1011000001 ==========
    ErrorCode CLASSROOM_LIBRARY_NOT_EXISTS = new ErrorCode(1011000001, "EduClassroomLibrary不存在");

    ErrorCode CLASSROOM_LIBRARY_CLASS_NAME_EXISTS = new ErrorCode(1011000002, "教室名称已存在！");

    ErrorCode CLASSROOM_LIBRARY_CLASS_COURSE_EXISTS = new ErrorCode(1011000003, "该教室已有授课安排，需先取消排课才可删除");

    ErrorCode CLASSROOM_LIBRARY_CLASS_COURSE_SECTION_EXISTS = new ErrorCode(1011000004, "选中的教室中存在授课安排，需先取消排课才可删除");

    // ========== 教学点管理 1011000005 ==========
    ErrorCode TEACHING_POINT_NOT_EXISTS = new ErrorCode(1011000005, "教学点不存在");
    ErrorCode TEACHING_POINT_CONTACT_PHONE_BOUND = new ErrorCode(1011000006, "手机号已被其他教学点绑定，每个联络员只能被一个教学点绑定");
    ErrorCode TEACHING_POINT_CLASSROOM_DELETE_FORBIDDEN = new ErrorCode(1011000007, "教学点教室不允许直接删除，请先删除对应的教学点");
    ErrorCode TEACHING_POINT_CLASSROOM_UPDATE_FORBIDDEN = new ErrorCode(1011000008, "教学点教室不允许直接修改，请通过教学点管理进行修改");
    ErrorCode TEACHING_POINT_CLASSROOM_CREATE_FORBIDDEN = new ErrorCode(1011000009, "教学点教室不允许直接创建，请通过教学点管理进行创建");

    // ========== 课程库 1-011-001-000 ==========
    ErrorCode COURSES_NOT_EXISTS = new ErrorCode(1011001001, "对象不存在");
    ErrorCode COURSES_TOPIC_STATUS_REQUIRED = new ErrorCode(1011001002, "专题状态不能为空");
    ErrorCode COURSES_OPTIONAL_STATUS_REQUIRED = new ErrorCode(1011001002, "课程状态不能为空");
    ErrorCode COURSES_EDUCATE_FORM_ID_REQUIRED = new ErrorCode(1011001003, "教学形式不能为空");
    ErrorCode COURSES_TOPIC_THEME_ID_REQUIRED = new ErrorCode(1011001004, "专题分类不能为空");
    ErrorCode COURSES_OPTIONAL_THEME_ID_REQUIRED = new ErrorCode(1011001004, "课程分类不能为空");
    ErrorCode COURSES_NAME_DUPLICATE = new ErrorCode(1011001005, "名称重复");
    ErrorCode COURSES_TEACHER_NOT_EXISTS = new ErrorCode(1011001006, "授课教师不存在");
    ErrorCode COURSES_IMPORT_ERROR = new ErrorCode(1011001007, "课程导入异常");
    ErrorCode COURSES_IMPORT_TOPIC_NAME_REQUIRED = new ErrorCode(1011001008, " 专题名称不能为空");
    ErrorCode COURSES_IMPORT_OPTIONAL_NAME_REQUIRED = new ErrorCode(1011001008, " 课程名称不能为空");
    // 课程分类不能为空
    ErrorCode COURSES_IMPORT_TOPIC_THEME_REQUIRED = new ErrorCode(1011001009, " 专题分类不能为空");
    ErrorCode COURSES_IMPORT_OPTIONAL_THEME_REQUIRED = new ErrorCode(1011001009, " 课程分类不能为空");
    ErrorCode COURSES_IMPORT_EDUCATE_FORM_REQUIRED = new ErrorCode(1011001010, " 教学形式不能为空");
    // 管理部门不存在
    ErrorCode COURSES_IMPORT_MANAGEMENT_DEPT_NOT_EXISTS = new ErrorCode(1011001011, " 管理部门不存在");
    // 课程分类不存在
    ErrorCode COURSES_IMPORT_THEME_NOT_EXISTS = new ErrorCode(1011001012, " 课程分类不存在");
    // 教学形式不存在
    ErrorCode COURSES_IMPORT_EDUCATE_FORM_NOT_EXISTS = new ErrorCode(1011001013, " 教学形式不存在");
    // 导入数据为空
    ErrorCode COURSE_IMPORT_EMPTY = new ErrorCode(1011001014, " 导入数据为空");
    ErrorCode COURSE_TENANT_NOT_EXISTS = new ErrorCode(1011001015, "租户不存在");
    // 名称长度
    ErrorCode COURSES_NAME_REQUIRED = new ErrorCode(1011001015, "{}名称长度限制1-50");

    ErrorCode COURSES_IMPORT_NAME_LENGTH_LIMIT = new ErrorCode(1011001016, "名称长度大于50");
    ErrorCode COURSE_HAS_BEEN_RELEASED_DELETE_FAILURE = new ErrorCode(1011001017, "课程已排课，删除失败");
    ErrorCode COURSES_IMPORT_ACTIVITY_NAME_REQUIRED = new ErrorCode(1011001018, " 教学活动名称不能为空");
    ErrorCode COURSES_IMPORT_ACTIVITY_TYPE_NOT_EXISTS = new ErrorCode(1011001019, " 活动类型不存在");
    ErrorCode COURSES_IMPORT_DATE_FORMAT_ERROR = new ErrorCode(1011001020, " 开发时间格式有误(例：2025-03-12)");
    ErrorCode COURSES_IMPORT_TEACHER_NOT_EXISTS = new ErrorCode(1011001021, "教师不存在");
    ErrorCode COURSES_IMPORT_TEACHER_DUPLICATE = new ErrorCode(1011001022, "为重名教师，请手动新增课程绑定");


    // ========== 师资-任课信息 1-011-002-000==========
    ErrorCode TEACHER_COURSE_INFORMATION_NOT_EXISTS = new ErrorCode(1011002001, "师资-任课信息不存在");

    // ========== 师资信息 1-011-003-000==========
    ErrorCode TEACHER_NOT_EXISTS = new ErrorCode(1011003001, "师资信息不存在");
    ErrorCode COURSE_ARRANGED = new ErrorCode(1011003002, "不能删除有授课安排的教师-课程信息");

    ErrorCode TEACHER_IMPORT_DEPT_NOT_EXISTS = new ErrorCode(1011003003, " 所属部门不存在");
    ErrorCode TEACHER_IMPORT_DEGREE_NOT_EXISTS = new ErrorCode(1011003004, " 学位不存在");
    ErrorCode TEACHER_IMPORT_GENDER_NOT_EXISTS = new ErrorCode(1011003005, " 性别不存在");
    ErrorCode TEACHER_IMPORT_NATION_NOT_EXISTS = new ErrorCode(1011003006, " 民族不存在");
    ErrorCode TEACHER_IMPORT_POLITICAL_STATUS_NOT_EXISTS = new ErrorCode(1011003007, " 政治面貌不存在");
    ErrorCode TEACHER_IMPORT_EDUCATION_NOT_EXISTS = new ErrorCode(1011003008, " 学历不存在");
    ErrorCode TEACHER_IMPORT_RANK_NOT_EXISTS = new ErrorCode(1011003009, " 职级不存在");
    ErrorCode TEACHER_IMPORT_ADMINISTRATIVE_LEVEL_NOT_EXISTS = new ErrorCode(1011003010, " 行政级别");
    ErrorCode TEACHER_IMPORT_NAME_REQUIRED = new ErrorCode(1011003011, " 姓名为必填项");
    ErrorCode TEACHER_IMPORT_NAME_LENGTH_LIMIT = new ErrorCode(1011003012, " 姓名字符超过最大长度30");
    ErrorCode TEACHER_IMPORT_ID_NUMBER_FORMAT_ERROR = new ErrorCode(1011003013, " 身份证格式错误");
    ErrorCode TEACHER_IMPORT_BIRTHDAY_FORMAT_ERROR = new ErrorCode(1011003014, " 出生日期格式错误");
    ErrorCode TEACHER_IMPORT_NATIVE_PLACE_LENGTH_LIMIT = new ErrorCode(1011003015, " 籍贯字符超过最大长度50");
    ErrorCode TEACHER_IMPORT_ARRIVAL_TIME_FORMAT_ERROR = new ErrorCode(1011003016, " 来校年月格式错误");
    ErrorCode TEACHER_IMPORT_CONTACT_INFORMATION_FORMAT_ERROR = new ErrorCode(1011003017, " 联系方式格式错误");
    ErrorCode TEACHER_IMPORT_MAIL_ADDRESS_LENGTH_LIMIT = new ErrorCode(1011003018, " 通信地址字符超过最大长度50");
    ErrorCode TEACHER_IMPORT_POSTAL_CODE_FORMAT_ERROR = new ErrorCode(1011003019, " 邮政编码格式错误");
    ErrorCode TEACHER_IMPORT_EMAIL_FORMAT_ERROR = new ErrorCode(**********, " 电子邮箱格式错误");
    ErrorCode TEACHER_IMPORT_WORK_UNIT_LENGTH_LIMIT = new ErrorCode(**********, " 所在单位字符超过最大长度50");
    ErrorCode TEACHER_IMPORT_PROFESSIONAL_TITLE_LENGTH_LIMIT = new ErrorCode(**********, " 职务字符超过最大长度50");
    ErrorCode TEACHER_IMPORT_REMARK_LENGTH_LIMIT = new ErrorCode(**********, " 备注字符超过最大长度100");
    ErrorCode TEACHER_IMPORT_DEPOSITBANK_LENGTH_LIMIT = new ErrorCode(**********, " 开户行字符超过最大长度50");
    ErrorCode TEACHER_IMPORT_BANKCARDNUMBER_LENGTH_LIMIT = new ErrorCode(**********, " 银行卡号字符超过最大长度30");


    // ========== 班主任待办事项 1-011-004-000  ==========
    ErrorCode TEACHER_TODO_ITEMS_NOT_EXISTS = new ErrorCode(**********, "班主任待办事项不存在");
    ErrorCode TEACHER_TODO_ITEMS_CLASS_INFO_NOT_EXISTS = new ErrorCode(**********, "班级不存在");

    // ========== 选修课发布信息 1-011-005-000 ==========
    ErrorCode ELECTIVE_RELEASE_NOT_EXISTS = new ErrorCode(**********, "选修课发布信息不存在");
    ErrorCode ELECTIVE_RELEASE_CLASSROOM_DUPLICATE = new ErrorCode(**********, "该次发布课程中存在教室占用冲突");
    ErrorCode ELECTIVE_RELEASE_TEACHER_DUPLICATE = new ErrorCode(**********, "该次发布课程中存在相同教师同时授课");
    ErrorCode ELECTIVE_RELEASE_COURSES_IS_EMPTY = new ErrorCode(**********, "请选择发布课程");
    ErrorCode ELECTIVE_RELEASE_CLASS_IS_EMPTY = new ErrorCode(1011005005, "请选择班级范围");
    ErrorCode ELECTIVE_RELEASE_SELECTION_END_TIME_ERROR = new ErrorCode(1011005006, "开始上课时间不能早于选学结束时间");
    ErrorCode ELECTIVE_RELEASE_TEACHER_CLASS_TIME_CONFLICT = new ErrorCode(1011005006, "存在教师授课时间冲突");
    ErrorCode ELECTIVE_RELEASE_CLASSROOM_CLASS_TIME_CONFLICT = new ErrorCode(1011005007, "存在课程占用教室时间冲突");
    ErrorCode ELECTIVE_RELEASE_CLASS_MASTER_USER_NOT_EXISTS = new ErrorCode(1011005008, "获取登录用户信息失败");
    ErrorCode ELECTIVE_RELEASE_NAME_LENGTH_ERROR = new ErrorCode(1011005009, "发布名称为1-50字符长度");
    ErrorCode ELECTIVE_RELEASE_DELETE_TIME_ERROR = new ErrorCode(1011005009, "已到发布上课时间，不可删除");

    // ========== 选修课发布课程关联 1-011-006-000 ==========
    ErrorCode ELECTIVE_RELEASE_COURSES_NOT_EXISTS = new ErrorCode(1011006001, "发布课程不存在");

    // ========== 选修课学员选课关联 1-011-007-000 ==========
    ErrorCode ELECTIVE_TRAINEE_SELECTION_NOT_EXISTS = new ErrorCode(1011007001, "选课不存在");
    ErrorCode ELECTIVE_TRAINEE_RELEASE_COURSES_STATUS = new ErrorCode(101100702, "查询学员选修课发布信息类型失败");
    ErrorCode ELECTIVE_TRAINEE_USER_NOT_EXISTS = new ErrorCode(101100703, "该用户学员不存在");
    ErrorCode ELECTIVE_TRAINEE_SELECTED_ELECTIVE_COURSE_TIME_CONFLICT = new ErrorCode(101100704, "与该学员其他已选选修课存在时间冲突！");
    ErrorCode ELECTIVE_TRAINEE_SELECTED_CLASS_COURSE_TIME_CONFLICT = new ErrorCode(101100705, "与该学员班级其他课程安排存在时间冲突！");
    ErrorCode ELECTIVE_TRAINEE_SELECTED_COURSE_LIST_IS_EMPTY = new ErrorCode(101100706, "批量选课不能为空！");
    ErrorCode ELECTIVE_TRAINEE_SELECTED_COURSE_TIME_SELF_CONFLICT = new ErrorCode(101100706, "批量选课之间存在上课时间冲突！");

    // ========== 点名签到、大课考勤 1-011-008-000 ==========
    ErrorCode ROLLCALL_SIGN_IN_NOT_EXISTS = new ErrorCode(1011008001, "ID不存在");
    ErrorCode ROLLCALL_SIGN_IN_TITLE_IS_BLANK = new ErrorCode(1011008002, "签到标题不能为空");
    ErrorCode ROLLCALL_SIGN_IN_CLASS_COURSE_ID_IS_NULL = new ErrorCode(1011008003, "课表不能为空");
    ErrorCode ROLLCALL_SIGN_IN_CHECK_START_TIME_IS_AFTER_END_TIME = new ErrorCode(1011008004, "打卡开始时间不能晚于打卡结束时间");
    ErrorCode ROLLCALL_SIGN_IN_CLASS_NOT_EXISTS = new ErrorCode(1011008005, "班次不存在");
    ErrorCode ROLLCALL_SIGN_IN_CLASS_HAS_CURRENT_SIGN_IN = new ErrorCode(1011008006, "班次存在未结束的点名签到");
    ErrorCode ROLLCALL_LECTURE_ATTENDANCE_CLASS_COURSE_NOT_EXISTS = new ErrorCode(1011008007, "课表不存在");
    ErrorCode ROLLCALL_SIGN_IN_CLASS_HAS_CURRENT_LECTURE_ATTENDANCE = new ErrorCode(1011008008, "该课程已有大课考勤，请考勤结束后再次发起");
    ErrorCode ROLLCALL_SIGN_IN_ENDED = new ErrorCode(1011008009, "考勤已结束");
    ErrorCode ROLLCALL_SIGN_IN_CLASS_IS_OVERLAP = new ErrorCode(1011008010, "考勤打卡时间冲突:\n{}\n有正在发起的大课考勤，请调整本次大课考勤打卡时间");
    ErrorCode ROLLCALL_SIGN_IN_CLASS_HAS_CURRENT_TRAINEE_REPORT = new ErrorCode(1011008011, "班次存在未结束的报道发起");

    // ========== 学员点名签到记录 1-011-009-000 ==========
    ErrorCode ROLLCALL_RECORD_NOT_EXISTS = new ErrorCode(1011009001, "学员点名签到记录不存在");


    // ========== 打卡地点历史记录1-011-010-000 ==========
    ErrorCode ROLLCALL_RECORD_HISTORY_TEACHER_ID_IS_EMPTY = new ErrorCode(1011010001, "老师ID不能为空");
    ErrorCode ROLLCALL_RECORD_HISTORY_TYPE_IS_EMPTY = new ErrorCode(1011010002, "历史地点类型不能为空");
    ErrorCode ROLLCALL_RECORD_HISTORY_ADDRESS_IS_EMPTY = new ErrorCode(1011010002, "地址不能为空");


    // ========== 班级管理 1012000001 ==========
    ErrorCode CLASS_MANAGEMENT_NOT_EXISTS = new ErrorCode(1012000001, "班次不存在");

    ErrorCode CLASS_MANAGEMENT_TIME_ERROR = new ErrorCode(1012000002, "班级发布时间不能晚于班级报名时间，存在班级的发布时间晚于班级的报名时间!");

    ErrorCode CLASS_MANAGEMENT_IMPORT_LIST_IS_EMPTY = new ErrorCode(1012000003, "导入数据不能为空");

    ErrorCode CLASS_MANAGEMENT_NAME_TEMPLATE_ERROR = new ErrorCode(1012000004, "模版名称不对");

    ErrorCode CLASS_MANAGEMENT_DICTIONARY_ERROR = new ErrorCode(1012000005, "相关字典不存在，请先配置字典");

    ErrorCode CLASS_MANAGEMENT_FILE_NAME_ERROR = new ErrorCode(1012000006, "上传的文件名不正确");

    ErrorCode CLASS_MANAGEMENT_HEAD_NAME_ERROR = new ErrorCode(1012000007, "表头信息不对，请下载模版上传");

    ErrorCode CLASS_MANAGEMENT_TEACHER_ID_NOT_EXIST = new ErrorCode(1012000008, "该用户在师资库中不存在！");

    ErrorCode NO_PERMISSION_ERROR = new ErrorCode(1012000009, "无权限访问");

    ErrorCode CLASS_MANAGEMENT_CREATE_ERROR = new ErrorCode(1012000010, "系统繁忙，请稍后再试！");

    ErrorCode CLASS_MANAGEMENT_SIGN_UP_UNIT = new ErrorCode(1012000011, "班级删除成功，班级对应的单位删除失败，存在脏数据！");

    ErrorCode CLASS_MANAGEMENT_REGISTRATION_END_TIME_EXCEED = new ErrorCode(1012000012, "班级报名已过截止时间");
    ErrorCode CLASS_COMPLETED = new ErrorCode(1012000013, "当前班级已结业");
    ErrorCode NO_PRIORITY_CAMPUS = new ErrorCode(1012000014, "班级设置优先级校区失败");
    ErrorCode CLASS_MANAGEMENT_IMPORT_CAMPUS_NOT_EXISTS_ERROR = new ErrorCode(1012000015, "校区不存在");

    // ========== 名额分配 1013000001 ==========
    ErrorCode SIGN_UP_UNIT_NOT_EXISTS = new ErrorCode(1013000001, "EduSignUpUnit不存在");

    ErrorCode SIGN_UP_UNIT_UNItNAME_EXISTS = new ErrorCode(1013000002, "单位名称已存在！");
    ErrorCode SIGN_UP_UNIT_USERNAME_IS_BLANK = new ErrorCode(1013000003, "单位用户名不能为空");
    ErrorCode SIGN_UP_UNIT_PHONE_IS_BLANK = new ErrorCode(1013000003, "负责人电话不能为空");
    ErrorCode SIGN_UP_UNIT_PHONE_FORMAT_ERROR = new ErrorCode(1013000003, "负责人电话格式错误");
    ErrorCode SIGN_UP_UNIT_IS_EXISTS = new ErrorCode(1013000004, "单位用户名已存在");
    ErrorCode SIGN_UP_UNIT_USRENAME_FORMAT_ERROR = new ErrorCode(1013000004, "单位用户名仅支持字母、数字，长度限制20个字符");

    ErrorCode SIGN_UP_UNIT_LEADER_NOT_EXISTS = new ErrorCode(1013000005, "非单位管理员，无查看参训统计权限");

    ErrorCode SIGN_UP_UNIT_PHONE_DUPLICATE = new ErrorCode(1013000006, "单位手机号码重复!");
    ErrorCode SIGN_UP_UNIT_USERNAME_DUPLICATE = new ErrorCode(1013000006, "单位用户名重复!");

    // ========== 班级住宿详情 1014000001 ==========
    ErrorCode CLASS_ACCOMMODATION_DETAILS_NOT_EXISTS = new ErrorCode(1014000001, "班级住宿详情不存在");
    ErrorCode CLASS_ACCOMMODATION_DETAILS_UPDATE_DATE_OUT_RANGE = new ErrorCode(1014000001, "修改日期范围需在班级报道时间至结业时间内");
    // ========== 校区容量 1015000001 ==========
    ErrorCode CAMPUS_CAPACITY_NOT_EXISTS = new ErrorCode(1015000001, "校区容量不存在");


    // ========== Trainee  1011000010 ==========
    ErrorCode TRAINEE_NOT_UNIQUE = new ErrorCode(1011000010, "你已报名{}班次，请移步该班报到");
    ErrorCode TRAINEE_NOT_EXISTS = new ErrorCode(1011000011, "学员信息不存在");
    ErrorCode TRAINEE_CLASS_NOT_SIGNING = new ErrorCode(1011000012, "不在报名时间");
    ErrorCode TRAINEE_CLASS_NO_QUOTA = new ErrorCode(1011000013, "班级名额不足");
    ErrorCode TRAINEE_UNIT_NO_QUOTA = new ErrorCode(1011000014, "该单位名额不足");
    ErrorCode CADRE_INFORMATION_EXISTS = new ErrorCode(1011000015, "干部信息已存在");
    ErrorCode CADRE_INFORMATION_NOT_EXISTS = new ErrorCode(1011000016, "干部信息不存在，请刷新后再试");
    ErrorCode CADRE_ALREADY_REGISTERED = new ErrorCode(1011000017, "所选干部已经报名，请刷新后再试");
    ErrorCode CADRE_INFO_SYNC_FAIL = new ErrorCode(1011000018, "干部信息同步失败");
    ErrorCode TRAINEE_LOCK_ACQUIRE_FAILED = new ErrorCode(1011000023, "网络错误，请刷新后重试");

    ErrorCode TRAINEE_GROUP_NAME_EXISTS = new ErrorCode(1011000024, "小组名字重复");
    ErrorCode TRAINEE_GROUP_NOT_EXISTS = new ErrorCode(1011000025, "小组不存在");
    ErrorCode CLASS_COMMITTEE_NOT_EXISTS = new ErrorCode(1011000026, "班委不存在，请刷新再试");

    ErrorCode TRAINEE_MEMBER_NOT_EMPTY = new ErrorCode(1011000027, "学员不为空");
    ErrorCode TRAINEE_PHONE_NOT_UNIQUE = new ErrorCode(1011000028, "学员手机号不能重复");
    ErrorCode CADRE_PHONE_NOT_UNIQUE = new ErrorCode(1011000029, "{} 存在手机号重复，请更正后再报名");
    // ========== EduNoticeAnnouncement  1010101010号 ==========
    ErrorCode NOTICE_ANNOUNCEMENT_NOT_EXISTS = new ErrorCode(1010101010, "EduNoticeAnnouncement不存在");

    ErrorCode PLAN_NOT_EXISTS = new ErrorCode(1011000019, "教学计划不存在");

    ErrorCode CLASS_COURSE_NOT_EXISTS = new ErrorCode(1011000020, "班级课程安排不存在");

    ErrorCode CLASS_COURSE_CONFLICT = new ErrorCode(1011000029, "您选择的目标班级 {} 在当前课程的授课时间段内存在已安排的课程，请检查并修改 {} 的课程安排后，再进行绑定。");

    ErrorCode ELECTIVE_RELEASE_TIME_ERROR = new ErrorCode(1011000028, "部分排课没有设置上课时间，请检查");

    ErrorCode CLASS_COURSE_CONFLICT_BY_TIME = new ErrorCode(1011000028, "存在课程时间冲突：{}");

    ErrorCode PLAN_DETAIL_NOT_EXISTS = new ErrorCode(1011000021, "教学计划详情不存在");

    ErrorCode CLASS_COURSE_TIME_CONFLICT = new ErrorCode(1011000022, "课表时间段存在冲突，请调整");

    ErrorCode CLASS_COURSE_CHANGE_TIME_PARAM_ERROR = new ErrorCode(1012000022, "调课时间参数不全");

    ErrorCode CLASS_COURSE_CHANGE_PARAM_EMPTY = new ErrorCode(1012000023, "调课参数不能都为空");

    ErrorCode CLASS_COURSE_CHANGE_NOT_EXISTS = new ErrorCode(1012000024, "对调的课程不能为空");

    ErrorCode OPTIONAL_COURSE_NOT_SUPPORT_CHANGE_TEACHER_CLASSROOM = new ErrorCode(1012000025, "选修课不支持修改老师和教室");

    ErrorCode TEACHING_ACTIVITY_COURSE_NOT_SUPPORT_CHANGE_TEACHER = new ErrorCode(1012000026, "活动课不支持修改授课老师");

    ErrorCode PLAN_NOT_EXIST = new ErrorCode(1012000027, "该班级暂无日程安排，请先创建并发布教学计划");

    ErrorCode CLASS_COURSE_MERGED_CHANGE_TIME_CONFLICT = new ErrorCode(1012000027, "由于关联班级[{}]存在已安排的课程或不存在教学计划安排，本次调课及同步失败。请检查并修改这些班级的课程安排后，再尝试调课。");
    // ========== 考勤签到 ==========
    ErrorCode CLOCK_IN_INFO_NOT_EXISTS = new ErrorCode(1112000001, "考勤签到不存在");

    ErrorCode ATTENDANCE_UPDATE_STATUS_NOT_EXISTS = new ErrorCode(1112000002, "更新打卡类型不存在");


    // ========== 考勤规则模版 1022000001 ==========
    ErrorCode RULE_TEMPLATE_NOT_EXISTS = new ErrorCode(1022000001, "考勤规则不存在");

    ErrorCode RULE_TEMPLATE_DEFAULT_ATTENDANCE_CHECK_EXISTS = new ErrorCode(1022000002, "该校区到课默认规则已存在");

    ErrorCode RULE_TEMPLATE_DEFAULT_MEAL_ATTENDANCE_EXISTS = new ErrorCode(1022000003, "该校区就餐默认规则已存在");

    ErrorCode RULE_TEMPLATE_DEFAULT_CHECK_IN_EXISTS = new ErrorCode(1022000004, "该校区住宿默认规则已存在");

    ErrorCode RULE_TEMPLATE_BIND_EXISTS = new ErrorCode(1022000005, "该考勤规则有班级在使用，无法删除！");


    ErrorCode RULE_TEMPLATE_BIND_BATCH_EXISTS = new ErrorCode(1022000006, "选中的考勤规中有班级在使用，无法删除！");

    ErrorCode RULE_TEMPLATE_ATTENDANCE_NAME_CHECK_EXISTS = new ErrorCode(1022000007, "到课规则名称已存在");

    ErrorCode RULE_TEMPLATE_CLASS_NOT_EXISTS = new ErrorCode(1022000001, "班级未配置考勤规则");

    ErrorCode QUESTION_CATEGORY_MANAGEMENT_NOT_EXISTS = new ErrorCode(1032000000, "题目类别管理不存在");
    ErrorCode QUESTION_CATEGORY_MANAGEMENT_EXITS_CHILDREN = new ErrorCode(1032000001, "存在存在子题目类别管理，无法删除");
    ErrorCode QUESTION_CATEGORY_MANAGEMENT_PARENT_NOT_EXITS = new ErrorCode(1032000002, "父级题目类别管理不存在");
    ErrorCode QUESTION_CATEGORY_MANAGEMENT_PARENT_ERROR = new ErrorCode(1032000003, "不能设置自己为父题目类别管理");
    ErrorCode QUESTION_CATEGORY_MANAGEMENT_FULL_NAME_DUPLICATE = new ErrorCode(1032000004, "已经存在该题目类别名称的题目类别管理");
    ErrorCode QUESTION_CATEGORY_MANAGEMENT_PARENT_IS_CHILD = new ErrorCode(1032000005, "不能设置自己的子QuestionCategoryManagement为父QuestionCategoryManagement");

    ErrorCode DEFAULT_QUESTION_CATEGORY_CANT_DELETE = new ErrorCode(1032000006, "默认题目类别管理不能删除");


    ErrorCode RULE_TEMPLATE_MEAL_ATTENDANCE_NAME_EXISTS = new ErrorCode(1022000008, "就餐规则名称已存在");

    ErrorCode RULE_TEMPLATE_CHECK_IN_NAME_EXISTS = new ErrorCode(1022000009, "住宿规则名称已存在");

    ErrorCode RULE_TEMPLATE_BAN_STATUS = new ErrorCode(1022000010, "该规则有班级在使用中，禁止禁用！");

    // ========== 题目类别管理 TODO 补充编号 ==========
    ErrorCode QUESTION_LOGIC_NOT_EXISTS = new ErrorCode(1033000000, "问题逻辑不存在");
    ErrorCode QUESTION_MANAGEMENT_NOT_EXISTS = new ErrorCode(1033000001, "题目管理不存在");
    ErrorCode QUESTIONNAIRE_MANAGEMENT_NOT_EXISTS = new ErrorCode(1033000002, "评估问卷管理不存在");

    ErrorCode QUESTIONNAIRE_MANAGEMENT_PUBLISHED = new ErrorCode(1033000003, "仅支持删除未发布的问卷");

    ErrorCode DEFAULT_QUESTIONNAIRE_EXIST = new ErrorCode(1033000004, "已有默认问卷");

    ErrorCode EDUCATE_FORM_QUESTIONNAIRE_EXIST = new ErrorCode(1033000004, "存在该教学形式的问卷");


    ErrorCode USER_NOT_TEACHER = new ErrorCode(1033000006, "该用户不是班主任");


    // ========== 全校就餐住宿考勤  ==========
    ErrorCode SCHOOL_ACCOMMODATION_ATTENDANCE_NOT_EXISTS = new ErrorCode(1023000001, "id不存在");
    ErrorCode QUESTIONNAIRE_DETAIL_NOT_EXISTS = new ErrorCode(1023000002, "评估问卷与问题关联不存在");

    ErrorCode OPTIONS_NOT_EXISTS = new ErrorCode(1023000003, "选项不存在");
    ErrorCode EVALUATION_RESPONSE_NOT_EXISTS = new ErrorCode(1023000004, "课程评价记录不存在");

    ErrorCode CLOCK_INFO_APP_NOT_EXISTS = new ErrorCode(1023000005, "当前时间无课程，您无需签到");

    ErrorCode CLOCK_TYPE_NOT_EXISTS = new ErrorCode(1023000006, "不存在的考勤类别");

    ErrorCode HAVE_DONE = new ErrorCode(1027000006, "已打卡，请勿重复打卡");

    ErrorCode CLOCK_INFO_LIVE_NOT_EXISTS = new ErrorCode(1023000007, "您无需住宿签到。");

    ErrorCode CLOCK_INFO_DINE_NOT_EXISTS = new ErrorCode(1023000008, "您无需就餐签到。");

    ErrorCode ROLL_SIGN_LECTURE_NOT_EXISTS = new ErrorCode(1023000009, "当前时间老师未发起大课考勤，您无需签到。");

    ErrorCode ROLL_SIGN_CHECK_NOT_EXISTS = new ErrorCode(1023000010, "当前时间老师未发起点名签到，您无需签到。");

    ErrorCode EVALUATION_DETAIL_NOT_EXISTS = new ErrorCode(1023000011, "课程评价详情不存在");

    ErrorCode NOT_TRAINEE = new ErrorCode(1023000012, "用户不是学员");

    ErrorCode EVALUATION_DUPLICATION = new ErrorCode(1023000013, "已有评估问卷");

    ErrorCode COURSE_TYPE_MISMATCH = new ErrorCode(1023000014, "课程类型不是选修课");
    ErrorCode CLOCK_LEAVE_NOT_SUPPORT = new ErrorCode(1023000015, "就餐、住宿不支持请假");
    ErrorCode TRAINEE_REPORT_CHECK_NOT_EXISTS = new ErrorCode(1023000016, "当前时间老师未发起报道，您无需报道。");

    // ========== 学员请假模块 1030000001 ==========
    ErrorCode LEAVE_CANCEL_NOT_ALLOWED = new ErrorCode(1030000001, "班主任已审批，无法撤回");

    ErrorCode LEAVE_CREATE_NOT_ALLOWED = new ErrorCode(1030000002, "您已结业，请勿发起请假");

    // ========== 班级考勤日历 TODO 补充编号 ==========
    ErrorCode CLASS_CLOCK_CALENDAR_NOT_EXISTS = new ErrorCode(1041000001, "班级考勤日历不存在");


    ErrorCode CLASS_HAVE_COURSE_NOT_EXISTS = new ErrorCode(1012000021, "没有已排课的班级");

    // ========== 结业考核模版设置 TODO 补充编号 ==========
    ErrorCode COMPLETION_TEMPLATE_NOT_EXISTS = new ErrorCode(1023000021, "结业考核模版设置不存在");

    ErrorCode COMPLETION_TEMPLATE_NAME_EXISTS = new ErrorCode(1023000022, "模板名称已存在");

    ErrorCode COMPLETION_TEMPLATE_DEFAULT_RULE_EXISTS = new ErrorCode(1023000023, "该校区默认规则已存在");

    ErrorCode CLASS_ID_OR_TEACHER_ID_NOT_GIVE = new ErrorCode(1023000024, "未传classId或teacherId");

    ErrorCode COMPLETION_TEMPLATE_CLASS_EXISTS = new ErrorCode(1023000024, "有班级在使用中，禁止删除");


    ErrorCode COMPLETION_TEMPLATE_CLASS_BAN_EXISTS = new ErrorCode(1023000025, "选中的模版有班级在使用中，禁止删除");

    ErrorCode DEFAULT_TEMPLATE = new ErrorCode(1023000026, "默认模板无法删除，需修改为非默认模板");
    ErrorCode TEMPLATE_NOT_EXISTS = new ErrorCode(1023000027, "模板不存在");
    // ========== 结业考核模版设置 TODO 补充编号 ==========
    ErrorCode CLASS_COMPLETION_TEMPLATE_NOT_EXISTS = new ErrorCode(1133000001, "结业考核模版设置不存在");

    /**
     * 教学计划配置
     */
    ErrorCode PLAN_CONFIG_NOT_EXISTS = new ErrorCode(1043000001, "教学计划配置不存在");

    /**
     * 教学计划模版
     */
    ErrorCode PLAN_TEMPLATE_NOT_EXISTS = new ErrorCode(1053000001, "教学计划模版不存在");

    ErrorCode PLAN_TEMPLATE_ALREADY_EXISTS = new ErrorCode(1053000002, "存在同名教学计划模版");

    /**
     * 教学计划模版配置
     */
    ErrorCode PLAN_TEMPLATE_CONFIG_NOT_EXISTS = new ErrorCode(1063000001, "教学计划模版配置不存在");

    /**
     * 课程表
     */
    ErrorCode CLASS_COURSE_BEGIN_TIME_LATER_THAN_END_TIME = new ErrorCode(1073000001, "课程结束时间需要大于开始时间");

    ErrorCode PLAN_CONFLICT = new ErrorCode(1073000002, "该时间段已有教学计划，请检查修改后再进行创建");

    /**
     * 课程表-教师-授课关系
     */
    ErrorCode CLASS_COURSE_TEACHER_NOT_EXISTS = new ErrorCode(1083000001, "课程表-教师-授课关系不存在");

    ErrorCode CLASS_NAME_NOT_MATCHES = new ErrorCode(1083000002, "请在班次名称前添加年份、春/秋季学期，例如：2025年春季学期中青年干部培训一班");

    ErrorCode CLASS_NAME_REPEAT = new ErrorCode(1083000003, "班次名称重复，请查看上方命名规则");


    // ========== 学员请假模块 1030000001 ==========
    ErrorCode ALREADY_USE = new ErrorCode(1093000001, "已转办");



    // ========== 使用次数 TODO 补充编号 ==========
    ErrorCode FREQUENCY_NOT_EXISTS = new ErrorCode(1093000001, "使用次数不存在");

    // ========== 意向单模块 1094000001 ==========
    ErrorCode YX_UNIT_TRAINING_NOT_EXISTS = new ErrorCode(1094000001, "意向单不存在");
    ErrorCode VERIFICATION_NOT_CORRECT = new ErrorCode(1094000002, "验证码不正确");

    ErrorCode TRAINING_INFORMATION_EXISTS = new ErrorCode(1094000003, "学员信息已存在");

    ErrorCode TRAINING_INFORMATION_NOT_EXISTS = new ErrorCode(1094000004, "学员信息不存在，请刷新后再试");
}
