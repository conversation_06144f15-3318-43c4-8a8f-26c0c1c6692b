<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.cadreInformation.CadreInformationMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <resultMap id="cadreInfoMap" type="com.unicom.swdx.module.edu.dal.dataobject.cadreInformation.CadreInformationDO">
        <result column="card_no" property="cardNo" typeHandler="com.unicom.swdx.framework.mybatis.core.type.SM4EncryptTypeHandler"/>
    </resultMap>

    <sql id="selectCadreInfo">
        SELECT
        eci.id,
        eci.NAME,
        eci.sex,
        eci.card_no,
        eci.phone,
        eci.educational_level,
        eci.birthday,
        eci.ethnic,
        eci.POSITION,
        eci.job_level,
        eci.graduation_school,
        eci.political_identity,
        eci.unit_id,
        eci.oldjob_level,
        esuu.unit_name,
        (
        SELECT COUNT
        ( * )
        FROM
        edu_trainee et
        JOIN edu_sign_up_unit esu ON et.unit_id = esu.ID
        WHERE
        et.card_no = eci.card_no
        AND esu.parent_id = eci.unit_id
        AND esu."template" = 0
        AND et.deleted = 0
        ) AS traineeCount
        FROM
        edu_cadre_information eci left join edu_sign_up_unit esuu on eci.unit_id = esuu.id
        WHERE
        eci.deleted = 0
        <choose>
            <when test="reqVO.idList != null and reqVO.idList.size() > 0">
                AND eci.id in
                <foreach  item="item" index="index" collection="reqVO.idList" open="(" separator="," close=" )">
                    #{item}
                </foreach>
            </when>
          <otherwise>
              <if test="reqVO.name != null and reqVO.name != ''">
                  AND eci.name LIKE CONCAT( '%', #{reqVO.name}, '%' )
              </if>
              <if test="reqVO.phone != null and reqVO.phone != ''">
                  AND eci.phone LIKE CONCAT( '%', #{reqVO.phone}, '%' )
              </if>
              <if test="reqVO.unitId != null and reqVO.unitId != ''">
                  AND eci.unit_id = #{reqVO.unitId}
              </if>
          </otherwise>
        </choose>

        order by eci.create_time desc
    </sql>

    <sql id="selectCadreInfoDetail">
        SELECT
        et.ID,
        ecm.class_name,
        ecm.class_name_code,
        ecm.class_attribute,
        ecm.class_open_time,
        ecm.completion_time,
        ecm."year",
        ecm.semester,
        eti.name AS classTeacherLead
        FROM
        edu_cadre_information eci
        inner JOIN edu_trainee et ON eci.card_no = et.card_no
        AND et.deleted = 0
        AND EXISTS ( SELECT 1 FROM edu_sign_up_unit esu WHERE esu.ID = et.unit_id AND esu.deleted = 0 AND esu.parent_id = #{reqVO.unitId} )
        LEFT JOIN edu_class_management ecm ON et.class_id = ecm."id"
        LEFT JOIN edu_teacher_information eti ON ecm.class_teacher_lead = eti."id"
        WHERE
        eci.deleted = 0
        AND eci."id" = #{reqVO.id}
        order by
        <choose>
            <when test="reqVO.order == 1"> ecm.class_name_code </when>
            <when test="reqVO.order == 2"> ecm.class_name </when>
            <when test="reqVO.order == 3"> ecm.class_attribute </when>
            <when test="reqVO.order == 4"> ecm.class_open_time </when>
            <when test="reqVO.order == 5"> ecm.completion_time </when>
        </choose>
        <choose>
            <when test="reqVO.orderType == 'asc'"> ASC </when>
            <when test="reqVO.orderType == 'desc'"> DESC </when>
        </choose>
    </sql>

    <select id="getPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.PageCadreInformationRespVO">
        <include refid="selectCadreInfo"/>
    </select>
    <select id="getList"
            resultType="com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.ExportCadreInfoExcelVO">
        <include refid="selectCadreInfo"/>
    </select>
    <select id="selectInfoDetailPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.CadreInfoDetailRespVO">
        <include refid="selectCadreInfoDetail"/>
    </select>
    <select id="selectInfoDetailList"
            resultType="com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.ExportCadreInfoDetailExcelVO">
        <include refid="selectCadreInfoDetail"/>
    </select>
    <select id="traineeInfoByUnitId"
            resultType="com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.TraineeInfoPageRespVO">
        SELECT
        eci.id,
        eci.NAME,
        eci.sex,
        eci.card_no,
        eci.phone,
        eci.educational_level,
        eci.birthday,
        eci.ethnic,
        eci.POSITION,
        eci.job_level,
        eci.graduation_school,
        eci.political_identity,
        eci.unit_id,
        (
        SELECT COUNT(*)
        FROM edu_trainee et
        JOIN edu_sign_up_unit esu ON et.unit_id = esu.ID
        WHERE et.card_no = eci.card_no
        AND esu.parent_id = eci.unit_id
        AND esu."template" = 0
        AND et.deleted = 0
        ) AS num
        FROM edu_cadre_information eci
        WHERE eci.deleted = 0
        AND eci.card_no NOT IN (
        SELECT et1.card_no
        FROM edu_trainee et1
        WHERE et1.class_id IN
        <foreach collection="classIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and et1.deleted = 0
        and et1.status in(797,798)
        )
        <if test="reqVO.nameOrPhone != null and reqVO.nameOrPhone != ''">
            AND (eci.name LIKE CONCAT( '%', #{reqVO.nameOrPhone}, '%' ) or eci.phone like concat( '%', #{reqVO.nameOrPhone}, '%' ))
        </if>
        <if test="reqVO.unitId != null and reqVO.unitId != ''">
            AND eci.unit_id = #{reqVO.unitId}
        </if>
        order by eci.create_time desc
    </select>
    <select id="getCadreInfoByTrainees"
            resultMap="cadreInfoMap">
        SELECT
        *
        FROM
        edu_cadre_information
        <where>
            deleted = 0
            <if test="list != null and list.size() > 0">
                and (card_no, unit_id) IN
                <foreach collection="list" item="item" open="(" separator="," close=")">
                    (#{item.cardNo}, #{item.unitId})
                </foreach>
            </if>
        </where>
    </select>


</mapper>
