package com.unicom.swdx.module.edu.service.plantemplateconfig;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.unicom.swdx.framework.test.core.ut.BaseDbUnitTest;

import com.unicom.swdx.module.edu.controller.admin.plantemplateconfig.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plantemplateconfig.PlanTemplateConfigDO;
import com.unicom.swdx.module.edu.dal.mysql.plantemplateconfig.PlanTemplateConfigMapper;
import com.unicom.swdx.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.framework.test.core.util.AssertUtils.*;
import static com.unicom.swdx.framework.test.core.util.RandomUtils.*;
import static com.unicom.swdx.framework.common.util.object.ObjectUtils.*;
import static com.unicom.swdx.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link PlanTemplateConfigServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(PlanTemplateConfigServiceImpl.class)
public class PlanTemplateConfigServiceImplTest extends BaseDbUnitTest {

    @Resource
    private PlanTemplateConfigServiceImpl planTemplateConfigService;

    @Resource
    private PlanTemplateConfigMapper planTemplateConfigMapper;

    @Test
    public void testCreatePlanTemplateConfig_success() {
        // 准备参数
        PlanTemplateConfigCreateReqVO reqVO = randomPojo(PlanTemplateConfigCreateReqVO.class);

        // 调用
        Long planTemplateConfigId = planTemplateConfigService.createPlanTemplateConfig(reqVO);
        // 断言
        assertNotNull(planTemplateConfigId);
        // 校验记录的属性是否正确
        PlanTemplateConfigDO planTemplateConfig = planTemplateConfigMapper.selectById(planTemplateConfigId);
        assertPojoEquals(reqVO, planTemplateConfig);
    }

    @Test
    public void testUpdatePlanTemplateConfig_success() {
        // mock 数据
        PlanTemplateConfigDO dbPlanTemplateConfig = randomPojo(PlanTemplateConfigDO.class);
        planTemplateConfigMapper.insert(dbPlanTemplateConfig);// @Sql: 先插入出一条存在的数据
        // 准备参数
        PlanTemplateConfigUpdateReqVO reqVO = randomPojo(PlanTemplateConfigUpdateReqVO.class, o -> {
            o.setId(dbPlanTemplateConfig.getId()); // 设置更新的 ID
        });

        // 调用
        planTemplateConfigService.updatePlanTemplateConfig(reqVO);
        // 校验是否更新正确
        PlanTemplateConfigDO planTemplateConfig = planTemplateConfigMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, planTemplateConfig);
    }

    @Test
    public void testUpdatePlanTemplateConfig_notExists() {
        // 准备参数
        PlanTemplateConfigUpdateReqVO reqVO = randomPojo(PlanTemplateConfigUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> planTemplateConfigService.updatePlanTemplateConfig(reqVO), PLAN_TEMPLATE_CONFIG_NOT_EXISTS);
    }

    @Test
    public void testDeletePlanTemplateConfig_success() {
        // mock 数据
        PlanTemplateConfigDO dbPlanTemplateConfig = randomPojo(PlanTemplateConfigDO.class);
        planTemplateConfigMapper.insert(dbPlanTemplateConfig);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlanTemplateConfig.getId();

        // 调用
        planTemplateConfigService.deletePlanTemplateConfig(id);
       // 校验数据不存在了
       assertNull(planTemplateConfigMapper.selectById(id));
    }

    @Test
    public void testDeletePlanTemplateConfig_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planTemplateConfigService.deletePlanTemplateConfig(id), PLAN_TEMPLATE_CONFIG_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanTemplateConfigPage() {
       // mock 数据
       PlanTemplateConfigDO dbPlanTemplateConfig = randomPojo(PlanTemplateConfigDO.class, o -> { // 等会查询到
           o.setTemplateId(null);
           o.setDayOfWeek(null);
           o.setPeriod(null);
           o.setBeginTime(null);
           o.setEndTime(null);
           o.setCreateTime(null);
       });
       planTemplateConfigMapper.insert(dbPlanTemplateConfig);
       // 测试 templateId 不匹配
       planTemplateConfigMapper.insert(cloneIgnoreId(dbPlanTemplateConfig, o -> o.setTemplateId(null)));
       // 测试 dayOfWeek 不匹配
       planTemplateConfigMapper.insert(cloneIgnoreId(dbPlanTemplateConfig, o -> o.setDayOfWeek(null)));
       // 测试 period 不匹配
       planTemplateConfigMapper.insert(cloneIgnoreId(dbPlanTemplateConfig, o -> o.setPeriod(null)));
       // 测试 beginTime 不匹配
       planTemplateConfigMapper.insert(cloneIgnoreId(dbPlanTemplateConfig, o -> o.setBeginTime(null)));
       // 测试 endTime 不匹配
       planTemplateConfigMapper.insert(cloneIgnoreId(dbPlanTemplateConfig, o -> o.setEndTime(null)));
       // 测试 createTime 不匹配
       planTemplateConfigMapper.insert(cloneIgnoreId(dbPlanTemplateConfig, o -> o.setCreateTime(null)));
       // 准备参数
       PlanTemplateConfigPageReqVO reqVO = new PlanTemplateConfigPageReqVO();
       reqVO.setTemplateId(null);
       reqVO.setDayOfWeek(null);
       reqVO.setPeriod(null);
//       reqVO.setBeginTime((new LocalDateTime[]{}));
//       reqVO.setEndTime((new LocalDateTime[]{}));
//       reqVO.setCreateTime((new LocalDateTime[]{}));

       // 调用
       PageResult<PlanTemplateConfigDO> pageResult = planTemplateConfigService.getPlanTemplateConfigPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlanTemplateConfig, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanTemplateConfigList() {
       // mock 数据
       PlanTemplateConfigDO dbPlanTemplateConfig = randomPojo(PlanTemplateConfigDO.class, o -> { // 等会查询到
           o.setTemplateId(null);
           o.setDayOfWeek(null);
           o.setPeriod(null);
           o.setBeginTime(null);
           o.setEndTime(null);
           o.setCreateTime(null);
       });
       planTemplateConfigMapper.insert(dbPlanTemplateConfig);
       // 测试 templateId 不匹配
       planTemplateConfigMapper.insert(cloneIgnoreId(dbPlanTemplateConfig, o -> o.setTemplateId(null)));
       // 测试 dayOfWeek 不匹配
       planTemplateConfigMapper.insert(cloneIgnoreId(dbPlanTemplateConfig, o -> o.setDayOfWeek(null)));
       // 测试 period 不匹配
       planTemplateConfigMapper.insert(cloneIgnoreId(dbPlanTemplateConfig, o -> o.setPeriod(null)));
       // 测试 beginTime 不匹配
       planTemplateConfigMapper.insert(cloneIgnoreId(dbPlanTemplateConfig, o -> o.setBeginTime(null)));
       // 测试 endTime 不匹配
       planTemplateConfigMapper.insert(cloneIgnoreId(dbPlanTemplateConfig, o -> o.setEndTime(null)));
       // 测试 createTime 不匹配
       planTemplateConfigMapper.insert(cloneIgnoreId(dbPlanTemplateConfig, o -> o.setCreateTime(null)));
       // 准备参数
       PlanTemplateConfigExportReqVO reqVO = new PlanTemplateConfigExportReqVO();
       reqVO.setTemplateId(null);
       reqVO.setDayOfWeek(null);
       reqVO.setPeriod(null);
//       reqVO.setBeginTime((new LocalDateTime[]{}));
//       reqVO.setEndTime((new LocalDateTime[]{}));
//       reqVO.setCreateTime((new LocalDateTime[]{}));

       // 调用
       List<PlanTemplateConfigDO> list = planTemplateConfigService.getPlanTemplateConfigList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlanTemplateConfig, list.get(0));
    }

}
