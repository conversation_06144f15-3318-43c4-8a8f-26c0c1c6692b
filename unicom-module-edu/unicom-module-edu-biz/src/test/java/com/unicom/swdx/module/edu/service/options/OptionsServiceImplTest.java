package com.unicom.swdx.module.edu.service.options;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.unicom.swdx.framework.test.core.ut.BaseDbUnitTest;

import com.unicom.swdx.module.edu.controller.admin.options.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.options.OptionsDO;
import com.unicom.swdx.module.edu.dal.mysql.options.OptionsMapper;
import com.unicom.swdx.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.framework.test.core.util.AssertUtils.*;
import static com.unicom.swdx.framework.test.core.util.RandomUtils.*;

import static com.unicom.swdx.framework.common.util.object.ObjectUtils.*;
import static com.unicom.swdx.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link OptionsServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(OptionsServiceImpl.class)
public class OptionsServiceImplTest extends BaseDbUnitTest {

    @Resource
    private OptionsServiceImpl optionsService;

    @Resource
    private OptionsMapper optionsMapper;

    @Test
    public void testCreateOptions_success() {
        // 准备参数
        OptionsSaveReqVO createReqVO = randomPojo(OptionsSaveReqVO.class).setId(null);

        // 调用
        Long optionsId = optionsService.createOptions(createReqVO);
        // 断言
        assertNotNull(optionsId);
        // 校验记录的属性是否正确
        OptionsDO options = optionsMapper.selectById(optionsId);
        assertPojoEquals(createReqVO, options, "id");
    }

    @Test
    public void testUpdateOptions_success() {
        // mock 数据
        OptionsDO dbOptions = randomPojo(OptionsDO.class);
        optionsMapper.insert(dbOptions);// @Sql: 先插入出一条存在的数据
        // 准备参数
        OptionsSaveReqVO updateReqVO = randomPojo(OptionsSaveReqVO.class, o -> {
            o.setId(dbOptions.getId()); // 设置更新的 ID
        });

        // 调用
        optionsService.updateOptions(updateReqVO);
        // 校验是否更新正确
        OptionsDO options = optionsMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, options);
    }

    @Test
    public void testUpdateOptions_notExists() {
        // 准备参数
        OptionsSaveReqVO updateReqVO = randomPojo(OptionsSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> optionsService.updateOptions(updateReqVO), OPTIONS_NOT_EXISTS);
    }

    @Test
    public void testDeleteOptions_success() {
        // mock 数据
        OptionsDO dbOptions = randomPojo(OptionsDO.class);
        optionsMapper.insert(dbOptions);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbOptions.getId();

        // 调用
        optionsService.deleteOptions(id);
       // 校验数据不存在了
       assertNull(optionsMapper.selectById(id));
    }

    @Test
    public void testDeleteOptions_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> optionsService.deleteOptions(id), OPTIONS_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetOptionsPage() {
       // mock 数据
       OptionsDO dbOptions = randomPojo(OptionsDO.class, o -> { // 等会查询到
           o.setOptionsType(null);
           o.setContent(null);
           o.setScore(null);
           o.setCreateDept(null);
           o.setCreateTime(null);
//           o.setCreateBy(null);
//           o.setUpdateBy(null);
//           o.setDelFlag(null);
           o.setQuestionId(null);
       });
       optionsMapper.insert(dbOptions);
       // 测试 optionsType 不匹配
       optionsMapper.insert(cloneIgnoreId(dbOptions, o -> o.setOptionsType(null)));
       // 测试 content 不匹配
       optionsMapper.insert(cloneIgnoreId(dbOptions, o -> o.setContent(null)));
       // 测试 score 不匹配
       optionsMapper.insert(cloneIgnoreId(dbOptions, o -> o.setScore(null)));
       // 测试 createDept 不匹配
       optionsMapper.insert(cloneIgnoreId(dbOptions, o -> o.setCreateDept(null)));
       // 测试 createTime 不匹配
       optionsMapper.insert(cloneIgnoreId(dbOptions, o -> o.setCreateTime(null)));
       // 测试 createBy 不匹配
//       optionsMapper.insert(cloneIgnoreId(dbOptions, o -> o.setCreateBy(null)));
//       // 测试 updateBy 不匹配
//       optionsMapper.insert(cloneIgnoreId(dbOptions, o -> o.setUpdateBy(null)));
//       // 测试 delFlag 不匹配
//       optionsMapper.insert(cloneIgnoreId(dbOptions, o -> o.setDelFlag(null)));
       // 测试 questionId 不匹配
       optionsMapper.insert(cloneIgnoreId(dbOptions, o -> o.setQuestionId(null)));
       // 准备参数
       OptionsPageReqVO reqVO = new OptionsPageReqVO();
       reqVO.setOptionsType(null);
       reqVO.setContent(null);
       reqVO.setScore(null);
       reqVO.setCreateDept(null);

//       reqVO.setCreateBy(null);
//       reqVO.setUpdateBy(null);
//       reqVO.setDelFlag(null);
       reqVO.setQuestionId(null);

       // 调用
       PageResult<OptionsDO> pageResult = optionsService.getOptionsPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbOptions, pageResult.getList().get(0));
    }

}
