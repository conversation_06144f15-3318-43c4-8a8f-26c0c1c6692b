package com.unicom.swdx.module.edu.controller.admin.teachercourseinformation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

@ApiModel(value = "管理后台 - 师资-任课信息中间 Excel 导出 Request VO", description = "参数和 TeacherCourseInformationPageReqVO 是一致的")
@Data
public class TeacherCourseInformationExportReqVO {

    @ApiModelProperty(value = "课程ID")
    private Long coursesId;

    @ApiModelProperty(value = "教师ID")
    private Long teacherId;

    @ApiModelProperty(value = "部门 ID")
    private Long deptId;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;
}
