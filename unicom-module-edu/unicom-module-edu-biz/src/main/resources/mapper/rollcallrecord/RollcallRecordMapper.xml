<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.rollcallrecord.RollcallRecordMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getRollcallSignInTraineeInfo"
            resultType="com.unicom.swdx.module.edu.controller.admin.traineegroup.AppTraineeGroupRespVO">
        select
            err.trainee_id id,
            etg.group_name groupName,
            et."name" "name",
            et.sex,
            et.phone,
            et."position",
            et.class_committee_id,
            ecc.class_committee_name
        from edu_rollcall_record err
                 left join edu_trainee et on err.trainee_id = et.id and et.deleted = 0
                 left join edu_class_committee ecc on ecc.id  = et.class_committee_id and ecc.deleted = 0
                 left join edu_trainee_group etg on et.group_id = etg.id and etg.deleted = 0
        where err.deleted = 0
          and err.rollcall_id = #{id}
          and err.status = #{status}
        order by etg.sort, et.group_sort
    </select>
</mapper>
