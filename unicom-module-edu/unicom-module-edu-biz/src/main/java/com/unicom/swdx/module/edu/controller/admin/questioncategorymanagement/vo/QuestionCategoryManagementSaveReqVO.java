package com.unicom.swdx.module.edu.controller.admin.questioncategorymanagement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 题目类别管理新增/修改 Request VO")
@Data
public class QuestionCategoryManagementSaveReqVO {

    @Schema(description = "主键ID",  example = "4884")
    private Long id;

    @Schema(description = "题目类别名称", example = "李四")
    private String fullName;

    @Schema(description = "创建部门")
    private Long createDept;

    @Schema(description = "父级", example = "2048")
    private Long parentId;

    @Schema(description = "是否内置", example = "false")
    private Boolean builtIn;

}
