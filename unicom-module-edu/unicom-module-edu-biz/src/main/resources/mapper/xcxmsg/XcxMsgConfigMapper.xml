<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.xcxmsg.XcxMsgConfigMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPageByReqVO"
            resultType="com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgConfigRespVO">
        SELECT exc.id,
        exc.title,
        exc.inform_content,
        exc.inform_time,
        exc.remark,
        exc.status,
        exc.tag
        FROM edu_xcxmsg_config exc
        where exc.deleted = 0
        <if test="reqVO.tag != null and reqVO.tag != ''">
            AND exc.tag in (#{reqVO.tag})
        </if>
        <if test="reqVO.title != null and reqVO.title != ''">
            AND exc.title LIKE CONCAT('%',#{reqVO.title},'%')
        </if>
    </select>

    <select id="selectEvaluateMsgIds" resultType="java.lang.Long">
        select c.system_id from pg_evaluation_response a
        left join edu_trainee b on a.student_id = b.id
        left join system_users c on c.id = b.user_id
        where a.deleted = 0 and b.deleted = 0 and c.deleted = 0
        and a.handle = 0 and a.tenant_id = #{tenantId}
        <if test="detailTime != null and detailTime != ''">
            and (a.expire_time > #{detailTime} or a.expire_time is null)
        </if>
        group by system_id
    </select>

    <select id="selectAttendanceMsgIds" resultType="java.lang.Long">
        select c.system_id from edu_clock_in_info a
        left join edu_trainee b on a.trainee_id = b.id
        left join system_users c on c.id = b.user_id
        where a.deleted = 0 and b.deleted = 0 and c.deleted = 0
        and a.type = 2 and a.trainee_status = 0 and a.tenant_id = #{tenantId}
        <if test="dateTime != null and dateTime != ''">
            and a.date like concat(#{dateTime},'%')
        </if>
        group by system_id
    </select>

    <select id="selectCourseMsgIds" resultType="java.lang.Long">
        select b.system_id from edu_class_course a
        left join edu_teacher_information b on b.id = ANY(STRING_TO_ARRAY(a.teacher_id_string, ',')::int[])
        where a.deleted = 0 and b.deleted = 0
        and a.is_temporary = 0 and a.tenant_id = #{tenantId}
        <if test="dateTime != null and dateTime != ''">
            and a."date" like concat(#{dateTime},'%')
        </if>
        and a.department = 0
        group by system_id
    </select>

</mapper>
