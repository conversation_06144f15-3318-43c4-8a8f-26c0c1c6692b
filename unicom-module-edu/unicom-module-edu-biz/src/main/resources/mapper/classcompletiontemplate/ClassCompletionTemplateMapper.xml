<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.classcompletiontemplate.ClassCompletionTemplateMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectListByClassId" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            edu_class_completion_template
        WHERE
            "deleted" = '0'
          <if test="classId != null">
              AND
              "class_id" = #{classId}
          </if>
          <if test="save == 0">
              and save = #{save}
          </if>
        <if test="save == 1">
            and save = #{save}
        </if>

    </select>

    <delete id="deletedByClassId">
        DELETE
            FROM
               edu_class_completion_template
        WHERE
            "class_id" = #{classId}
          and
            save = '0'
    </delete>

    <select id="selectListByClassIdAndSave" resultType="java.lang.Long">
        SELECT
            DISTINCT ecct ."class_id"
        FROM
            "edu_class_completion_template" ecct
        WHERE
            ecct ."deleted" = '0'
          AND
            ecct ."save" = '1'
        <if test="idCode != null and idCode != ''">
            and ecct.id_code = #{idCode}
        </if>
    </select>
</mapper>
