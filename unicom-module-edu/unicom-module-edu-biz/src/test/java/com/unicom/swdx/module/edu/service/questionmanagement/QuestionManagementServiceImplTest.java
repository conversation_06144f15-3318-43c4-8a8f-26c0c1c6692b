package com.unicom.swdx.module.edu.service.questionmanagement;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.unicom.swdx.framework.test.core.ut.BaseDbUnitTest;

import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionmanagement.QuestionManagementDO;
import com.unicom.swdx.module.edu.dal.mysql.questionmanagement.QuestionManagementMapper;
import com.unicom.swdx.framework.common.pojo.PageResult;


import org.springframework.context.annotation.Import;

import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.framework.test.core.util.AssertUtils.*;
import static com.unicom.swdx.framework.test.core.util.RandomUtils.*;

import static com.unicom.swdx.framework.common.util.object.ObjectUtils.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link QuestionManagementServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(QuestionManagementServiceImpl.class)
public class QuestionManagementServiceImplTest extends BaseDbUnitTest {

    @Resource
    private QuestionManagementServiceImpl questionManagementService;

    @Resource
    private QuestionManagementMapper questionManagementMapper;

    @Test
    public void testCreateQuestionManagement_success() {
        // 准备参数
        QuestionManagementSaveReqVO createReqVO = randomPojo(QuestionManagementSaveReqVO.class).setId(null);

        // 调用
        Long questionManagementId = questionManagementService.createQuestionManagement(createReqVO);
        // 断言
        assertNotNull(questionManagementId);
        // 校验记录的属性是否正确
        QuestionManagementDO questionManagement = questionManagementMapper.selectById(questionManagementId);
        assertPojoEquals(createReqVO, questionManagement, "id");
    }

    @Test
    public void testUpdateQuestionManagement_success() {
        // mock 数据
        QuestionManagementDO dbQuestionManagement = randomPojo(QuestionManagementDO.class);
        questionManagementMapper.insert(dbQuestionManagement);// @Sql: 先插入出一条存在的数据
        // 准备参数
        QuestionManagementSaveReqVO updateReqVO = randomPojo(QuestionManagementSaveReqVO.class, o -> {
            o.setId(dbQuestionManagement.getId()); // 设置更新的 ID
        });

        // 调用
        questionManagementService.updateQuestionManagement(updateReqVO);
        // 校验是否更新正确
        QuestionManagementDO questionManagement = questionManagementMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, questionManagement);
    }

    @Test
    public void testUpdateQuestionManagement_notExists() {
        // 准备参数
        QuestionManagementSaveReqVO updateReqVO = randomPojo(QuestionManagementSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> questionManagementService.updateQuestionManagement(updateReqVO), QUESTION_MANAGEMENT_NOT_EXISTS);
    }

    @Test
    public void testDeleteQuestionManagement_success() {
        // mock 数据
        QuestionManagementDO dbQuestionManagement = randomPojo(QuestionManagementDO.class);
        questionManagementMapper.insert(dbQuestionManagement);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbQuestionManagement.getId();

        // 调用
        questionManagementService.deleteQuestionManagement(id);
       // 校验数据不存在了
       assertNull(questionManagementMapper.selectById(id));
    }

    @Test
    public void testDeleteQuestionManagement_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> questionManagementService.deleteQuestionManagement(id), QUESTION_MANAGEMENT_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetQuestionManagementPage() {
       // mock 数据
       QuestionManagementDO dbQuestionManagement = randomPojo(QuestionManagementDO.class, o -> { // 等会查询到
           o.setStem(null);
           o.setQuestionType(null);
           o.setScore(null);
           o.setCategoryId(null);
           o.setDescription(null);
           o.setCreateDept(null);
           o.setCreateTime(null);
           o.setCreator(null);
           o.setOneBallotVetoResult(null);
           o.setIsLogic(null);
       });
       questionManagementMapper.insert(dbQuestionManagement);
       // 测试 stem 不匹配
       questionManagementMapper.insert(cloneIgnoreId(dbQuestionManagement, o -> o.setStem(null)));
       // 测试 questionType 不匹配
       questionManagementMapper.insert(cloneIgnoreId(dbQuestionManagement, o -> o.setQuestionType(null)));
       // 测试 score 不匹配
       questionManagementMapper.insert(cloneIgnoreId(dbQuestionManagement, o -> o.setScore(null)));
       // 测试 categoryId 不匹配
       questionManagementMapper.insert(cloneIgnoreId(dbQuestionManagement, o -> o.setCategoryId(null)));
       // 测试 description 不匹配
       questionManagementMapper.insert(cloneIgnoreId(dbQuestionManagement, o -> o.setDescription(null)));
       // 测试 createDept 不匹配
       questionManagementMapper.insert(cloneIgnoreId(dbQuestionManagement, o -> o.setCreateDept(null)));
       // 测试 createTime 不匹配
       questionManagementMapper.insert(cloneIgnoreId(dbQuestionManagement, o -> o.setCreateTime(null)));
       // 测试 creator 不匹配
       questionManagementMapper.insert(cloneIgnoreId(dbQuestionManagement, o -> o.setCreator(null)));
       // 测试 oneBallotVetoResult 不匹配
       questionManagementMapper.insert(cloneIgnoreId(dbQuestionManagement, o -> o.setOneBallotVetoResult(null)));
       // 测试 isLogic 不匹配
       questionManagementMapper.insert(cloneIgnoreId(dbQuestionManagement, o -> o.setIsLogic(null)));
       // 准备参数
       QuestionManagementPageReqVO reqVO = new QuestionManagementPageReqVO();
       reqVO.setStem(null);
       reqVO.setQuestionType(null);
       reqVO.setScore(null);
       reqVO.setCategoryId(null);
       reqVO.setDescription(null);
       reqVO.setCreateDept(null);
       reqVO.setCreator(null);
       reqVO.setOneBallotVetoResult(null);
       reqVO.setIsLogic(null);

       // 调用
       PageResult<QuestionManagementRespVO> pageResult = questionManagementService.getQuestionManagementPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbQuestionManagement, pageResult.getList().get(0));
    }

}
