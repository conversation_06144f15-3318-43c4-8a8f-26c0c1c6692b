<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.traineegroup.TraineeGroupMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectGroupPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupRespVO">
        select
            et.id,
            et.name,
            et.sex,
            et.birthday ,
            et.educational_level ,
            et.political_identity ,
            et."position" ,
            etg.group_name,
            et.group_id,
            et.class_committee_id,
            cc.class_committee_name
        from
            edu_trainee et
                left join edu_trainee_group etg on
                et.group_id = etg.id
            left join edu_class_committee cc on
                et.class_committee_id = cc.id
        where
            et.deleted = 0
          and et.class_id =#{reqVO.classId}
        <if test="reqVO.groupId != null and reqVO.groupId != 0">
            and et.group_id = #{reqVO.groupId}
        </if>
        <if test="reqVO.groupId != null and reqVO.groupId == 0">
            and (et.group_id is null or et.group_id = 0)
        </if>

        order by
            etg.sort,et.group_sort
    </select>
    <select id="selectGroupList"
            resultType="com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupRespVO">
        select
        et.id,
        et.name,
        et.sex,
        et.birthday ,
        et.educational_level ,
        et.political_identity ,
        et."position" ,
        etg.group_name,
        et.group_id,
        et.class_committee_id,
        cc.class_committee_name
        from
        edu_trainee et
        left join edu_trainee_group etg on
        et.group_id = etg.id
        left join edu_class_committee cc on
        et.class_committee_id = cc.id
        where
        et.deleted = 0
        and et.class_id =#{reqVO.classId}
          <choose>
              <when test="reqVO.idList != null and reqVO.idList.size()>0">
                    and et.id in
                    <foreach collection="reqVO.idList" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
              </when>
              <otherwise>
                  <if test="reqVO.groupId != null and reqVO.groupId != 0">
                      and et.group_id = #{reqVO.groupId}
                  </if>
                  <if test="reqVO.groupId != null and reqVO.groupId == 0">
                      and (et.group_id is null or et.group_id =0)
                  </if>
              </otherwise>
          </choose>
        order by
        etg.sort,et.group_sort
    </select>
</mapper>
