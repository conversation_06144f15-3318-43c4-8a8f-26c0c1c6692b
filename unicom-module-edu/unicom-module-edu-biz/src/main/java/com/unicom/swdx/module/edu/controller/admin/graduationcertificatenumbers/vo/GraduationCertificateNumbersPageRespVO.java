package com.unicom.swdx.module.edu.controller.admin.graduationcertificatenumbers.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description: 管理后台-调训系统-学员结业证书 分页查询 Response VO
 * @date 2024-11-19
 */
@ApiModel("管理后台-调训系统-学员结业证书 Response VO")
@Data
public class GraduationCertificateNumbersPageRespVO {

    @ApiModelProperty(value = "序号", example = "1")
    private Long serialNumber;

    @ApiModelProperty(value = "学员ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long traineeId;

    @ApiModelProperty(value = "学员姓名", example = "课程")
    private String traineeName;

    @ApiModelProperty(value = "证书编号", example = "20012125")
    private String certificateNumber;

    @ApiModelProperty(value = "开班日期", example = "2024-11-19")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate classOpenDate;

    @ApiModelProperty(value = "结业日期", example = "2024-11-19")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate completionDate;

    @ApiModelProperty(value = "生成日期", example = "2024-11-19")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate generateDate;
}
