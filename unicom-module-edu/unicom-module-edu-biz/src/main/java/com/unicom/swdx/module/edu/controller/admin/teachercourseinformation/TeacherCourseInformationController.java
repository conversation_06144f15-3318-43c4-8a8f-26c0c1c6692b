package com.unicom.swdx.module.edu.controller.admin.teachercourseinformation;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.edu.controller.admin.teachercourseinformation.vo.*;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationSimpleRespVO;
import com.unicom.swdx.module.edu.convert.teachercourseinformation.TeacherCourseInformationConvert;
import com.unicom.swdx.module.edu.dal.dataobject.teachercourseinformation.TeacherCourseInformationDO;
import com.unicom.swdx.module.edu.service.teachercourseinformation.TeacherCourseInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Api(tags = "管理后台 - 师资-任课信息中间")
@RestController
@RequestMapping("/edu/teacher-course-information")
@Validated
public class TeacherCourseInformationController {

    @Resource
    private TeacherCourseInformationService teacherCourseInformationService;

    @PostMapping("/create")
    @ApiOperation("创建师资-任课信息中间")
    // @PreAuthorize("@ss.hasPermission('edu:teacher-course-information:create')")
    public CommonResult<Long> createTeacherCourseInformation(@Valid @RequestBody TeacherCourseInformationCreateReqVO createReqVO) {
        return success(teacherCourseInformationService.createTeacherCourseInformation(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新师资-任课信息中间")
    // @PreAuthorize("@ss.hasPermission('edu:teacher-course-information:update')")
    public CommonResult<Boolean> updateTeacherCourseInformation(@Valid @RequestBody TeacherCourseInformationUpdateReqVO updateReqVO) {
        teacherCourseInformationService.updateTeacherCourseInformation(updateReqVO);
        return success(true);
    }

    @PostMapping("/batchInsert")
    @ApiOperation("批量创建师资-任课信息中间")
    @PreAuthorize("@ss.hasPermission('edu:teacher-course-information:create')")
    public CommonResult<Long> batchCreateTeacherCourseInformation(@Valid @RequestBody TeacherCourseInformationBatchCreateReqVO createReqVO) {
        return success(teacherCourseInformationService.batchCreateTeacherCourseInformation(createReqVO));
    }

    @PostMapping("/delete")
    @ApiOperation("删除师资-任课信息中间")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    // @PreAuthorize("@ss.hasPermission('edu:teacher-course-information:delete')")
    public CommonResult<Boolean> deleteTeacherCourseInformation(@RequestParam("id") Long id) {
        teacherCourseInformationService.deleteTeacherCourseInformation(id);
        return success(true);
    }

    @GetMapping("/getSimpleListByCourseId")
    @ApiOperation("根据课程id获取其授课教师下拉框")
    @ApiImplicitParam(name = "courseId", value = "课程id", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:teacher-course-information:query')")
    public CommonResult<List<TeacherInformationSimpleRespVO>> getSimpleListByCourseId(@RequestParam("courseId") Long courseId) {
        List<TeacherInformationSimpleRespVO> respVOList = teacherCourseInformationService.getSimpleListByCourseId(courseId);
        return success(respVOList);
    }

    @GetMapping("/get")
    @ApiOperation("获得师资-任课信息中间")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    // @PreAuthorize("@ss.hasPermission('edu:teacher-course-information:query')")
    public CommonResult<TeacherCourseInformationRespVO> getTeacherCourseInformation(@RequestParam("id") Long id) {
        TeacherCourseInformationDO teacherCourseInformation = teacherCourseInformationService.getTeacherCourseInformation(id);
        return success(TeacherCourseInformationConvert.INSTANCE.convert(teacherCourseInformation));
    }

    @GetMapping("/list")
    @ApiOperation("获得师资-任课信息中间列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    // @PreAuthorize("@ss.hasPermission('edu:teacher-course-information:query')")
    public CommonResult<List<TeacherCourseInformationRespVO>> getTeacherCourseInformationList(@RequestParam("ids") Collection<Long> ids) {
        List<TeacherCourseInformationDO> list = teacherCourseInformationService.getTeacherCourseInformationList(ids);
        return success(TeacherCourseInformationConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得师资-任课信息中间分页")
    // @PreAuthorize("@ss.hasPermission('edu:teacher-course-information:query')")
    public CommonResult<PageResult<TeacherCourseInformationRespVO>> getTeacherCourseInformationPage(@Valid TeacherCourseInformationPageReqVO pageVO) {
        PageResult<TeacherCourseInformationDO> pageResult = teacherCourseInformationService.getTeacherCourseInformationPage(pageVO);
        return success(TeacherCourseInformationConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @ApiOperation("导出师资-任课信息中间 Excel")
    // @PreAuthorize("@ss.hasPermission('edu:teacher-course-information:export')")
    @OperateLog(type = EXPORT)
    public void exportTeacherCourseInformationExcel(@Valid TeacherCourseInformationExportReqVO exportReqVO,
                                                    HttpServletResponse response) throws IOException {
        List<TeacherCourseInformationDO> list = teacherCourseInformationService.getTeacherCourseInformationList(exportReqVO);
        // 导出 Excel
        List<TeacherCourseInformationExcelVO> datas = TeacherCourseInformationConvert.INSTANCE.convertList02(list);
        ExcelUtils.writeByIncludeColumnIndexes(response, "师资-任课信息中间.xls", "数据", TeacherCourseInformationExcelVO.class, datas, exportReqVO.getIncludeColumnIndexes());
    }

}
