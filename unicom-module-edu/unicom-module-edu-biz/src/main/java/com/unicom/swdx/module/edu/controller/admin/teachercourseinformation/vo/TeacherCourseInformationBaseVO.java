package com.unicom.swdx.module.edu.controller.admin.teachercourseinformation.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 师资-任课信息中间 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class TeacherCourseInformationBaseVO {

    @ApiModelProperty(value = "课程ID", required = true)
    @NotNull(message = "课程ID不能为空")
    private Long coursesId;

    @ApiModelProperty(value = "教师ID", required = true)
    @NotNull(message = "教师ID不能为空")
    private Long teacherId;

    @ApiModelProperty(value = "部门 ID")
    private Long deptId;

}
