package com.unicom.swdx.module.edu.controller.admin.electivetraineeselection;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo.*;
import com.unicom.swdx.module.edu.service.electivetraineeselection.ElectiveTraineeSelectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Api(tags = "管理后台 - 选修课学员选课")
@RestController
@RequestMapping("/edu/elective-trainee-selection")
@Validated
public class ElectiveTraineeSelectionController {

    @Resource
    private ElectiveTraineeSelectionService electiveTraineeSelectionService;

    @PostMapping("/create")
    @ApiOperation("学员移动端-选修课-学员选课")
    @PreAuthorize("@ss.hasPermission('edu:elective-trainee-selection:create')")
    public CommonResult<Long> createElectiveTraineeSelection(@Valid @RequestBody ElectiveTraineeSelectionCreateReqVO createReqVO) {
        return success(electiveTraineeSelectionService.createElectiveTraineeSelection(createReqVO));
    }

    @PostMapping("/batchCreate")
    @ApiOperation("学员移动端-选修课-学员批量选课")
    @PreAuthorize("@ss.hasPermission('edu:elective-trainee-selection:create')")
    public CommonResult<Boolean> createElectiveTraineeSelection(@Valid @RequestBody List<ElectiveTraineeSelectionCreateReqVO> batchCreateReqVO) {
        return success(electiveTraineeSelectionService.batchCreateElectiveTraineeSelection(batchCreateReqVO));
    }

    @PostMapping("/course-selected-trainee-page")
    @ApiOperation("发布课程已选课人员信息分页")
    @PreAuthorize("@ss.hasPermission('edu:elective-trainee-selection:query')")
    public CommonResult<PageResult<ElectiveCourseTraineeSelectedRespVO>> getCourseSelectedTraineePage(@Valid @RequestBody ElectiveCourseTraineeSelectedPageReqVO reqVO) {
        PageResult<ElectiveCourseTraineeSelectedRespVO> pageResult = electiveTraineeSelectionService.getCourseSelectedTraineePage(reqVO);
        return success(pageResult);
    }

    @PostMapping("/export-course-selected-trainee-excel")
    @ApiOperation("导出发布课程已选课人员信息")
    @PreAuthorize("@ss.hasPermission('edu:elective-trainee-selection:export')")
    @OperateLog(type = EXPORT)
    public void exportCourseSelectedTraineeExcel(@Valid @RequestBody ElectiveCourseTraineeSelectedPageReqVO reqVO,
                                                 HttpServletResponse response) throws IOException {
        electiveTraineeSelectionService.exportCourseSelectedTraineeExcel(reqVO, response);
    }

    @PostMapping("/list-trainee-selection-app")
    @ApiOperation("班主任移动端-选修课管理-选课人员详情信息列表")
    @PreAuthorize("@ss.hasPermission('edu:elective-trainee-selection:query')")
    public CommonResult<List<AppElectiveTraineeSelectionSimpleRespVO>> getCourseSelectedTraineeListForApp(@Valid @RequestBody AppElectiveTraineeSelectionReleaseReqVO reqVO) {
        List<AppElectiveTraineeSelectionSimpleRespVO> list = electiveTraineeSelectionService.getCourseSelectionTraineeList(reqVO);
        return success(list);
    }

    @GetMapping("/exist-unselect-teacher")
    @ApiOperation("班主任移动端-选修课管理-班级是否存在未选学员(气泡)")
    @ApiImplicitParam(name = "classId", value = "指定班级ID", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:elective-trainee-selection:query')")
    public CommonResult<Boolean> hasUnSelectTeacher(@RequestParam("classId") Long classId) {
        boolean hasUnSelect = electiveTraineeSelectionService.existUnSelectTeacher(classId);
        return success(hasUnSelect);
    }

    @GetMapping("/exist-unselect-trainee")
    @ApiOperation("学员移动端-选修课-是否存在未选选修课(气泡)")
    @PreAuthorize("@ss.hasPermission('edu:elective-trainee-selection:query')")
    public CommonResult<Boolean> hasUnSelectTrainee() {
        boolean hasUnSelect = electiveTraineeSelectionService.existUnSelectTrainee();
        return success(hasUnSelect);
    }



}
