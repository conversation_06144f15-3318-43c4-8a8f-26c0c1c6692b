package com.unicom.swdx.module.edu.controller.admin.schoolaccommodationattendance.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("管理后台 - 全校就餐住宿考勤创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SchoolAccommodationAttendanceCreateReqVO extends SchoolAccommodationAttendanceBaseVO {

    @ApiModelProperty(value = "生成的年份")
    private Integer year;

}
