<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.electivereleaseclasses.ElectiveReleaseClassesMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectListByClassTimePeriod"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.electivereleaseclasses.ElectiveReleaseClassesDO">
        select
        eerc.id,
        eerc.release_id,
        eerc.class_id
        from edu_elective_release_classes  eerc
        left join edu_elective_release eer on eerc.release_id = eer.id
        where
        eerc.deleted = 0
        and eer.deleted = 0
        and
        (eer.class_start_time between #{classStartDateTime} and #{classEndDateTime}
        or
        eer.class_end_time  between #{classStartDateTime} and #{classEndDateTime}
        or (eer.class_start_time &lt;= #{classStartDateTime} and eer.class_end_time &gt;= #{classEndDateTime}))
        <if test="excludeId != null">
            and eer.id != #{excludeId}
        </if>
    </select>
    <select id="selectClassInfoListByReleaseId"
            resultType="com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.classInfo.ClassInfoVO">
        select
            ec.id,
            ec.class_name
        from edu_elective_release_classes  eerc
                 left join edu_class_management  ec on eerc.class_id = ec.id
        where
            eerc.deleted = 0
          and ec.deleted = 0
          and eerc.release_id = #{releaseId}
    </select>
</mapper>
