<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.unicom.cloud</groupId>
        <artifactId>unicom-module-edu</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>unicom-module-edu-biz</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>

    <dependencies>

        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <!-- 业务组件 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-banner</artifactId>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-biz-operatelog</artifactId>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-biz-dict</artifactId>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-biz-error-code</artifactId>
        </dependency>

        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-rpc</artifactId>
        </dependency>
        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-web</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-monitor</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-job</artifactId>
        </dependency>


        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-protection</artifactId>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-module-edu-api</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.15.4</version> <!-- 请使用最新的稳定版本 -->
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
