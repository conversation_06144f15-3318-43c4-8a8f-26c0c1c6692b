package com.unicom.swdx.module.edu.service.planconfig;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.unicom.swdx.framework.test.core.ut.BaseDbUnitTest;

import com.unicom.swdx.module.edu.controller.admin.planconfig.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.planconfig.PlanConfigDO;
import com.unicom.swdx.module.edu.dal.mysql.planconfig.PlanConfigMapper;
import com.unicom.swdx.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.framework.test.core.util.AssertUtils.*;
import static com.unicom.swdx.framework.test.core.util.RandomUtils.*;
import static com.unicom.swdx.framework.common.util.object.ObjectUtils.*;
import static com.unicom.swdx.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link PlanConfigServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(PlanConfigServiceImpl.class)
public class PlanConfigServiceImplTest extends BaseDbUnitTest {

    @Resource
    private PlanConfigServiceImpl planConfigService;

    @Resource
    private PlanConfigMapper planConfigMapper;

    @Test
    public void testCreatePlanConfig_success() {
        // 准备参数
        PlanConfigCreateReqVO reqVO = randomPojo(PlanConfigCreateReqVO.class);

        // 调用
        Long planConfigId = planConfigService.createPlanConfig(reqVO);
        // 断言
        assertNotNull(planConfigId);
        // 校验记录的属性是否正确
        PlanConfigDO planConfig = planConfigMapper.selectById(planConfigId);
        assertPojoEquals(reqVO, planConfig);
    }

    @Test
    public void testUpdatePlanConfig_success() {
        // mock 数据
        PlanConfigDO dbPlanConfig = randomPojo(PlanConfigDO.class);
        planConfigMapper.insert(dbPlanConfig);// @Sql: 先插入出一条存在的数据
        // 准备参数
        PlanConfigUpdateReqVO reqVO = randomPojo(PlanConfigUpdateReqVO.class, o -> {
            o.setId(dbPlanConfig.getId()); // 设置更新的 ID
        });

        // 调用
        planConfigService.updatePlanConfig(reqVO);
        // 校验是否更新正确
        PlanConfigDO planConfig = planConfigMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, planConfig);
    }

    @Test
    public void testUpdatePlanConfig_notExists() {
        // 准备参数
        PlanConfigUpdateReqVO reqVO = randomPojo(PlanConfigUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> planConfigService.updatePlanConfig(reqVO), PLAN_CONFIG_NOT_EXISTS);
    }

    @Test
    public void testDeletePlanConfig_success() {
        // mock 数据
        PlanConfigDO dbPlanConfig = randomPojo(PlanConfigDO.class);
        planConfigMapper.insert(dbPlanConfig);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlanConfig.getId();

        // 调用
        planConfigService.deletePlanConfig(id);
       // 校验数据不存在了
       assertNull(planConfigMapper.selectById(id));
    }

    @Test
    public void testDeletePlanConfig_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planConfigService.deletePlanConfig(id), PLAN_CONFIG_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanConfigPage() {
       // mock 数据
       PlanConfigDO dbPlanConfig = randomPojo(PlanConfigDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setDayOfWeek(null);
           o.setPeriod(null);
           o.setBeginTime(null);
           o.setEndTime(null);
           o.setCreateTime(null);
       });
       planConfigMapper.insert(dbPlanConfig);
       // 测试 planId 不匹配
       planConfigMapper.insert(cloneIgnoreId(dbPlanConfig, o -> o.setPlanId(null)));
       // 测试 dayOfWeek 不匹配
       planConfigMapper.insert(cloneIgnoreId(dbPlanConfig, o -> o.setDayOfWeek(null)));
       // 测试 period 不匹配
       planConfigMapper.insert(cloneIgnoreId(dbPlanConfig, o -> o.setPeriod(null)));
       // 测试 beginTime 不匹配
       planConfigMapper.insert(cloneIgnoreId(dbPlanConfig, o -> o.setBeginTime(null)));
       // 测试 endTime 不匹配
       planConfigMapper.insert(cloneIgnoreId(dbPlanConfig, o -> o.setEndTime(null)));
       // 测试 createTime 不匹配
       planConfigMapper.insert(cloneIgnoreId(dbPlanConfig, o -> o.setCreateTime(null)));
       // 准备参数
       PlanConfigPageReqVO reqVO = new PlanConfigPageReqVO();
       reqVO.setPlanId(null);
       reqVO.setDayOfWeek(null);
       reqVO.setPeriod(null);
//       reqVO.setBeginTime((new LocalDateTime[]{}));
//       reqVO.setEndTime((new LocalDateTime[]{}));
//       reqVO.setCreateTime((new LocalDateTime[]{}));

       // 调用
       PageResult<PlanConfigDO> pageResult = planConfigService.getPlanConfigPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlanConfig, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanConfigList() {
       // mock 数据
       PlanConfigDO dbPlanConfig = randomPojo(PlanConfigDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setDayOfWeek(null);
           o.setPeriod(null);
           o.setBeginTime(null);
           o.setEndTime(null);
           o.setCreateTime(null);
       });
       planConfigMapper.insert(dbPlanConfig);
       // 测试 planId 不匹配
       planConfigMapper.insert(cloneIgnoreId(dbPlanConfig, o -> o.setPlanId(null)));
       // 测试 dayOfWeek 不匹配
       planConfigMapper.insert(cloneIgnoreId(dbPlanConfig, o -> o.setDayOfWeek(null)));
       // 测试 period 不匹配
       planConfigMapper.insert(cloneIgnoreId(dbPlanConfig, o -> o.setPeriod(null)));
       // 测试 beginTime 不匹配
       planConfigMapper.insert(cloneIgnoreId(dbPlanConfig, o -> o.setBeginTime(null)));
       // 测试 endTime 不匹配
       planConfigMapper.insert(cloneIgnoreId(dbPlanConfig, o -> o.setEndTime(null)));
       // 测试 createTime 不匹配
       planConfigMapper.insert(cloneIgnoreId(dbPlanConfig, o -> o.setCreateTime(null)));
       // 准备参数
       PlanConfigExportReqVO reqVO = new PlanConfigExportReqVO();
       reqVO.setPlanId(null);
       reqVO.setDayOfWeek(null);
       reqVO.setPeriod(null);
//       reqVO.setBeginTime((new LocalDateTime[]{}));
//       reqVO.setEndTime((new LocalDateTime[]{}));
//       reqVO.setCreateTime((new LocalDateTime[]{}));

       // 调用
       List<PlanConfigDO> list = planConfigService.getPlanConfigList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlanConfig, list.get(0));
    }

}
