<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.questioncategorymanagement.QuestionCategoryManagementMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getDefaultCategory"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.questioncategorymanagement.QuestionCategoryManagementDO">
        select * from pg_question_category_management where deleted = 0 order by id limit 1
    </select>
    <select id="selectCategoryList"
            resultType="com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.QuestionRespVO">
        select * from pg_question_category_management where deleted = 0 order by id desc
    </select>
    <select id="selectDefault" resultType="java.lang.Long">
        select id from pg_question_category_management where deleted = 0 order by id limit 1
    </select>
    <select id="getBuiltInCategoryId" resultType="java.lang.Long">
        select id from pg_question_category_management where deleted = 0 and built_in = true
    </select>
</mapper>