package com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.dto;

import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesSubRespVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;

@ApiModel("包含学员选择的课程信息以及课程发布信息")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ElectiveTraineeSelectedCoursesAndReleaseDTO extends ElectiveReleaseCoursesSubRespVO {
    /**
     * 发布名称
     */
    private String releaseName;
    /**
     * 选学开始时间
     */
    private LocalDateTime selectionStartTime;
    /**
     * 选学结束时间
     */
    private LocalDateTime selectionEndTime;
    /**
     * 上课日期
     */
    private LocalDate classDate;
    /**
     * 午别时间 0-上午 1-下午 2-晚上
     */
    private Integer dayPeriod;
    /**
     * 上课时段开始时间
     */
    private LocalDateTime classStartTime;
    /**
     * 上课时段结束时间
     */
    private LocalDateTime classEndTime;
    /**
     * 发布创建时间
     */
    private LocalDateTime releaseCreateTime;
}
