package com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

@ApiModel("管理后台 - 发布课程的已选课学员信息分页 Req VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ElectiveCourseTraineeSelectedPageReqVO extends PageParam {

    @ApiModelProperty(value = "发布课程ID", required = true, example = "1")
    @NotNull(message = "发布课程ID不能为空")
    private Long releaseCourseId;

    @ApiModelProperty(value = "是否是班主任-限定该班主任班级的发布信息", example = "false-非班主任(默认) true-班主任")
    private Boolean isClassMaster;

    @ApiModelProperty(hidden = true)
    private List<Long> classIdList;

    @ApiModelProperty(value = "学员姓名", example = "张三")
    private String traineeName;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;

    @ApiModelProperty(value = "序号是否倒排(默认正排)")
    private Boolean isSerialDesc;
}
