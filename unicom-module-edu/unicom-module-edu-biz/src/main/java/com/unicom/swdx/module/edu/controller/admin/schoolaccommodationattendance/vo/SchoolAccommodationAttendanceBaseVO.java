package com.unicom.swdx.module.edu.controller.admin.schoolaccommodationattendance.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.*;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
* 全校就餐住宿考勤 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SchoolAccommodationAttendanceBaseVO {

    @ApiModelProperty(value = "早餐考勤，0-开，1-关")
    private Integer breakfast;

    @ApiModelProperty(value = "午餐考勤，0-开，1-关")
    private Integer lunch;

    @ApiModelProperty(value = "晚餐考勤，0-开，1-关")
    private Integer dinner;

    @ApiModelProperty(value = "住宿考勤，0-开，1-关")
    private Integer putUp;

    @ApiModelProperty(value = "考勤日期")
    private LocalDate clockDate;

    @ApiModelProperty(value = "是节假日，0-是，1-否")
    private Integer isHoliday;

}
