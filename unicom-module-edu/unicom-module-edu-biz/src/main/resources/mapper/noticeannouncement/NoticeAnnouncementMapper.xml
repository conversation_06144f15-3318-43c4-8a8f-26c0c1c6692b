<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.noticeannouncement.NoticeAnnouncementMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <insert id="setNoticeAnnouncementUrl">
        INSERT INTO "edu_notice_file_url" ("notice_id", "url", "file_name", "file_size")
        VALUES (#{reqVO.noticeId}, #{reqVO.url}, #{reqVO.fileName}, #{reqVO.fileSize});
    </insert>

    <update id="deleteNoticeAnnouncementUrl">
        UPDATE
            "edu_notice_file_url"
        SET
            "deleted" = '1'
        WHERE
            notice_id = #{id}
    </update>

    <select id="selectPageList" resultType="com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementDO">

        SELECT
        ena."id",
        ena."publisher",
        ena."title",
        ena."is_top",
        ena."is_publish",
        ena."status",
        ena."publish_time",
        ena."drafts_time",
        ena."class_ids"
        FROM
        edu_notice_announcement ena
        WHERE
        ena."deleted" = '0'
        <if test="pageReqVO.publishId != null">
            AND ena."publish_id" = #{pageReqVO.publishId}
        </if>
        <if test="classId != null">
            and (class_ids is null or  #{classId} = ANY(string_to_array(class_ids, ',')::text[])
            )
        </if>
        <!-- 动态查询条件 -->
        <if test="(pageReqVO.title != null and pageReqVO.title != '') or (pageReqVO.publisher != null and pageReqVO.publisher != '')">
            AND (ena."title" LIKE CONCAT('%', #{pageReqVO.title}, '%') OR
            ena."publisher" LIKE CONCAT('%', #{pageReqVO.publisher}, '%'))
        </if>
        <if test="pageReqVO.isPublish != null">
            AND ena."is_publish" = #{pageReqVO.isPublish}
        </if>
        <if test="pageReqVO.smallProgram != null and pageReqVO.smallProgram != ''">
            AND ena."status" = '1'
        </if>
        <if test="pageReqVO.endTime != null and pageReqVO.endTime != '' and pageReqVO.startTime != null and pageReqVO.startTime != ''" >
            AND  ena."publish_time" >= #{pageReqVO.startTime}
            AND  ena."publish_time" &lt;= #{pageReqVO.endTime}
        </if>
        <if test="pageReqVO.moduleCode != null">
            AND ena."module_code" = #{pageReqVO.moduleCode}
        </if>
        <if test="pageReqVO.isPublish == 2 ">
            ORDER BY
            <if test="pageReqVO.tag == 1">
                ena."title"
            </if>
            <if test="pageReqVO.tag == 2">
                ena."publish_time"
            </if>
            <if test="pageReqVO.tag == 3">
                ena."drafts_time"
            </if>
            <if test="pageReqVO.change == 0">
                ASC
            </if>
            <if test="pageReqVO.change == 1">
                DESC
            </if>
        </if>
        <if test="pageReqVO.isPublish == 1 ">
            ORDER BY
            ena."is_top" DESC,
            CASE WHEN ena."is_top" = 1 THEN ena."top_time" END DESC,
            <if test="pageReqVO.tag == 1">
                ena."title"
            </if>
            <if test="pageReqVO.tag == 2">
                ena."publish_time"
            </if>
            <if test="pageReqVO.tag == 3">
                ena."drafts_time"
            </if>
            <if test="pageReqVO.change == 0">
                ASC
            </if>
            <if test="pageReqVO.change == 1">
                DESC
            </if>
        </if>
    </select>

    <select id="selectByOneId" resultType="com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementUrlDO">
        SELECT
            ena."content",
            ena."publisher",
            ena."publish_time",
            ena."title",
            ena."is_top",
            ena."is_publish",
            ena."status",
            ena."top_time",
            ena."drafts_time",
            ena."class_ids",
            enofu."url" ,
            enofu."file_name",
            enofu."file_size"
        FROM
            edu_notice_announcement ena
                LEFT JOIN edu_notice_file_url enofu
                          ON ena ."id" = enofu ."notice_id" AND enofu."deleted" = '0'
        WHERE
            ena."deleted" = '0'
         and
            ena."id" = #{id}
    </select>

    <update id="isTopNoticeAnnouncement">
        UPDATE
            edu_notice_announcement ena
        SET
            ena."is_top" = #{isTop},
            ena."top_time" = #{localDateTime}
        WHERE
            ena."id" = #{id}
    </update>
    <update id="notIsTopNoticeAnnouncement">
        UPDATE
            edu_notice_announcement ena
        SET
            ena."is_top" = #{isTop}
        WHERE
            ena."id" = #{id}
    </update>

    <update id="isUpOrDownNoticeAnnouncement">
        UPDATE
            edu_notice_announcement ena
        SET
            ena."status" = #{status}
        WHERE
            ena."id" = #{id}
    </update>

    <update id="updatePublishById">
        UPDATE
            edu_notice_announcement ena
        SET
            "status" = '1',
            "publish_time" = #{localDateTime},
            "is_publish" = '1'
        WHERE
            ena."id" = #{id}
    </update>

    <select id="selectListInfo" resultType="com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementDO">
        SELECT
        ena."id",
        ena."publisher",
        ena."title",
        ena."content",
        ena."is_top",
        ena."is_publish",
        ena."status",
        ena."publish_time",
        ena."drafts_time"
        FROM
        edu_notice_announcement ena
        WHERE
        ena."deleted" = '0'
        <if test="pageReqVO.publishId != null">
            AND ena."publish_id" = #{pageReqVO.publishId}
        </if>
        <choose>
            <when test="pageReqVO.idList != null and pageReqVO.idList.size() > 0">
                and ena.id in
                <foreach collection="pageReqVO.idList" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                <!-- 动态查询条件 -->
                <if test="(pageReqVO.title != null and pageReqVO.title != '') or (pageReqVO.publisher != null and pageReqVO.publisher != '')">
                    AND (ena."title" LIKE CONCAT('%', #{pageReqVO.title}, '%') OR
                    ena."publisher" LIKE CONCAT('%', #{pageReqVO.publisher}, '%'))
                </if>

                <if test="pageReqVO.isPublish != null">
                    AND ena."is_publish" = #{pageReqVO.isPublish}
                </if>

                <if test="pageReqVO.endTime != null and pageReqVO.endTime != '' and pageReqVO.startTime != null and pageReqVO.startTime != ''" >
                    AND  ena."publish_time" >= #{pageReqVO.startTime}
                    AND  ena."publish_time" &lt;= #{pageReqVO.endTime}
                </if>
            </otherwise>
        </choose>
        <if test="pageReqVO.moduleCode != null">
            AND ena."module_code" = #{pageReqVO.moduleCode}
        </if>
        <if test="pageReqVO.isPublish == 2 ">
            ORDER BY
            <if test="pageReqVO.tag == 1">
                ena."title"
            </if>
            <if test="pageReqVO.tag == 2">
                ena."publish_time"
            </if>
            <if test="pageReqVO.tag == 3">
                ena."drafts_time"
            </if>
            <if test="pageReqVO.change == 0">
                ASC
            </if>
            <if test="pageReqVO.change == 1">
                DESC
            </if>
        </if>
        <if test="pageReqVO.isPublish == 1 ">
            ORDER BY
            ena."is_top" DESC,
            CASE WHEN ena."is_top" = 1 THEN ena."top_time" END DESC,
            <if test="pageReqVO.tag == 1">
                ena."title"
            </if>
            <if test="pageReqVO.tag == 2">
                ena."publish_time"
            </if>
            <if test="pageReqVO.tag == 3">
                ena."drafts_time"
            </if>
            <if test="pageReqVO.change == 0">
                ASC
            </if>
            <if test="pageReqVO.change == 1">
                DESC
            </if>
        </if>
    </select>

    <select id="selectByOneIds"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementUrlDO">
        SELECT
        ena."content",
        ena."publisher",
        ena."publish_time",
        ena."title",
        ena."is_top",
        ena."is_publish",
        ena."status",
        ena."top_time",
        ena."drafts_time",
        enofu."url" ,
        enofu."file_name",
        enofu."file_size",
        enofu."notice_id"
        FROM
        edu_notice_announcement ena
        LEFT JOIN edu_notice_file_url enofu
        ON ena ."id" = enofu ."notice_id" AND enofu."deleted" = '0'
        WHERE
        ena."deleted" = '0'
        and
        ena."id" in
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectByClassIds"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementDO">
        SELECT *
        FROM edu_notice_announcement
        WHERE EXISTS (
        SELECT 1
        FROM unnest(string_to_array(class_ids, ',')::text[]) AS class_id
        WHERE class_id = ANY(
        <foreach item="item" collection="classIds" open="ARRAY[" close="]" separator=",">
            #{item}
        </foreach>
        )
        )
    </select>
</mapper>
