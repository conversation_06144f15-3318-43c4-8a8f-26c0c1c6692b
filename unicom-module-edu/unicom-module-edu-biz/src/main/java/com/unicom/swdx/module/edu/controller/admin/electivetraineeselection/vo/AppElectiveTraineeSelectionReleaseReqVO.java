package com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

@ApiModel("班主任移动端-选修课管理 - 查看选修课发布选课详情 Request VO")
@Data
public class AppElectiveTraineeSelectionReleaseReqVO {

    @ApiModelProperty(value = "选修课发布ID", required = true, example = "1")
    @NotNull(message = "选修课发布ID不能为空")
    private Long releaseId;

    @ApiModelProperty(value = "班级ID", required = true, example = "1")
    @NotNull(message = "班级ID不能为空")
    private Long classId;

    @ApiModelProperty(value = "学员选课状态 0-未选 1-已选 2-全部(默认)", example = "0")
    @Range(min = 0, max = 2, message = "学员选课状态不存在)")
    private Integer status;
}
