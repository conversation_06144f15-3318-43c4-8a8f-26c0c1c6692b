<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.notificationmessage.NotificationMessageMapper">

    <insert id="setNotificationMessageUnit">
        INSERT INTO "edu_notification_message_unit" ("notice_id", "unit_id","is_read","display_status")
        VALUES (#{reqVO.noticeId}, #{reqVO.unitId},#{reqVO.isRead},#{reqVO.displayStatus});
    </insert>

    <update id="deleteNotificationMessageUnit">
        UPDATE
        "edu_notification_message_unit"
        SET
        "deleted" = '1'
        WHERE
        notice_id = #{id}
    </update>

    <update id="setUnitRead">
        UPDATE
        "edu_notification_message_unit"
        SET
        "is_read" = 2
        WHERE
        notice_id = #{id} and unit_id = #{unit}
    </update>

    <update id="setUnitDisplay">
        UPDATE
        "edu_notification_message_unit"
        SET
        "display_status" = 2
        WHERE
        notice_id in (#{ids}) and unit_id = #{unit}
    </update>

    <select id="getUnitsByNoticeId" resultType="java.lang.Integer">
        SELECT
        enmu.unit_id
        FROM
        edu_notification_message_unit enmu
        WHERE
        enmu."deleted" = '0'
        and
        enmu."notice_id" = #{id}
    </select>

    <select id="selectPageList" resultType="com.unicom.swdx.module.edu.dal.dataobject.notificationmessage.NotificationMessageDO">

        SELECT
        enm."id",
        enm."publisher",
        enm."title",
        enm."is_top",
        enm."is_publish",
        enm."status",
        enm."publish_time",
        enm."drafts_time"
        FROM
        edu_notification_message enm
        <if test="pageReqVO.unit != null and pageReqVO.isRead != null">
            left join edu_notification_message_unit enmu on enmu.deleted = 0 and enmu.notice_id = enm.id
        </if>
        WHERE
        enm."deleted" = '0'
        <!-- 动态查询条件 -->
        <if test="(pageReqVO.title != null and pageReqVO.title != '') or (pageReqVO.publisher != null and pageReqVO.publisher != '')">
            AND (enm."title" LIKE CONCAT('%', #{pageReqVO.title}, '%') OR
            enm."publisher" LIKE CONCAT('%', #{pageReqVO.publisher}, '%'))
        </if>
        <if test="pageReqVO.isPublish != null">
            AND enm."is_publish" = #{pageReqVO.isPublish}
        </if>
        <choose>
            <when test="pageReqVO.unit != null and pageReqVO.isRead != null">
                AND enm."status" = '1' and enmu.unit_id = #{pageReqVO.unit} and enmu.is_read = #{pageReqVO.isRead}
                <if test="pageReqVO.displayStatus != null">
                    and enmu.display_status = #{pageReqVO.displayStatus}
                </if>
            </when>
            <otherwise>
                and enm."publisher" != '系统'
            </otherwise>
        </choose>
        <if test="pageReqVO.endTime != null and pageReqVO.endTime != '' and pageReqVO.startTime != null and pageReqVO.startTime != ''" >
            AND  enm."publish_time" >= #{pageReqVO.startTime}
            AND  enm."publish_time" &lt;= #{pageReqVO.endTime}
        </if>
        <if test="pageReqVO.isPublish == 2 ">
            ORDER BY
            <if test="pageReqVO.tag == 1">
                enm."title"
            </if>
            <if test="pageReqVO.tag == 2">
                enm."publish_time"
            </if>
            <if test="pageReqVO.tag == 3">
                enm."drafts_time"
            </if>
            <if test="pageReqVO.change == 0">
                ASC
            </if>
            <if test="pageReqVO.change == 1">
                DESC
            </if>
        </if>
        <if test="pageReqVO.isPublish == 1 ">
            ORDER BY
            enm."is_top" DESC,
            CASE WHEN enm."is_top" = 1 THEN enm."top_time" END DESC,
            <if test="pageReqVO.tag == 1">
                enm."title"
            </if>
            <if test="pageReqVO.tag == 2">
                enm."publish_time"
            </if>
            <if test="pageReqVO.tag == 3">
                enm."drafts_time"
            </if>
            <if test="pageReqVO.change == 0">
                ASC
            </if>
            <if test="pageReqVO.change == 1">
                DESC
            </if>
        </if>
    </select>

    <update id="isTopNotificationMessage">
        UPDATE
        edu_notification_message enm
        SET
        enm."is_top" = #{isTop},
        enm."top_time" = #{localDateTime}
        WHERE
        enm."id" = #{id}
    </update>

    <update id="notIsTopNotificationMessage">
        UPDATE
        edu_notification_message enm
        SET
        enm."is_top" = #{isTop}
        WHERE
        enm."id" = #{id}
    </update>

    <update id="isUpOrDownNotificationMessage">
        UPDATE
        edu_notification_message enm
        SET
        enm."status" = #{status}
        WHERE
        enm."id" = #{id}
    </update>

    <update id="updatePublishById">
        UPDATE
        edu_notification_message enm
        SET
        "status" = '1',
        "publish_time" = #{localDateTime},
        "is_publish" = '1'
        WHERE
        enm."id" = #{id}
    </update>

    <select id="selectListInfo" resultType="com.unicom.swdx.module.edu.dal.dataobject.notificationmessage.NotificationMessageDO">
        SELECT
        enm."id",
        enm."publisher",
        enm."title",
        enm."content",
        enm."is_top",
        enm."is_publish",
        enm."status",
        enm."publish_time",
        enm."drafts_time"
        FROM
        edu_notification_message enm
        <if test="pageReqVO.unit != null and pageReqVO.isRead != null">
            left join edu_notification_message_unit enmu on enmu.deleted = 0 and enmu.notice_id = enm.id
        </if>
        WHERE
        enm."deleted" = '0'
        <choose>
            <when test="pageReqVO.idList != null and pageReqVO.idList.size() > 0">
                and enm.id in
                <foreach collection="pageReqVO.idList" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                <!-- 动态查询条件 -->
                <if test="(pageReqVO.title != null and pageReqVO.title != '') or (pageReqVO.publisher != null and pageReqVO.publisher != '')">
                    AND (enm."title" LIKE CONCAT('%', #{pageReqVO.title}, '%') OR
                    enm."publisher" LIKE CONCAT('%', #{pageReqVO.publisher}, '%'))
                </if>
                <if test="pageReqVO.isPublish != null">
                    AND enm."is_publish" = #{pageReqVO.isPublish}
                </if>
                <choose>
                    <when test="pageReqVO.unit != null and pageReqVO.isRead != null">
                        AND enm."status" = '1' and enmu.unit_id = #{pageReqVO.unit} and enmu.is_read = #{pageReqVO.isRead}
                    </when>
                    <otherwise>
                        and enm."publisher" != '系统'
                    </otherwise>
                </choose>
                <if test="pageReqVO.endTime != null and pageReqVO.endTime != '' and pageReqVO.startTime != null and pageReqVO.startTime != ''" >
                    AND  enm."publish_time" >= #{pageReqVO.startTime}
                    AND  enm."publish_time" &lt;= #{pageReqVO.endTime}
                </if>
            </otherwise>
        </choose>
        <if test="pageReqVO.isPublish == 2 ">
            ORDER BY
            <if test="pageReqVO.tag == 1">
                enm."title"
            </if>
            <if test="pageReqVO.tag == 2">
                enm."publish_time"
            </if>
            <if test="pageReqVO.tag == 3">
                enm."drafts_time"
            </if>
            <if test="pageReqVO.change == 0">
                ASC
            </if>
            <if test="pageReqVO.change == 1">
                DESC
            </if>
        </if>
        <if test="pageReqVO.isPublish == 1 ">
            ORDER BY
            enm."is_top" DESC,
            CASE WHEN enm."is_top" = 1 THEN enm."top_time" END DESC,
            <if test="pageReqVO.tag == 1">
                enm."title"
            </if>
            <if test="pageReqVO.tag == 2">
                enm."publish_time"
            </if>
            <if test="pageReqVO.tag == 3">
                enm."drafts_time"
            </if>
            <if test="pageReqVO.change == 0">
                ASC
            </if>
            <if test="pageReqVO.change == 1">
                DESC
            </if>
        </if>
    </select>

</mapper>