package com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("班主任移动端-选修课管理 - 发布课程的已选课学员简单信息 Resp VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppElectiveTraineeSelectionSimpleRespVO extends ElectiveTraineeSelectionBaseVO {

    @ApiModelProperty(value = "学员姓名")
    private String name;

    @ApiModelProperty(value = "学员性别")
    private String sex;

    @ApiModelProperty(value = "学员手机号")
    private String phone;

    @ApiModelProperty(value = "选修课名称")
    private String courseName;

    @ApiModelProperty(value = "选修课ID")
    private Long courseId;

    @ApiModelProperty(value = "选课时间")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime createTime;
}
