package com.unicom.swdx.module.edu.service.questionnairemanagement;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.object.BeanUtils;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.redis.util.RedisUtil;
import com.unicom.swdx.framework.security.core.LoginUser;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ClassCoursePageReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.EvaluationDetailSaveReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.EvaluationResponseSaveReqVO;
import com.unicom.swdx.module.edu.controller.admin.options.vo.OptionsRespVO;
import com.unicom.swdx.module.edu.controller.admin.options.vo.OptionsTemplateRespVO;
import com.unicom.swdx.module.edu.controller.admin.questioncategorymanagement.vo.QuestionCategoryManagementListReqVO;
import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.QuestionManagementRespVO;
import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.QuestionManagementSaveReqVO;
import com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.*;
import com.unicom.swdx.module.edu.convert.options.OptionsConvert;
import com.unicom.swdx.module.edu.convert.question.QuestionManagementConvert;
import com.unicom.swdx.module.edu.convert.questionnaire.QuestionnaireDetailConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationdetail.EvaluationDetailDO;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationresponse.EvaluationResponseDO;
import com.unicom.swdx.module.edu.dal.dataobject.options.OptionsDO;
import com.unicom.swdx.module.edu.dal.dataobject.questioncategorymanagement.QuestionCategoryManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.questionlogic.QuestionLogicDO;
import com.unicom.swdx.module.edu.dal.dataobject.questionmanagement.PgQuestionnaireHisDO;
import com.unicom.swdx.module.edu.dal.dataobject.questionmanagement.QuestionManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairedetail.QuestionnaireDetailDO;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairemanagement.QuestionnaireManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.todoitems.TodoItemsDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.evaluationdetail.EvaluationDetailMapper;
import com.unicom.swdx.module.edu.dal.mysql.evaluationresponse.EvaluationResponseMapper;
import com.unicom.swdx.module.edu.dal.mysql.options.OptionsMapper;
import com.unicom.swdx.module.edu.dal.mysql.questioncategorymanagement.QuestionCategoryManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.questionlogic.QuestionLogicMapper;
import com.unicom.swdx.module.edu.dal.mysql.questionmanagement.QuestionManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.questionnairedetail.QuestionnaireDetailMapper;
import com.unicom.swdx.module.edu.dal.mysql.questionnairemanagement.QuestionnaireManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper;
import com.unicom.swdx.module.edu.dal.mysql.todoitems.TodoItemsMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.dal.mysql.xcxmsg.PgQuestionnaireHisMapper;
import com.unicom.swdx.module.edu.enums.classmanage.ClassManageStatus;
import com.unicom.swdx.module.edu.enums.questionnaire.PgQuestionnaireHisStatusEnum;
import com.unicom.swdx.module.edu.enums.questionnaire.PgQuestionnaireHisTypeEnum;
import com.unicom.swdx.module.edu.enums.questionnaire.QuestionnaireStatusEnum;
import com.unicom.swdx.module.edu.enums.questionnaire.QuestionnaireUpdateModeEnum;
import com.unicom.swdx.module.edu.enums.teachertodoitems.TeacherTodoStatusEnum;
import com.unicom.swdx.module.edu.enums.teachertodoitems.TeacherTodoTypeEnum;
import com.unicom.swdx.module.edu.service.classcourse.ClassCourseService;
import com.unicom.swdx.module.edu.service.evaluationresponse.EvaluationResponseService;
import com.unicom.swdx.module.edu.service.questioncategorymanagement.QuestionCategoryManagementService;
import com.unicom.swdx.module.edu.service.xcxMsg.XcxMsgService;
import com.unicom.swdx.module.edu.utils.excel.ExcelCellUtils;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.*;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.edu.utils.serialnumber.PageDataSerialNumberUtil.generateSerialNumberList;

/**
 * 评估问卷管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionnaireManagementServiceImpl implements QuestionnaireManagementService {

    public static final String CONTENT = "xx老师您好，这是您带的xx班（x月x日—x月x日）的测评问卷，请您确认核对";

    @Resource
    private ClassManagementMapper classManagementMapper;

    @Resource
    private QuestionnaireManagementMapper questionnaireManagementMapper;

    @Resource
    private QuestionCategoryManagementMapper questionCategoryManagementMapper;

    @Resource
    private QuestionCategoryManagementService questionCategoryManagementService;

    @Resource
    private QuestionManagementMapper questionManagementMapper;

    @Resource
    private OptionsMapper optionsMapper;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private QuestionnaireDetailMapper questionnaireDetailMapper;

    @Resource
    private QuestionLogicMapper questionLogicMapper;

    @Resource
    private EvaluationDetailMapper evaluationDetailMapper;

    @Resource
    private TraineeMapper traineeMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private EvaluationResponseService evaluationResponseService;

    @Resource
    private XcxMsgService xcxMsgService;

    @Resource
    private EvaluationResponseMapper evaluationResponseMapper;

    @Resource
    private TeacherInformationMapper teacherInformationMapper;

    @Resource
    @Lazy
    private ClassCourseService classCourseService;


    @Override
    public Long createQuestionnaireManagement(QuestionnaireManagementSaveReqVO createReqVO) {
        // 默认问卷时查询是否已存在默认问卷
        if (createReqVO.getIsDefault().equals("1") && createReqVO.getStatus().equals("1")) {
            validateDefaultQuestionnaireExists();
        }
        // 验证该教学形式问卷是否已存在
        // validateEducateFormExists(createReqVO.getTopicEducateForm(), -1L, createReqVO.getStatus());
        // 插入问卷
        QuestionnaireManagementDO questionnaireManagement = BeanUtils.toBean(createReqVO, QuestionnaireManagementDO.class);
        questionnaireManagementMapper.insert(questionnaireManagement);
        // 新增题目
        List<QuestionManagementDO> questions = QuestionManagementConvert.Instance.convertList1(createReqVO.getQuestions());
        questions.forEach(question -> question.setQuestionnaireId(questionnaireManagement.getId()));
        questions.forEach(question -> question.setId(null));
        questions.forEach(question -> question.setIsActive(true));
        questionManagementMapper.insertBatch(questions);
        questions.forEach(questionManagement -> {
            // 单选题增加选项
            if (questionManagement.getQuestionType().equals("2") || questionManagement.getQuestionType().equals("1") || questionManagement.getQuestionType().equals("6")) {
                List<OptionsDO> options = questionManagement.getOptions();
                options.forEach(option -> option.setQuestionId(questionManagement.getId()));
                options.forEach(option -> option.setOptionsType("0"));
                optionsMapper.insertBatch(options);
            }
        });
        // 新增问卷与评估项关联关系
//        List<QuestionnaireDetailDO> questionnaireDetail = QuestionnaireDetailConvert.Instance.convertList(createReqVO.getQuestions());
//        questionnaireDetail.forEach(questionnaireDetailDO -> questionnaireDetailDO.setQuestionnaireId(questionnaireManagement.getId()));
//        questionnaireDetailMapper.insertBatch(questionnaireDetail);
        // 新增问卷的逻辑
//        List<QuestionLogicDO> questionLogic = QuestionLogicConvert.Instance.convertList(createReqVO.getQuestionLogic());
//        questionLogic.forEach(questionLogicDO -> questionLogicDO.setQuestionnaireId(questionnaireManagement.getId()));
//        questionLogicMapper.insertBatch(questionLogic);
        // 返回
        return questionnaireManagement.getId();
    }

    private void validateEducateFormExists(String topicEducateForm, Long id, String status) {
        if (status.equals("1")) {
            List<Long> educateForms = Arrays.stream(topicEducateForm.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            List<Long> existEducateForms = getCollectingEducateForm();
            // Long count = questionnaireManagementMapper.countByEducateForm(topicEducateForm);
            // 新增
            if (id.equals(-1L)) {
                if (CollectionUtil.containsAny(existEducateForms, educateForms)) {
                    throw exception(EDUCATE_FORM_QUESTIONNAIRE_EXIST);
                }
            } else { // 修改
                QuestionnaireManagementDO questionnaireManagementDO = questionnaireManagementMapper.selectById(id);

                List<Long> questionEducateForms = Arrays.stream(questionnaireManagementDO.getTopicEducateForm().split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                List<Long> newEducateForms = educateForms.stream()
                        .filter(educateForm -> !questionEducateForms.contains(educateForm))
                        .collect(Collectors.toList());
                if (!newEducateForms.isEmpty() && CollectionUtil.containsAny(existEducateForms, newEducateForms)) {
                    throw exception(EDUCATE_FORM_QUESTIONNAIRE_EXIST);
                }
            }
        }
    }

    @Override
    public void updateQuestionnaireManagement(QuestionnaireManagementSaveReqVO updateReqVO) {

        String updateMode = updateReqVO.getUpdateMode();
        QuestionnaireUpdateModeEnum updateModeEnum = QuestionnaireUpdateModeEnum.getByStatus(updateMode);
        if (Objects.isNull(updateModeEnum)) {
            throw exception(QUESTIONNAIRE_UPDATE_MODE_NOT_EXISTS);
        }

        switch (updateModeEnum) {
            case PUBLISHED_UPDATE: {
                handlePublishedUpdate(updateReqVO);
            }
            break;
            case PUBLISHED_DELETED_UPDATE: {
                handlePublishedDeletedUpdate(updateReqVO);
            }
            break;
            case SCHEDULED_UPDATE: {
                handleScheduledUpdate(updateReqVO);
            }
            break;
            case REMINDER_UPDATE: {
                handleReminderUpdate(updateReqVO);
            }
            break;
            case DRAFT_UPDATE: {
                handleDraftUpdate(updateReqVO);
            }
            break;
            default: {
                throw exception(QUESTIONNAIRE_UPDATE_MODE_NOT_SUPPORT);
            }
        }
    }

    private void handlePublishedUpdate(QuestionnaireManagementSaveReqVO updateReqVO) {
        // 校验存在
        QuestionnaireManagementDO exit = validateQuestionnaireManagementExists(updateReqVO.getId(), true);

        // 默认问卷时查询是否已存在默认问卷
        if (updateReqVO.getIsDefault().equals("1") && updateReqVO.getStatus().equals("1")) {
            validateUpdateDefaultQuestionnaire(updateReqVO.getId());
        }

        // 更新评估项信息
        List<QuestionManagementSaveReqVO> questions = updateReqVO.getQuestions();
        questions.forEach(question -> {
            if (question.getQuestionnaireId() == null) {
                question.setQuestionnaireId(updateReqVO.getId());
            }
        });
        List<QuestionManagementDO> questionManagementDOS = QuestionManagementConvert.Instance.convertList1(questions);

        questionManagementMapper.updateBatch(questionManagementDOS);
        questionManagementDOS.forEach(questionManagement -> {
            // 单选题增加选项
            if (questionManagement.getQuestionType().equals("2") || questionManagement.getQuestionType().equals("1") || questionManagement.getQuestionType().equals("6")) {
                List<OptionsDO> options = questionManagement.getOptions();
                options.forEach(option -> option.setQuestionId(questionManagement.getId()));
                options.forEach(option -> option.setOptionsType("0"));
                optionsMapper.updateBatch(options);
            }
        });

        // 更新
        QuestionnaireManagementDO updateObj = BeanUtils.toBean(updateReqVO, QuestionnaireManagementDO.class);
        questionnaireManagementMapper.updateById(updateObj);


        //如果存在对应的待办，把待办的名字更新为全新的名字
        List<TodoItemsDO> todoItemsDOList = todoItemsMapper.selectList(new LambdaQueryWrapperX<TodoItemsDO>()
                .eq(TodoItemsDO::getQuestionnaireId, updateReqVO.getId())
                .eq(TodoItemsDO::getType, TeacherTodoTypeEnum.CREATE_QUESTION.getType())
                .eq(TodoItemsDO::getStatus, TeacherTodoStatusEnum.UNDO.getStatus())
        );
        if (CollectionUtil.isNotEmpty(todoItemsDOList)) {
            todoItemsDOList.forEach(todoItemsDO -> {
                todoItemsDO.setContent(updateReqVO.getTitle());
            });
            todoItemsMapper.updateBatch(todoItemsDOList);
        }
    }

    private void handlePublishedDeletedUpdate(QuestionnaireManagementSaveReqVO updateReqVO) {
        // 校验存在
        QuestionnaireManagementDO exit = validateQuestionnaireManagementExists(updateReqVO.getId(), true);

        // 默认问卷时查询是否已存在默认问卷
        if (updateReqVO.getIsDefault().equals("1") && updateReqVO.getStatus().equals("1")) {
            validateUpdateDefaultQuestionnaire(updateReqVO.getId());
        }

        // 更新评估项信息
        List<QuestionManagementSaveReqVO> questions = updateReqVO.getQuestions();
        questions.forEach(question -> {
            if (question.getQuestionnaireId() == null) {
                question.setQuestionnaireId(updateReqVO.getId());
            }
        });
        List<QuestionManagementDO> questionManagementDOS = QuestionManagementConvert.Instance.convertList1(questions);

        questionManagementMapper.updateBatch(questionManagementDOS);
        questionManagementDOS.forEach(questionManagement -> {
            // 单选题增加选项
            if (questionManagement.getQuestionType().equals("2") || questionManagement.getQuestionType().equals("1") || questionManagement.getQuestionType().equals("6")) {
                List<OptionsDO> options = questionManagement.getOptions();
                options.forEach(option -> option.setQuestionId(questionManagement.getId()));
                options.forEach(option -> option.setOptionsType("0"));
                optionsMapper.updateBatch(options);
            }
        });
        evaluationResponseMapper.delete(EvaluationResponseDO::getQuestionnaireId, updateReqVO.getId());
        evaluationDetailMapper.delete(EvaluationDetailDO::getQuestionnaireId, updateReqVO.getId());

        // 更新
        QuestionnaireManagementDO updateObj = BeanUtils.toBean(updateReqVO, QuestionnaireManagementDO.class);
        questionnaireManagementMapper.updateById(updateObj);
        // 删除已答的问卷以后重新下发问卷

        this.publishQuestionnaireManagement(new QuestionnairePublishVO()
                .setQuestionnaireId(updateObj.getId())
                .setPublishType("0")
                .setPublishScale(updateReqVO.getPublishScale())
                .setClassCourse(updateReqVO.getClassCourse())
                .setCourseStartTime(exit.getCourseStartTime())
                .setCourseEndTime(exit.getCourseEndTime()));

        //如果存在对应的待办，把待办的名字更新为全新的名字
        List<TodoItemsDO> todoItemsDOList = todoItemsMapper.selectList(new LambdaQueryWrapperX<TodoItemsDO>()
                .eq(TodoItemsDO::getQuestionnaireId, updateReqVO.getId())
                .eq(TodoItemsDO::getType, TeacherTodoTypeEnum.CONFIRM_QUESTION.getType())
                .eq(TodoItemsDO::getStatus, TeacherTodoStatusEnum.UNDO.getStatus())
        );
        if (CollectionUtil.isNotEmpty(todoItemsDOList)) {
            todoItemsDOList.forEach(todoItemsDO -> {
                todoItemsDO.setContent(updateReqVO.getTitle());
            });
            todoItemsMapper.updateBatch(todoItemsDOList);
        }
    }

    private void handleScheduledUpdate(QuestionnaireManagementSaveReqVO updateReqVO) {
        // 校验存在
        QuestionnaireManagementDO exit = validateQuestionnaireManagementExists(updateReqVO.getId(), true);

        // 默认问卷时查询是否已存在默认问卷
        if (updateReqVO.getIsDefault().equals("1") && updateReqVO.getStatus().equals("1")) {
            validateUpdateDefaultQuestionnaire(updateReqVO.getId());
        }

        // 更新评估项信息
        List<QuestionManagementSaveReqVO> questions = updateReqVO.getQuestions();
        questions.forEach(question -> {
            if (question.getQuestionnaireId() == null) {
                question.setQuestionnaireId(updateReqVO.getId());
            }
        });
        List<QuestionManagementDO> questionManagementDOS = QuestionManagementConvert.Instance.convertList1(questions);

        LambdaUpdateWrapper<QuestionManagementDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(QuestionManagementDO::getIsActive, false);
        wrapper.eq(QuestionManagementDO::getQuestionnaireId, updateReqVO.getId());
        questionManagementMapper.update(wrapper);

        questionManagementDOS.forEach(questionManagementDO -> {
            questionManagementDO.setIsActive(true);
        });
        questionManagementMapper.insertBatch(questionManagementDOS);
        questionManagementDOS.forEach(questionManagement -> {
            // 单选题增加选项
            if (questionManagement.getQuestionType().equals("2") || questionManagement.getQuestionType().equals("1") || questionManagement.getQuestionType().equals("6")) {
                List<OptionsDO> options = questionManagement.getOptions();
                options.forEach(option -> option.setQuestionId(questionManagement.getId()));
                options.forEach(option -> option.setOptionsType("0"));
                optionsMapper.insertBatch(options);
            }
        });

        //清理相关的缓存
        List<EvaluationResponseDO> evaluationResponses = evaluationResponseMapper.selectList(
                new LambdaQueryWrapper<EvaluationResponseDO>()
                        .eq(EvaluationResponseDO::getQuestionnaireId, updateReqVO.getId())
        );

        if (CollectionUtil.isNotEmpty(evaluationResponses)) {
            List<Long> studentIds = evaluationResponses.stream()
                    .map(EvaluationResponseDO::getStudentId)
                    .filter(Objects::nonNull) // 过滤 null 值
                    .collect(Collectors.toList());

            studentIds.forEach(studentId -> {
                String cacheKey = "unhandled:" + studentId;
                redisUtil.del(cacheKey);
            });
        }

        QuestionnaireManagementDO updateObj = BeanUtils.toBean(updateReqVO, QuestionnaireManagementDO.class);
        questionnaireManagementMapper.updateById(updateObj);

        //如果是问卷处于定时待发布时完成更新 则将对应的定时任务继续
        List<PgQuestionnaireHisDO> pgQuestionnaireHisDOList = pgQuestionnaireHisMapper.selectList(new LambdaQueryWrapperX<PgQuestionnaireHisDO>()
                .eq(PgQuestionnaireHisDO::getQuestionnaireId, updateReqVO.getId())
                .eq(PgQuestionnaireHisDO::getStatus, PgQuestionnaireHisStatusEnum.PAUSE.getStatus())
                .eq(PgQuestionnaireHisDO::getType, PgQuestionnaireHisTypeEnum.PUBLISH_QUESTIONNAIRE.getStatus()));
        if (CollectionUtil.isNotEmpty(pgQuestionnaireHisDOList)) {
            pgQuestionnaireHisMapper.updateById(pgQuestionnaireHisDOList.get(0).setStatus(PgQuestionnaireHisStatusEnum.UNDO.getStatus()));
        }

        //如果存在对应的待办，把待办的名字更新为全新的名字
        List<TodoItemsDO> todoItemsDOList = todoItemsMapper.selectList(new LambdaQueryWrapperX<TodoItemsDO>()
                .eq(TodoItemsDO::getQuestionnaireId, updateReqVO.getId())
                .eq(TodoItemsDO::getType, TeacherTodoTypeEnum.CONFIRM_QUESTION.getType())
                .eq(TodoItemsDO::getStatus, TeacherTodoStatusEnum.UNDO.getStatus())
        );
        if (CollectionUtil.isNotEmpty(todoItemsDOList)) {
            todoItemsDOList.forEach(todoItemsDO -> {
                todoItemsDO.setContent(updateReqVO.getTitle());
            });
            todoItemsMapper.updateBatch(todoItemsDOList);
        }
    }

    private void handleReminderUpdate(QuestionnaireManagementSaveReqVO updateReqVO) {
        // 校验存在
        QuestionnaireManagementDO exit = validateQuestionnaireManagementExists(updateReqVO.getId(), true);

        // 默认问卷时查询是否已存在默认问卷
        if (updateReqVO.getIsDefault().equals("1") && updateReqVO.getStatus().equals("1")) {
            validateUpdateDefaultQuestionnaire(updateReqVO.getId());
        }

        // 更新评估项信息
        List<QuestionManagementSaveReqVO> questions = updateReqVO.getQuestions();
        questions.forEach(question -> {
            if (question.getQuestionnaireId() == null) {
                question.setQuestionnaireId(updateReqVO.getId());
            }
        });
        List<QuestionManagementDO> questionManagementDOS = QuestionManagementConvert.Instance.convertList1(questions);

        LambdaUpdateWrapper<QuestionManagementDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(QuestionManagementDO::getIsActive, false);
        wrapper.eq(QuestionManagementDO::getQuestionnaireId, updateReqVO.getId());
        questionManagementMapper.update(wrapper);

        questionManagementDOS.forEach(questionManagementDO -> {
            questionManagementDO.setIsActive(true);
        });
        questionManagementMapper.insertBatch(questionManagementDOS);
        questionManagementDOS.forEach(questionManagement -> {
            // 单选题增加选项
            if (questionManagement.getQuestionType().equals("2") || questionManagement.getQuestionType().equals("1") || questionManagement.getQuestionType().equals("6")) {
                List<OptionsDO> options = questionManagement.getOptions();
                options.forEach(option -> option.setQuestionId(questionManagement.getId()));
                options.forEach(option -> option.setOptionsType("0"));
                optionsMapper.insertBatch(options);
            }
        });

        //清理相关的缓存
        List<EvaluationResponseDO> evaluationResponses = evaluationResponseMapper.selectList(
                new LambdaQueryWrapper<EvaluationResponseDO>()
                        .eq(EvaluationResponseDO::getQuestionnaireId, updateReqVO.getId())
        );

        if (CollectionUtil.isNotEmpty(evaluationResponses)) {
            List<Long> studentIds = evaluationResponses.stream()
                    .map(EvaluationResponseDO::getStudentId)
                    .filter(Objects::nonNull) // 过滤 null 值
                    .collect(Collectors.toList());

            studentIds.forEach(studentId -> {
                String cacheKey = "unhandled:" + studentId;
                redisUtil.del(cacheKey);
            });
        }

        QuestionnaireManagementDO updateObj = BeanUtils.toBean(updateReqVO, QuestionnaireManagementDO.class);
        questionnaireManagementMapper.updateById(updateObj);

        //如果存在对应的待办，把待办的名字更新为全新的名字
        List<TodoItemsDO> todoItemsDOList = todoItemsMapper.selectList(new LambdaQueryWrapperX<TodoItemsDO>()
                .eq(TodoItemsDO::getQuestionnaireId, updateReqVO.getId())
                .eq(TodoItemsDO::getType, TeacherTodoTypeEnum.CONFIRM_QUESTION.getType())
                        .eq(TodoItemsDO::getStatus, TeacherTodoStatusEnum.UNDO.getStatus())
                );
        if (CollectionUtil.isNotEmpty(todoItemsDOList)) {
            todoItemsDOList.forEach(todoItemsDO -> {
                todoItemsDO.setContent(updateReqVO.getTitle());
            });
            todoItemsMapper.updateBatch(todoItemsDOList);
        }

    }

    private void handleDraftUpdate(QuestionnaireManagementSaveReqVO updateReqVO) {
        // 校验存在
        QuestionnaireManagementDO exit = validateQuestionnaireManagementExists(updateReqVO.getId(), true);

        // 默认问卷时查询是否已存在默认问卷
        if (updateReqVO.getIsDefault().equals("1") && updateReqVO.getStatus().equals("1")) {
            validateUpdateDefaultQuestionnaire(updateReqVO.getId());
        }

        // 更新评估项信息
        List<QuestionManagementSaveReqVO> questions = updateReqVO.getQuestions();
        questions.forEach(question -> {
            if (question.getQuestionnaireId() == null) {
                question.setQuestionnaireId(updateReqVO.getId());
            }
        });
        List<QuestionManagementDO> questionManagementDOS = QuestionManagementConvert.Instance.convertList1(questions);

        LambdaUpdateWrapper<QuestionManagementDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(QuestionManagementDO::getIsActive, false);
        wrapper.eq(QuestionManagementDO::getQuestionnaireId, updateReqVO.getId());
        questionManagementMapper.update(wrapper);

        questionManagementDOS.forEach(questionManagementDO -> {
            questionManagementDO.setIsActive(true);
        });
        questionManagementMapper.insertBatch(questionManagementDOS);
        questionManagementDOS.forEach(questionManagement -> {
            // 单选题增加选项
            if (questionManagement.getQuestionType().equals("2") || questionManagement.getQuestionType().equals("1") || questionManagement.getQuestionType().equals("6")) {
                List<OptionsDO> options = questionManagement.getOptions();
                options.forEach(option -> option.setQuestionId(questionManagement.getId()));
                options.forEach(option -> option.setOptionsType("0"));
                optionsMapper.insertBatch(options);
            }
        });

        //清理相关的缓存
        List<EvaluationResponseDO> evaluationResponses = evaluationResponseMapper.selectList(
                new LambdaQueryWrapper<EvaluationResponseDO>()
                        .eq(EvaluationResponseDO::getQuestionnaireId, updateReqVO.getId())
        );

        if (CollectionUtil.isNotEmpty(evaluationResponses)) {
            List<Long> studentIds = evaluationResponses.stream()
                    .map(EvaluationResponseDO::getStudentId)
                    .filter(Objects::nonNull) // 过滤 null 值
                    .collect(Collectors.toList());

            studentIds.forEach(studentId -> {
                String cacheKey = "unhandled:" + studentId;
                redisUtil.del(cacheKey);
            });
        }

        QuestionnaireManagementDO updateObj = BeanUtils.toBean(updateReqVO, QuestionnaireManagementDO.class);
        questionnaireManagementMapper.updateById(updateObj);
    }

    @Override
    public void deleteQuestionnaireManagement(Long id) {
        // 校验存在
        validateQuestionnaireManagementExists(id, false);
        // 校验能否删除
        LambdaUpdateWrapper<EvaluationResponseDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(EvaluationResponseDO::getDeleted, false);
        wrapper.eq(EvaluationResponseDO::getQuestionnaireId, id);
        Long count = evaluationResponseMapper.selectCount(wrapper);
        if (count > 0) {
            throw exception(QUESTIONNAIRE_MANAGEMENT_PUBLISHED);
        }
        // 删除
        questionnaireManagementMapper.deleteById(id);
        // 删除对应的逻辑
        questionLogicMapper.deleteByQuestionnaireId(id);
        // 删除对应的评估项信息
        questionnaireDetailMapper.deleteByQuestionnaireId(id);
    }

    @Override
    public void batchDeleteQuestionnaireManagement(List<Long> ids) {
        // 校验能否删除
        validateQuestionnaireDeletable(ids);
        ids.forEach(id -> {
            LambdaUpdateWrapper<EvaluationResponseDO> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(EvaluationResponseDO::getDeleted, false);
            wrapper.eq(EvaluationResponseDO::getQuestionnaireId, id);
            Long count = evaluationResponseMapper.selectCount(wrapper);
            if (count > 0) {
                throw exception(QUESTIONNAIRE_MANAGEMENT_PUBLISHED);
            }
        });

        // 批量删除
        questionnaireManagementMapper.deleteBatchIds(ids);
        // 删除对应的逻辑
        questionLogicMapper.batchDeleteByQuestionnaireId(ids);
        // 删除对应的评估项信息
        questionnaireDetailMapper.batchDeleteByQuestionnaireId(ids);
    }

    private QuestionnaireManagementDO validateQuestionnaireManagementExists(Long id, Boolean isUpdate) {
        QuestionnaireManagementDO questionnaireManagementDO = questionnaireManagementMapper.selectById(id);
        if (questionnaireManagementDO == null) {
            throw exception(QUESTIONNAIRE_MANAGEMENT_NOT_EXISTS);
        }
        if (!isUpdate && questionnaireManagementDO.getStatus().equals("1")) {
            throw exception(QUESTIONNAIRE_MANAGEMENT_PUBLISHED);
        }
        return questionnaireManagementDO;
    }

    private void validateDefaultQuestionnaireExists() {
        Integer count = questionnaireManagementMapper.countDefault(getTenantId());
        if (count != 0) {
            throw exception(DEFAULT_QUESTIONNAIRE_EXIST);
        }
    }

    private void validateUpdateDefaultQuestionnaire(Long id) {
        QuestionnaireManagementDO questionnaireManagementDO = questionnaireManagementMapper.selectById(id);
        if (!(questionnaireManagementDO.getIsDefault().equals("1") && questionnaireManagementDO.getStatus().equals("1"))) {
            Integer count = questionnaireManagementMapper.countDefault(getTenantId());
            if (count != 0) {
                throw exception(DEFAULT_QUESTIONNAIRE_EXIST);
            }
        }
    }


    private void validateQuestionnaireDeletable(List<Long> ids) {
        List<QuestionnaireManagementDO> questionnaireManagementDOS = questionnaireManagementMapper.selectBatchIds(ids);
        if (questionnaireManagementDOS.size() != ids.size()) {
            throw exception(QUESTIONNAIRE_MANAGEMENT_NOT_EXISTS);
        }
        Set<String> collect = questionnaireManagementDOS.stream().map(QuestionnaireManagementDO::getStatus).collect(Collectors.toSet());
        if (collect.contains("1")) {
            throw exception(QUESTIONNAIRE_MANAGEMENT_PUBLISHED);
        }
    }

    @Override
    public QuestionnaireManagementRespVO getQuestionnaireManagement(Long id, Boolean isEvaluate) {
        QuestionnaireManagementDO questionnaireManagement = questionnaireManagementMapper.selectById(id);
        QuestionnaireManagementRespVO questionnaireManagementRespVO = QuestionnaireDetailConvert.Instance.convertDO(questionnaireManagement);
        // 通过问卷id获取问题详情
        LambdaQueryWrapper<QuestionManagementDO> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(QuestionManagementDO::getQuestionnaireId, id);
        wrapper.eq(QuestionManagementDO::getIsActive, true);
        List<QuestionManagementDO> questions = questionManagementMapper.selectList(wrapper);
        if (isEvaluate) {
            questions = questions.stream().filter(question -> !Objects.equals(question.getQuestionType(), "6")).collect(Collectors.toList());
        } else {
            questions = questions.stream().filter(question -> !Objects.equals(question.getQuestionType(), "7")).collect(Collectors.toList());
        }
//        List<QuestionnaireDetailDO> questionnaireDetails = questionnaireDetailMapper.selectByQuestionnaireId(id);
        if (questions != null && questions.size() != 0) {
            List<Long> questionIds = questions.stream().map(QuestionManagementDO::getId).collect(Collectors.toList());
//            List<QuestionManagementDO> questionManagementDOS = questionManagementMapper.selectList(QuestionManagementDO::getId, questionIds);
            List<QuestionManagementRespVO> questionManagementRespVO = QuestionManagementConvert.Instance.convertList(questions);
            // 单选题获取选项
            List<OptionsDO> options = optionsMapper.selectListsByQuestionIds(questionIds);
            // 通过问卷id获取逻辑详情
//            List<QuestionLogicDO> questionLogicDOS = questionLogicMapper.selectListByQuestionnaireId(id);
            questionManagementRespVO.forEach(questionManagement -> {
                // 是否一票否决、是否必选、一票否决选项
//                List<QuestionnaireDetailDO> questionnaireDetail = questionnaireDetails.stream()
//                        .filter(questionnaireDetailDO -> questionnaireDetailDO.getQuestionId().equals(questionManagement.getId()))
//                        .collect(Collectors.toList());
//                questionManagement.setDetailId(questionnaireDetail.get(0).getId());
//                questionManagement.setSerialNumber(questionnaireDetail.get(0).getSerialNumber());
//                questionManagement.setRequired(questionnaireDetail.get(0).getRequired());
                if (questionManagement.getQuestionType().equals("2") || questionManagement.getQuestionType().equals("1") || questionManagement.getQuestionType().equals("6") || questionManagement.getQuestionType().equals("7")) {
//                    questionManagement.setOneBallotVeto(questionnaireDetail.get(0).getOneBallotVeto());
//                    if (questionnaireDetail.get(0).getOptionId() != null) {
//                        questionManagement.setOptionId(questionnaireDetail.get(0).getOptionId());
//                    }
//                    // 选择题的选项
                    List<OptionsDO> optionsDOS = options.stream()
                            .filter(optionsDO -> optionsDO.getQuestionId().equals(questionManagement.getId()))
                            .collect(Collectors.toList());
                    List<OptionsTemplateRespVO> optionsTemplateRespVOS = OptionsConvert.Instance.convertList1(optionsDOS);
                    questionManagement.setOptions(optionsTemplateRespVOS);
                }
//                // 逻辑关联
//                List<QuestionLogicDO> questionLogic = questionLogicDOS.stream()
//                        .filter(questionLogicDO -> questionLogicDO.getQuestionId().equals(questionManagement.getId()))
//                        .collect(Collectors.toList());
//                // 被另一个问题关联
//                List<QuestionLogicDO> logicQuestion = questionLogicDOS.stream()
//                        .filter(questionLogicDO -> questionLogicDO.getLogicQuestionId().equals(questionManagement.getId()))
//                        .collect(Collectors.toList());
//                questionManagement.setQuestionLogic(QuestionLogicConvert.Instance.convertList01(questionLogic));
//
//                questionManagement.setLogicQuestion(QuestionLogicConvert.Instance.convertList01(logicQuestion));
            });
            questionManagementRespVO = questionManagementRespVO.stream().sorted(Comparator.comparing(QuestionManagementRespVO::getSerialNumber)).collect(Collectors.toList());
            questionnaireManagementRespVO.setQuestions(questionManagementRespVO);
        }
        return questionnaireManagementRespVO;
    }


    @Override
    public QuestionnaireManagementRespVO getQuestionnaireManagementWithClassQuestion(Long id, Boolean isEvaluate) {
        QuestionnaireManagementDO questionnaireManagement = questionnaireManagementMapper.selectById(id);
        QuestionnaireManagementRespVO questionnaireManagementRespVO = QuestionnaireDetailConvert.Instance.convertDO(questionnaireManagement);
        // 通过问卷id获取问题详情
        LambdaQueryWrapper<QuestionManagementDO> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(QuestionManagementDO::getQuestionnaireId, id);
        wrapper.eq(QuestionManagementDO::getIsActive, true);
        List<QuestionManagementDO> questions = questionManagementMapper.selectList(wrapper);
        List<QuestionManagementDO> classQuestions = new ArrayList<>();
        if (isEvaluate) {
            //todo 这里之后应该改成 准备发布了 但是还没到下发时间的状态
            if (Objects.nonNull(questionnaireManagement)
                    && (QuestionnaireStatusEnum.REMINDED.getStatus().equals(questionnaireManagement.getStatus()) || QuestionnaireStatusEnum.SCHEDULED_TO_COLLECT.getStatus().equals(questionnaireManagement.getStatus()))
                    && questions.stream().map(QuestionManagementDO::getQuestionType).anyMatch("6"::equals)) {
                if (Objects.nonNull(questionnaireManagement.getPublishScale())) {


                    List<ClassCourseDO> classCourse = this.getClassCourseByPublishScaleAndCourseTimeRange(questionnaireManagement.getPublishScale(), questionnaireManagement.getCourseStartTime(), questionnaireManagement.getCourseEndTime());
                    List<QuestionManagementDO> courseQuestionTemplate = questions.stream().filter(question -> Objects.equals(question.getQuestionType(), "6")).collect(Collectors.toList());
                    List<OptionsDO> templateOptions = optionsMapper.selectListByQuestionId(courseQuestionTemplate.get(0).getId());
                    courseQuestionTemplate.get(0).setOptions(templateOptions);

                    classCourse.forEach(classCourseDO -> {

                        QuestionManagementDO q = new QuestionManagementDO()
                                .setStem(classCourseDO.getCourseName() + "(" + classCourseDO.getTeacherName() + ")")
                                .setQuestionType("7")
                                .setIsActive(true)
                                .setSerialNumber(99L)
                                .setSuggestionTag(courseQuestionTemplate.get(0).getSuggestionTag())
                                .setQuestionnaireId(id)
                                .setRequired(true)
                                .setId(UUID.randomUUID().getMostSignificantBits() & Long.MAX_VALUE);
                        q.setTenantId(classCourseDO.getTenantId());
                        // 为空不显示教师名字
                        if (StringUtils.isEmpty(classCourseDO.getTeacherName())) {
                            q.setStem(classCourseDO.getCourseName());
                        }
                        classQuestions.add(q);
                    });

                    classQuestions.forEach(questionManagement -> {
                        // 增加选项
                        List<OptionsDO> options = courseQuestionTemplate.get(0).getOptions();
                        options.forEach(option -> option.setId(null));
                        options.forEach(option -> option.setQuestionId(questionManagement.getId()));
                        options.forEach(option -> option.setOptionsType("0"));
                        options.forEach(option -> option.setTenantId(questionManagement.getTenantId()));
                        questionManagement.setOptions(options);

                    });
                }
            }

            questions = questions.stream().filter(question -> !Objects.equals(question.getQuestionType(), "6")).collect(Collectors.toList());


        } else {
            questions = questions.stream().filter(question -> !Objects.equals(question.getQuestionType(), "7")).collect(Collectors.toList());
        }
        if (questions != null && questions.size() != 0) {
            List<Long> questionIds = questions.stream().map(QuestionManagementDO::getId).collect(Collectors.toList());
            List<QuestionManagementRespVO> questionManagementRespVO = QuestionManagementConvert.Instance.convertList(questions);
            // 单选题获取选项
            List<OptionsDO> options = optionsMapper.selectListsByQuestionIds(questionIds);
            questionManagementRespVO.forEach(questionManagement -> {
                if (questionManagement.getQuestionType().equals("2") || questionManagement.getQuestionType().equals("1") || questionManagement.getQuestionType().equals("6") || questionManagement.getQuestionType().equals("7")) {
//                    // 选择题的选项
                    List<OptionsDO> optionsDOS = options.stream()
                            .filter(optionsDO -> optionsDO.getQuestionId().equals(questionManagement.getId()))
                            .collect(Collectors.toList());
                    List<OptionsTemplateRespVO> optionsTemplateRespVOS = OptionsConvert.Instance.convertList1(optionsDOS);
                    questionManagement.setOptions(optionsTemplateRespVOS);
                }
            });
            questionManagementRespVO = questionManagementRespVO.stream().sorted(Comparator.comparing(QuestionManagementRespVO::getSerialNumber)).collect(Collectors.toList());
            questionnaireManagementRespVO.setQuestions(questionManagementRespVO);
        }
        if (CollectionUtil.isNotEmpty(classQuestions)) {
            List<QuestionManagementRespVO> questionManagementRespVO = QuestionManagementConvert.Instance.convertList(classQuestions);
            questionManagementRespVO.forEach(questionManagement -> {
                List<OptionsDO> options = classQuestions.get(0).getOptions();
                List<OptionsTemplateRespVO> optionsTemplateRespVOS = OptionsConvert.Instance.convertList1(options);
                questionManagement.setOptions(optionsTemplateRespVOS);
            });
            if (CollectionUtil.isNotEmpty(questionnaireManagementRespVO.getQuestions())) {
                questionnaireManagementRespVO.getQuestions().addAll(questionManagementRespVO);
            } else {
                questionnaireManagementRespVO.setQuestions(questionManagementRespVO);
            }
        }
        return questionnaireManagementRespVO;
    }


    @Override
    public QuestionnaireManagementRespVO getRatedQuestionnaireManagement(Long questionnaireId, Long userId, Long classCourseId) {
        QuestionnaireManagementRespVO result = getQuestionnaireManagement(questionnaireId, true);
        if (userId == null) {
            userId = getLoginUserId();
        }
        TraineeDO traineeDO = traineeMapper.selectByUserId(userId);
        List<EvaluationDetailDO> ratedDetails = evaluationDetailMapper.selectRatedQuestionnaireDetail(questionnaireId, traineeDO.getId(), classCourseId);
        List<QuestionManagementRespVO> questions = result.getQuestions();
        questions.forEach(question -> {
            Optional<EvaluationDetailDO> details = ratedDetails.stream()
                    .filter(ratedDetail -> ratedDetail.getQuestionId().equals(question.getId()))
                    .findFirst();
            if (details.isPresent()) {
                EvaluationDetailDO detail = details.get();

                if (detail.getContent() != null) {
                    question.setRateContent(detail.getContent());
                }
                if (detail.getScore() != null) {
                    question.setRateScore(detail.getScore().toString());
                }
                if (detail.getOptionId() != null) {
                    question.setRateOptionId(detail.getOptionId().toString());
                }
                question.setEvaluationDetailId(detail.getId());
            }
        });
        result.setQuestions(questions);
        return result;
    }

    @Override
    public PageResult<QuestionnaireManagementRespVO> getQuestionnaireManagementPage(QuestionnaireManagementPageReqVO pageReqVO) {
        IPage<QuestionnaireManagementRespVO> page = MyBatisUtils.buildPage(pageReqVO);
        Long count = questionnaireManagementMapper.countBuiltId();
        // 没有内置问卷就新建一个
        if (count == 0) {
            QuestionnaireManagementDO questionnaireManagement = new QuestionnaireManagementDO()
                    .setBuiltIn(true)
                    .setIsDefault("0")
                    .setStatus("0")
                    .setTitle("讲授式课堂教学评价表")
                    .setTopicEducateForm("1632")
                    .setLowscore(70)
                    .setLowscoreTag(true)
                    .setLowword(0)
                    .setLowwordTag(true)
                    .setTimeTag(true)
                    .setTimeLimit(3);
            questionnaireManagementMapper.insert(questionnaireManagement);
            Long builtInCategoryId = questionCategoryManagementMapper.getBuiltInCategoryId();
            if (builtInCategoryId == null) {
                List<QuestionCategoryManagementDO> questionCategoryManagementList = questionCategoryManagementService.getQuestionCategoryManagementList(new QuestionCategoryManagementListReqVO().setBuiltIn(true));
                builtInCategoryId = questionCategoryManagementList.get(0).getId();
            }
            List<Long> questionIds = questionManagementMapper.selectBuiltInQuestionId(builtInCategoryId);
            List<Long> optionIds = optionsMapper.selectOptionId(questionIds.get(1));
            QuestionnaireDetailDO questionnaireDetail0 = new QuestionnaireDetailDO()
                    .setQuestionnaireId(questionnaireManagement.getId())
                    .setQuestionId(questionIds.get(1))
                    .setOneBallotVeto(true)
                    .setRequired(true)
                    .setSerialNumber(0L)
                    .setOptionId(optionIds.get(1));
            questionnaireDetailMapper.insert(questionnaireDetail0);
            QuestionnaireDetailDO questionnaireDetail1 = new QuestionnaireDetailDO()
                    .setQuestionnaireId(questionnaireManagement.getId())
                    .setQuestionId(questionIds.get(0))
                    .setOneBallotVeto(false)
                    .setRequired(false)
                    .setSerialNumber(1L);
            questionnaireDetailMapper.insert(questionnaireDetail1);
            QuestionnaireDetailDO questionnaireDetail2 = new QuestionnaireDetailDO()
                    .setQuestionnaireId(questionnaireManagement.getId())
                    .setQuestionId(questionIds.get(4))
                    .setOneBallotVeto(false)
                    .setRequired(true)
                    .setSerialNumber(2L);
            questionnaireDetailMapper.insert(questionnaireDetail2);
            QuestionnaireDetailDO questionnaireDetail3 = new QuestionnaireDetailDO()
                    .setQuestionnaireId(questionnaireManagement.getId())
                    .setQuestionId(questionIds.get(3))
                    .setOneBallotVeto(false)
                    .setRequired(false)
                    .setSerialNumber(3L);
            questionnaireDetailMapper.insert(questionnaireDetail3);
            QuestionnaireDetailDO questionnaireDetail4 = new QuestionnaireDetailDO()
                    .setQuestionnaireId(questionnaireManagement.getId())
                    .setQuestionId(questionIds.get(2))
                    .setOneBallotVeto(false)
                    .setRequired(false)
                    .setSerialNumber(4L);
            questionnaireDetailMapper.insert(questionnaireDetail4);
            // 新增问卷的逻辑
            QuestionLogicDO questionLogic = new QuestionLogicDO()
                    .setQuestionId(questionIds.get(1))
                    .setLogicQuestionId(questionIds.get(0))
                    .setQuestionnaireId(questionnaireManagement.getId())
                    .setScore(0)
                    .setOption(optionIds.get(1));
            questionLogicMapper.insert(questionLogic);
        }
        Long loginUserId = getLoginUserId();
        List<QuestionnaireManagementRespVO> pageResult = questionnaireManagementMapper.selectPageByPageVO(page, pageReqVO, loginUserId);
//        pageResult = pageResult.stream().filter(result -> {
//            boolean isTemplate = Boolean.TRUE.equals(result.getIsTemplate());
//            boolean isTypeZero = Objects.equals(result.getTemplateType(), 0);
//            boolean isCreatorMatch = Objects.equals(result.getCreator(), loginUserId);
//
//            // 排除条件：isTemplate && isTypeZero && 非 isCreatorMatch
//            return !(isTemplate && isTypeZero && !isCreatorMatch);
//        }).collect(Collectors.toList());
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(null,
                page.getTotal(),
                pageReqVO,
                pageResult.size());
        for (int i = 0; i < pageResult.size(); i++) {
            pageResult.get(i).setSerialNumber(serialNumberList.get(i));
        }
        if (pageReqVO.getIsSerialDesc() == null || pageReqVO.getIsSerialDesc() == false) {
            pageResult = pageResult.parallelStream().sorted(
                    Comparator.comparing(QuestionnaireManagementRespVO::getSerialNumber)
            ).collect(Collectors.toList());
        } else {
            pageResult = pageResult.parallelStream().sorted(
                    Comparator.comparing(QuestionnaireManagementRespVO::getSerialNumber).reversed()
            ).collect(Collectors.toList());
        }
        pageResult.forEach(result -> {
            result.setCreatorName(adminUserApi.getUser(result.getCreator())
                    .getCheckedData().getNickname());
        });
        return new PageResult<>(pageResult, page.getTotal());
    }

    @Override
    public List<QuestionRespVO> getQuestionList(QuestionListReqVO listReqVO) {
        List<QuestionRespVO> categories = questionCategoryManagementMapper.selectCategoryList();
        List<QuestionRespVO> questions = questionManagementMapper.selectQuestionList();
        List<Long> choices = questions.stream()
                .filter(questionRespVO -> questionRespVO.getQuestionType().equals("2"))
                .map(QuestionRespVO::getQuestionId)
                .collect(Collectors.toList());
        List<OptionsDO> optionsDOS = optionsMapper.selectListsByQuestionIds(choices);
        questions.forEach(questionRespVO -> {
            if (questionRespVO.getQuestionType().equals("2")) {
                List<OptionsDO> options = optionsDOS.stream()
                        .filter(optionsDO -> optionsDO.getQuestionId().equals(questionRespVO.getQuestionId()))
                        .collect(Collectors.toList());
                questionRespVO.setOptions(options);
            }
        });
        categories.forEach(category -> category.setType(0L));
        questions.forEach(question -> question.setType(1L));
        List<QuestionRespVO> result = new ArrayList<>();
        result.addAll(categories);
        result.addAll(questions);
        return result;
    }

    @Override
    public List<QuestionnaireManagementRespVO> getTemplateQuestionnaire(Integer templateType, Long questionnaireId) {
        if (templateType == null) {
            templateType = 0;
        }
        Long loginUserId = getLoginUserId();
        List<QuestionnaireManagementRespVO> templates = questionnaireManagementMapper.getTemplate(templateType, loginUserId);
        templates.forEach(template -> {
            LambdaQueryWrapper<QuestionManagementDO> wrapper = new LambdaQueryWrapper<>();

            wrapper.eq(QuestionManagementDO::getQuestionnaireId, template.getId());
            wrapper.eq(QuestionManagementDO::getIsActive, true);
            List<QuestionManagementDO> questionManagementDOS = questionManagementMapper.selectList(wrapper);
            List<Long> questionIds = questionManagementDOS.stream().map(QuestionManagementDO::getId).collect(Collectors.toList());
            List<QuestionManagementRespVO> questionManagementRespVO = QuestionManagementConvert.Instance.convertList(questionManagementDOS);
            List<OptionsDO> options = optionsMapper.selectListsByQuestionIds(questionIds);
            questionManagementRespVO.forEach(questionManagement -> {
                if (questionManagement.getQuestionType().equals("2") || questionManagement.getQuestionType().equals("1") || questionManagement.getQuestionType().equals("6")) {
                    // 选择题的选项
                    List<OptionsDO> optionsDOS = options.stream()
                            .filter(optionsDO -> optionsDO.getQuestionId().equals(questionManagement.getId()))
                            .collect(Collectors.toList());
                    List<OptionsTemplateRespVO> optionsTemplateRespVOS = OptionsConvert.Instance.convertList1(optionsDOS);
                    questionManagement.setOptions(optionsTemplateRespVOS);
                }
            });
            template.setQuestions(questionManagementRespVO);
        });
        if (questionnaireId != null) {
            templates = templates.stream().filter(template -> !Objects.equals(template.getId(), questionnaireId)).collect(Collectors.toList());
            ;
        }
        return templates;
    }

    @Override
    public Long getByEducateForm(Long educateFormId, Long tenantId) {
        return questionnaireManagementMapper.getByEducateForm(educateFormId, tenantId);
    }

    @Override
    public Long getDefaultQuestionnaireId(Long tenantId) {
        return questionnaireManagementMapper.getDefaultQuestionnaire(tenantId);
    }

    @Override
    public List<Long> getCollectingEducateForm() {
        Long tenantId = getTenantId();
        List<String> collectingEducateForm = questionnaireManagementMapper.getCollectingEducateForm(tenantId);
        List<Long> educateForm = new ArrayList<>();
        collectingEducateForm.forEach(collecting -> {
            List<Long> collect = Arrays.stream(collecting.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            educateForm.addAll(collect);
        });
        return educateForm;
    }

    @Resource
    PgQuestionnaireHisMapper pgQuestionnaireHisMapper;


    private void checkQuestionnaireBeforePublish(Long questionnaireId) {
        QuestionnaireManagementDO questionnaireManagementDO = questionnaireManagementMapper.selectById(questionnaireId);
        if(Objects.isNull(questionnaireManagementDO)){
            throw exception(QUESTIONNAIRE_MANAGEMENT_NOT_EXISTS);
        }
        if(QuestionnaireStatusEnum.SCHEDULED_TO_COLLECT.getStatus().equals(questionnaireManagementDO.getStatus())){
            throw exception(QUESTIONNAIRE_ALREADY_SCHEDULED);
        }
    }

    @Override
    public void publishQuestionnaireManagement(QuestionnairePublishVO publishVO) {
        LoginUser loginUser = getLoginUser();

        Long questionnaireId = publishVO.getQuestionnaireId();

        //发布前检查问卷状态
        checkQuestionnaireBeforePublish(questionnaireId);

        if (Boolean.TRUE.equals(publishVO.getIsRepublish())) {
            LambdaUpdateWrapper<EvaluationResponseDO> wrapper = new LambdaUpdateWrapper<>();
            List<ClassCourseDO> classCourse = publishVO.getClassCourse();
            List<String> stems = new ArrayList<>();
            classCourse.forEach(classCourseDO -> {
                String stem = classCourseDO.getCourseName() + "(" + classCourseDO.getTeacherName() + ")";
                stems.add(stem);
            });

            if (CollectionUtil.isNotEmpty(stems)) {
                wrapper.set(EvaluationResponseDO::getIsCutoff, false)
                        .eq(EvaluationResponseDO::getQuestionnaireId, questionnaireId);
                evaluationResponseMapper.update(wrapper);
                questionnaireManagementMapper.updateById(new QuestionnaireManagementDO()
                        .setId(questionnaireId)
                        .setStatus("1"));
                LambdaUpdateWrapper<QuestionManagementDO> questionWrapper = new LambdaUpdateWrapper<>();
                questionWrapper.set(QuestionManagementDO::getIsActive, true)
                        .eq(QuestionManagementDO::getQuestionnaireId, questionnaireId)
                        .in(QuestionManagementDO::getStem, stems)
                        .eq(QuestionManagementDO::getQuestionType, "7");
                questionManagementMapper.update(questionWrapper);
            }

        } else {
            // 直接发布
            if (Objects.equals(publishVO.getPublishType(), "0")) {

                publishQuestionnaireManagementNow(publishVO, loginUser.getId(), loginUser.getTenantId());

                //插入确认待办
                LambdaQueryWrapperX<TodoItemsDO> lambdaQueryWrapperX = new LambdaQueryWrapperX();
                lambdaQueryWrapperX.eq(TodoItemsDO::getQuestionnaireId, publishVO.getQuestionnaireId());
                lambdaQueryWrapperX.eq(TodoItemsDO::getClassId, publishVO.getPublishScale());
                lambdaQueryWrapperX.eq(TodoItemsDO::getType, TeacherTodoTypeEnum.CONFIRM_QUESTION.getType());


                List<TodoItemsDO> todoItemsDOList = todoItemsMapper.selectList(lambdaQueryWrapperX);

                if (CollectionUtil.isEmpty(todoItemsDOList)) {
                    QuestionnaireManagementDO questionnaireManagementDO = questionnaireManagementMapper.selectById(publishVO.getQuestionnaireId());


                    TodoItemsDO itemsDO = new TodoItemsDO();
                    itemsDO.setClassId(publishVO.getPublishScale());
                    itemsDO.setStatus(TeacherTodoStatusEnum.UNDO.getStatus());
                    itemsDO.setType(TeacherTodoTypeEnum.CONFIRM_QUESTION.getType());
                    itemsDO.setQuestionnaireId(publishVO.getQuestionnaireId());
                    itemsDO.setTenantId(questionnaireManagementDO.getTenantId());
                    itemsDO.setContent(questionnaireManagementDO.getTitle());
                    teacherTodoItemsMapper.insert(itemsDO);
                }


            } else if (Objects.equals(publishVO.getPublishType(), "1")) {


                List<PgQuestionnaireHisDO> hisDOList = pgQuestionnaireHisMapper.selectList(new LambdaQueryWrapperX<PgQuestionnaireHisDO>().eq(PgQuestionnaireHisDO::getQuestionnaireId, questionnaireId));


                if (CollectionUtil.isNotEmpty(hisDOList)) {

                    PgQuestionnaireHisDO pgQuestionnaireHisDO = hisDOList.get(0);

                    if (pgQuestionnaireHisDO.getStatus() == 1) {
                        throw new ServiceException(500, "当前问卷已经发布，不能再次发布");
                    } else {

                        LocalDateTime now = publishVO.getPublishTime();
                        LocalDateTime firstExecutionTime = pgQuestionnaireHisDO.getSendTime();

                        pgQuestionnaireHisDO.setSendTime(now);

                        pgQuestionnaireHisMapper.updateById(pgQuestionnaireHisDO);

                    }


                } else {

                    //定时发布 参数存到表里表
                    PgQuestionnaireHisDO pgQuestionnaireHisDO = PgQuestionnaireHisDO.builder()
                            .operatorId(loginUser.getId())
                            .sendTime(publishVO.getPublishTime())
                            .infor(JSONUtil.toJsonStr(publishVO))
                            .tenantId(loginUser.getTenantId())
                            .status(0)
                            .questionnaireId(questionnaireId)
                            .type(0).build();

                    pgQuestionnaireHisMapper.insert(pgQuestionnaireHisDO);

                    //修改对应的问卷的状态为定时待收集
                    questionnaireManagementMapper.updateById(new QuestionnaireManagementDO().setId(questionnaireId).setStatus(QuestionnaireStatusEnum.SCHEDULED_TO_COLLECT.getStatus()));

                    //插入确认待办
                    LambdaQueryWrapperX<TodoItemsDO> lambdaQueryWrapperX = new LambdaQueryWrapperX();
                    lambdaQueryWrapperX.eq(TodoItemsDO::getQuestionnaireId, publishVO.getQuestionnaireId());
                    lambdaQueryWrapperX.eq(TodoItemsDO::getClassId, publishVO.getPublishScale());
                    lambdaQueryWrapperX.eq(TodoItemsDO::getType, TeacherTodoTypeEnum.CONFIRM_QUESTION.getType());


                    List<TodoItemsDO> todoItemsDOList = todoItemsMapper.selectList(lambdaQueryWrapperX);

                    if (CollectionUtil.isEmpty(todoItemsDOList)) {
                        QuestionnaireManagementDO questionnaireManagementDO = questionnaireManagementMapper.selectById(publishVO.getQuestionnaireId());


                        TodoItemsDO itemsDO = new TodoItemsDO();
                        itemsDO.setClassId(publishVO.getPublishScale());
                        itemsDO.setStatus(TeacherTodoStatusEnum.UNDO.getStatus());
                        itemsDO.setType(TeacherTodoTypeEnum.CONFIRM_QUESTION.getType());
                        itemsDO.setQuestionnaireId(publishVO.getQuestionnaireId());
                        itemsDO.setTenantId(questionnaireManagementDO.getTenantId());
                        itemsDO.setContent(questionnaireManagementDO.getTitle());
                        teacherTodoItemsMapper.insert(itemsDO);
                    }
                }

            }


        }


        try {

            //已经发布了 不需要发布问卷的待办了

            LambdaQueryWrapperX<TodoItemsDO> lambdaQueryWrapperX = new LambdaQueryWrapperX();
            lambdaQueryWrapperX.eq(TodoItemsDO::getQuestionnaireId, publishVO.getQuestionnaireId());
            lambdaQueryWrapperX.eq(TodoItemsDO::getClassId, publishVO.getPublishScale());
            lambdaQueryWrapperX.eq(TodoItemsDO::getTenantId, loginUser.getTenantId());
            lambdaQueryWrapperX.eq(TodoItemsDO::getType, TeacherTodoTypeEnum.CREATE_QUESTION.getType());

            List<TodoItemsDO> todoItemsDOList = todoItemsMapper.selectList(lambdaQueryWrapperX);

            if (CollectionUtil.isNotEmpty(todoItemsDOList)) {

                todoItemsDOList.forEach(it -> {

                    it.setStatus(1);
                    todoItemsMapper.updateById(it);

                });

            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        //清理相关的缓存
        List<EvaluationResponseDO> evaluationResponses = evaluationResponseMapper.selectList(
                new LambdaQueryWrapper<EvaluationResponseDO>()
                        .eq(EvaluationResponseDO::getQuestionnaireId, publishVO.getQuestionnaireId())
        );

        if (CollectionUtil.isNotEmpty(evaluationResponses)) {
            List<Long> studentIds = evaluationResponses.stream()
                    .map(EvaluationResponseDO::getStudentId)
                    .filter(Objects::nonNull) // 过滤 null 值
                    .collect(Collectors.toList());

            studentIds.forEach(studentId -> {
                String cacheKey = "unhandled:" + studentId;
                redisUtil.del(cacheKey);
            });
        }


    }

    public List<ClassCourseDO> getClassCourseByPublishScaleAndCourseTimeRange(Long publishScale, LocalDateTime courseStartTime, LocalDateTime courseEndTime) {
        ClassCoursePageReqVO classCoursePageReqVO = new ClassCoursePageReqVO().setClassId(publishScale);
        classCoursePageReqVO.setPageSize(9999);
        classCoursePageReqVO.setPageNo(1);
        if(Objects.nonNull(courseStartTime) && Objects.nonNull(courseEndTime)){
            classCoursePageReqVO.setBetweenTime(new LocalDateTime[]{courseStartTime, courseEndTime});
        }
        PageResult<ClassCourseDO> classCourseDOPageResult = classCourseService.getClassCoursePage(classCoursePageReqVO);
        if (Objects.isNull(classCourseDOPageResult)) {
            return new ArrayList<>();
        }
        return classCourseDOPageResult.getList();
    }

    public void publishQuestionnaireManagementNow(QuestionnairePublishVO publishVO, Long userid, Long tennantId) {

        LocalDateTime now = LocalDateTime.now();
        Long classId = publishVO.getPublishScale();
        Long questionnaireId = publishVO.getQuestionnaireId();

        QuestionnaireManagementRespVO questionnaire = this.getQuestionnaireManagement(questionnaireId, false);
        List<QuestionManagementRespVO> questions = questionnaire.getQuestions();
        if (questions != null) {
            List<QuestionManagementRespVO> courseQuestionTemplate = questions.stream().filter(question -> Objects.equals(question.getQuestionType(), "6")).collect(Collectors.toList());
            List<TraineeDO> trainee = null;
            if (classId != -1L) {
                trainee = traineeMapper.getAllTraineeByClassIds(Collections.singletonList(classId));
                if (!courseQuestionTemplate.isEmpty()) {
                    //这里要改成通过班级id,开始时间，结束时间来获取课程列表
                    List<ClassCourseDO> classCourse = this.getClassCourseByPublishScaleAndCourseTimeRange(publishVO.getPublishScale(), publishVO.getCourseStartTime(), publishVO.getCourseEndTime());
                    List<QuestionManagementDO> classQuestions = new ArrayList<>();
                    if (classCourse != null) {

                        classCourse.forEach(classCourseDO -> {

                            QuestionManagementDO q = new QuestionManagementDO()
                                    .setStem(classCourseDO.getCourseName() + "(" + classCourseDO.getTeacherName() + ")")
                                    .setQuestionType("7")
                                    .setIsActive(true)
                                    .setSerialNumber(99L)
                                    .setRequired(true)
                                    .setSuggestionTag(courseQuestionTemplate.get(0).getSuggestionTag())
                                    .setQuestionnaireId(publishVO.getQuestionnaireId());
                            q.setTenantId(classCourseDO.getTenantId());
                            // 为空不显示教师名字
                            if (StringUtils.isEmpty(classCourseDO.getTeacherName())) {
                                q.setStem(classCourseDO.getCourseName());
                            }
                            classQuestions.add(q);
                        });

                        questionManagementMapper.insertBatch(classQuestions);
                        List<OptionsDO> allOptions = new ArrayList<>();
                        classQuestions.forEach(questionManagement -> {
                            // 增加选项
                            List<OptionsDO> options = OptionsConvert.Instance.convertList2(courseQuestionTemplate.get(0).getOptions());
                            options.forEach(option -> option.setId(null));
                            options.forEach(option -> option.setQuestionId(questionManagement.getId()));
                            options.forEach(option -> option.setOptionsType("0"));
                            options.forEach(option -> option.setTenantId(questionManagement.getTenantId()));
                            allOptions.addAll(options);
                        });
                        optionsMapper.insertBatch(allOptions);
                        questions = questions.stream().filter(question -> !Objects.equals(question.getQuestionType(), "6")).collect(Collectors.toList());
                        questions.addAll(QuestionManagementConvert.Instance.convertList(classQuestions));
                    }


                }

            } else {
                LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(TraineeDO::getDeleted, false);
                wrapper.eq(TraineeDO::getStatus, 798);
                trainee = traineeMapper.selectList(wrapper);
            }
            if (!trainee.isEmpty()) {
                List<QuestionManagementRespVO> finalQuestions = questions;
                trainee.forEach(traineeDO -> {
                    EvaluationResponseSaveReqVO responseSaveReqVO = new EvaluationResponseSaveReqVO();
                    responseSaveReqVO.setQuestionnaireId(questionnaire.getId());
                    responseSaveReqVO.setStudentId(traineeDO.getId());
                    responseSaveReqVO.setIssuer(String.valueOf(userid));
                    responseSaveReqVO.setHandle(false);
                    responseSaveReqVO.setTenantId(tennantId);
                    responseSaveReqVO.setPublishScale(classId);

                    // 下发问卷
                    responseSaveReqVO.setId(evaluationResponseService.createEvaluationResponse(responseSaveReqVO));

                    try {
                        List<EvaluationDetailSaveReqVO> detailSaveReqVOList = new ArrayList<>();
                        finalQuestions.forEach(question -> {
                            EvaluationDetailSaveReqVO detailSaveReqVO = new EvaluationDetailSaveReqVO();
                            detailSaveReqVO.setQuestionnaireId(questionnaire.getId());
                            detailSaveReqVO.setQuestionId(question.getId());
                            detailSaveReqVO.setQuestionType(question.getQuestionType());
                            detailSaveReqVO.setStudentId(traineeDO.getId());
                            detailSaveReqVO.setTenantId(tennantId);
                            detailSaveReqVO.setPublishScale(classId);
                            detailSaveReqVO.setResponseId(responseSaveReqVO.getId());
                            detailSaveReqVOList.add(detailSaveReqVO);


                        });
                        // 记录每个学员的选项
                        this.batchCreateEvaluationDetail(detailSaveReqVOList);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                });
            }
            // 发布完成后设置问卷状态为发布收集中
            questionnaireManagementMapper.updateById(new QuestionnaireManagementDO()
                    .setId(questionnaireId)
                    .setPublishTime(now)
                    .setPublisher(userid)
                    .setPublishScale(publishVO.getPublishScale())
                    .setStatus("1"));
            ////或许要去掉这个逻辑
            //if (publishVO.getClassCourse() != null && !publishVO.getClassCourse().isEmpty()) {
            //
            //}
        } else {
            throw new NullPointerException("问卷不存在题目");
        }
    }

    @Override
    public void exportEvaluationExcel() {
        // 1. 创建工作簿
        Workbook workbook = new XSSFWorkbook();

        // 2. 创建工作表
        Sheet sheet = workbook.createSheet("XX班评估汇总表");

        // 3. 设置列宽
        sheet.setColumnWidth(0, 5000);  // A列
        sheet.setColumnWidth(1, 3000);  // B列
        sheet.setColumnWidth(2, 3000);  // C列
        sheet.setColumnWidth(3, 3000);  // D列
        sheet.setColumnWidth(4, 3000);  // E列
        sheet.setColumnWidth(5, 3000);  // F列
        sheet.setColumnWidth(6, 3000);  // G列
        sheet.setColumnWidth(7, 3000);  // H列

        // 4. 创建样式
        CellStyle headerStyle = ExcelCellUtils.createHeaderStyle(workbook);
        CellStyle dataStyle = ExcelCellUtils.createDataStyle(workbook);
        CellStyle titleStyle = ExcelCellUtils.createTitleStyle(workbook);
        CellStyle mergedStyle = ExcelCellUtils.createMergedHeaderStyle(workbook);

        // 5. 生成表格内容
        int rowNum = 0;
        // 标题行
        Row titleRow = sheet.createRow(rowNum++);
        ExcelCellUtils.createMergedCell(sheet, titleRow, 0, 9, "XX班评估汇总表", titleStyle);

        // 5.1 第一行标题信息
        Row infoRow = sheet.createRow(rowNum++);
        ExcelCellUtils.createCell(infoRow, 0, "培训时间", headerStyle);
        ExcelCellUtils.createCell(infoRow, 1, "4.6-4.12", dataStyle);
        ExcelCellUtils.createCell(infoRow, 2, "培训人数", headerStyle);
        ExcelCellUtils.createCell(infoRow, 3, "117", dataStyle);
        ExcelCellUtils.createCell(infoRow, 4, "参评人数", headerStyle);
        ExcelCellUtils.createCell(infoRow, 5, "109", dataStyle);
        ExcelCellUtils.createCell(infoRow, 6, "教务老师", headerStyle);
        ExcelCellUtils.createCell(infoRow, 7, "刘成豪", dataStyle);
        ExcelCellUtils.createCell(infoRow, 8, "带班老师", headerStyle);
        ExcelCellUtils.createCell(infoRow, 9, "黄昌、黄俊、黄昊博", dataStyle);

        // 5.4 表头行
        Row headerRow = sheet.createRow(rowNum++);
        ExcelCellUtils.createMergedCellBorder(sheet, headerRow, 0, 1, "评估项目", headerStyle);

        // 5.5 类别行
        Row categoryRow = sheet.createRow(rowNum++);
        ExcelCellUtils.createCell(categoryRow, 0, "类别", headerStyle);
        ExcelCellUtils.createCell(categoryRow, 1, "评估内容", headerStyle);

        String[] headers = {"测评人数", "满意人数", "一般人数", "不满意人数", "满意", "一般", "不满意", "整体满意率"};

        for (int i = 0; i < headers.length; i++) {

            ExcelCellUtils.mergeVertically(sheet, 2 + i, rowNum - 2, rowNum - 1, headers[i], mergedStyle);

        }

        rowNum++;

        ExcelCellUtils.mergeCells(sheet, rowNum, rowNum + 1, 0, 1, "课程", mergedStyle);

        for (int i = 0; i < headers.length; i++) {

            ExcelCellUtils.mergeVertically(sheet, 2 + i, rowNum, rowNum + 1, headers[i], mergedStyle);

        }

        String savePath = "D:\\XX班评估汇总表.xlsx"; // 指定路径

        // 写入文件
        try {
            Files.createDirectories(Paths.get(savePath).getParent()); // 确保目录存在
            try (FileOutputStream outputStream = new FileOutputStream(savePath)) {
                workbook.write(outputStream);
                System.out.println("文件已保存至: " + savePath);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @Override
    public void cutoffQuestionnaire(Long questionnaireId) {
        LambdaUpdateWrapper<EvaluationResponseDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(EvaluationResponseDO::getIsCutoff, true)
                .eq(EvaluationResponseDO::getQuestionnaireId, questionnaireId)
                .eq(EvaluationResponseDO::getHandle, 0);
        evaluationResponseMapper.update(wrapper);
        questionnaireManagementMapper.updateById(new QuestionnaireManagementDO()
                .setId(questionnaireId)
                .setStatus("2"));
//        LambdaUpdateWrapper<QuestionManagementDO> questionWrapper = new LambdaUpdateWrapper<>();
//        questionWrapper.set(QuestionManagementDO::getIsActive, false)
//                .eq(QuestionManagementDO::getQuestionnaireId,questionnaireId)
//                .eq(QuestionManagementDO::getQuestionType, "7");
//        questionManagementMapper.update(questionWrapper);
    }

    private Long createEvaluationDetail(EvaluationDetailSaveReqVO createReqVO) {
        // 插入
        EvaluationDetailDO evaluationDetail = BeanUtils.toBean(createReqVO, EvaluationDetailDO.class);
        evaluationDetailMapper.insert(evaluationDetail);


//        String cacheKey = "unhandled:" + createReqVO.getStudentId();
//        redisUtil.del(cacheKey);


        // 返回
        return evaluationDetail.getId();
    }

    private Long batchCreateEvaluationDetail(List<EvaluationDetailSaveReqVO> createReqVOList) {
        // 插入
        if (CollectionUtil.isNotEmpty(createReqVOList)) {
            List<EvaluationDetailDO> evaluationDetailDOList = BeanUtils.toBean(createReqVOList, EvaluationDetailDO.class);
            evaluationDetailMapper.insertBatch(evaluationDetailDOList);
        }
//        String cacheKey = "unhandled:" + createReqVO.getStudentId();
//        redisUtil.del(cacheKey);
        // 返回
        return 1L;
    }


    @Resource
    private TodoItemsMapper teacherTodoItemsMapper;

    @Resource
    private TodoItemsMapper todoItemsMapper;

    @Override
    public void questionnaireRemind(QuestionnaireRemindVO remindVO) {

        //给班主任搞个 代办实现， 然后发送 一个小程序通知（立刻 ，定时）

//        xcxMsgService.sendBatchXcxMsg(new XcxMsgSendReqVO(
//                remindVO.getStudentIdList(),
//                XcxMsgSendType.LEAVE_REPORT.getCode(),
//                null, null,
//                "id_"+remindVO.getQuestionnaireId().toString()));
//        return true;


        LoginUser loginUser = getLoginUser();

        QuestionnairePublishVO publishVO = QuestionnairePublishVO.builder()
                .questionnaireId(remindVO.getQuestionnaireId())
                .publishType(remindVO.getRemindType())
                .publishTime(remindVO.getRemindTime())
                .publishScale(remindVO.getRemindScale())
                .courseStartTime(remindVO.getCourseStartTime())
                .courseEndTime(remindVO.getCourseEndTime())
                .build();


        if (Objects.equals(remindVO.getRemindType(), "0")) {


            LambdaQueryWrapperX<TodoItemsDO> lambdaQueryWrapperX = new LambdaQueryWrapperX();
            lambdaQueryWrapperX.eq(TodoItemsDO::getQuestionnaireId, publishVO.getQuestionnaireId());
            lambdaQueryWrapperX.eq(TodoItemsDO::getClassId, publishVO.getPublishScale());
            lambdaQueryWrapperX.eq(TodoItemsDO::getType, TeacherTodoTypeEnum.CREATE_QUESTION.getType());


            List<TodoItemsDO> todoItemsDOList = todoItemsMapper.selectList(lambdaQueryWrapperX);

            if (CollectionUtil.isEmpty(todoItemsDOList)) {
                QuestionnaireManagementDO questionnaireManagementDO = questionnaireManagementMapper.selectById(remindVO.getQuestionnaireId());


                TodoItemsDO itemsDO = new TodoItemsDO();
                itemsDO.setClassId(remindVO.getRemindScale());
                itemsDO.setStatus(TeacherTodoStatusEnum.UNDO.getStatus());
                itemsDO.setType(TeacherTodoTypeEnum.CREATE_QUESTION.getType());
                itemsDO.setQuestionnaireId(remindVO.getQuestionnaireId());
                itemsDO.setTenantId(questionnaireManagementDO.getTenantId());
                itemsDO.setContent(questionnaireManagementDO.getTitle());
                teacherTodoItemsMapper.insert(itemsDO);
            }

            //把对应的问卷改为已提醒
            questionnaireManagementMapper.updateById(new QuestionnaireManagementDO().setStatus(QuestionnaireStatusEnum.REMINDED.getStatus()).setId(remindVO.getQuestionnaireId()).setPublishScale(publishVO.getPublishScale()).setCourseStartTime(publishVO.getCourseStartTime()).setCourseEndTime(publishVO.getCourseEndTime()));


            //publishQuestionnaireManagementNow(publishVO , loginUser.getId() , loginUser.getTenantId());


        } else if (Objects.equals(remindVO.getRemindType(), "1")) {

            //判断是否已经有过提醒了，有则改，无则加
            List<PgQuestionnaireHisDO> hisDOList = pgQuestionnaireHisMapper.selectList(new LambdaQueryWrapperX<PgQuestionnaireHisDO>().eq(PgQuestionnaireHisDO::getQuestionnaireId, remindVO.getQuestionnaireId()));


            if (CollectionUtil.isNotEmpty(hisDOList)) {

                PgQuestionnaireHisDO pgQuestionnaireHisDO = hisDOList.get(0);

                if (pgQuestionnaireHisDO.getStatus() == 1) {
                    throw new ServiceException(500, "当前问卷已经发布，不能再次发布");
                } else {

                    LocalDateTime now = publishVO.getPublishTime();
                    LocalDateTime firstExecutionTime = pgQuestionnaireHisDO.getSendTime();

                    pgQuestionnaireHisDO.setSendTime(now);

                    pgQuestionnaireHisMapper.updateById(pgQuestionnaireHisDO);

                }


            } else {

                //定时发布 参数存到表里表
                PgQuestionnaireHisDO pgQuestionnaireHisDO = PgQuestionnaireHisDO.builder()
                        .operatorId(loginUser.getId())
                        .sendTime(publishVO.getPublishTime())
                        .infor(JSONUtil.toJsonStr(publishVO))
                        .tenantId(loginUser.getTenantId())
                        .status(0)
                        .questionnaireId(remindVO.getQuestionnaireId())
                        .type(1).build();

                pgQuestionnaireHisMapper.insert(pgQuestionnaireHisDO);

            }


        }


    }

    @Override
    public PageResult<QuestionnaireStatsRespVO> getQuestionnaireStatsPage(QuestionnaireStatsPageReqVO pageReqVO) {
        IPage<QuestionnaireStatsRespVO> page = MyBatisUtils.buildPage(pageReqVO);
        List<QuestionnaireStatsRespVO> pageResult = questionnaireManagementMapper.selectStatPageByPageVO(page, pageReqVO);

        Set<Long> classIds = pageResult.stream().map(QuestionnaireStatsRespVO::getPublishScale).filter(Objects::nonNull).map(Long::parseLong).collect(Collectors.toSet());
        List<ClassManagementDO> classManagementDOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(classIds)) {
            classManagementDOList = classManagementMapper.selectBatchIds(classIds);
        }
        //映射成id为key的map
        Map<Long, ClassManagementDO> classManagementDOMap = classManagementDOList.stream().collect(Collectors.toMap(ClassManagementDO::getId, v -> v));

        //我要知道每个问卷是否包含6，7的问卷
        Set<Long> questionnaireIds = pageResult.stream().map(QuestionnaireStatsRespVO::getId).collect(Collectors.toSet());
        List<QuestionManagementDO> classQuestionManagementDOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(questionnaireIds)) {
            classQuestionManagementDOList = questionManagementMapper.selectList(new LambdaQueryWrapperX<QuestionManagementDO>().in(QuestionManagementDO::getQuestionType, "6", "7").in(QuestionManagementDO::getQuestionnaireId, questionnaireIds).eq(QuestionManagementDO::getIsActive, true));
        }
        Map<Long, List<QuestionManagementDO>> questionManagementDOMap = classQuestionManagementDOList.stream().collect(Collectors.groupingBy(QuestionManagementDO::getQuestionnaireId));

        pageResult.forEach(result -> {
            if (!Objects.equals(result.getStatus(), "0")) {
                result.setDistributedCount(evaluationResponseMapper.countTotal(result.getId()));
                result.setEvaluatedCount(evaluationResponseMapper.countHandled(result.getId()));
                result.setUnevaluatedCount(result.getDistributedCount() - result.getEvaluatedCount());
            } else {
                // 修改默认为 0
                result.setDistributedCount(0);
                result.setEvaluatedCount(0);
                result.setUnevaluatedCount(0);
            }
            if (result.getPublisher() != null) {
                AdminUserRespDTO user = adminUserApi.getUser(result.getPublisher()).getCheckedData();
                result.setPublisherName(user.getNickname());
            }
            if (result.getPublishScale() != null) {
                if (result.getPublishScale().equals("-1")) {
                    result.setPublishScaleName("所有在校");
                } else {

                    ClassManagementDO targetClass = classManagementDOMap.get(Long.parseLong(result.getPublishScale()));
                    if (Objects.nonNull(targetClass)) {
                        result.setPublishScaleName(targetClass.getClassName());
                        //找到班主任的名字填上
                        if (Objects.nonNull(targetClass.getClassTeacherLead())) {
                            TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectById(targetClass.getClassTeacherLead());
                            if (Objects.nonNull(teacherInformationDO)) {
                                result.setClassTeacherLeadName(teacherInformationDO.getName());
                            }
                        }
                        //再把开班结业时间填上
                        result.setClassOpenTime(targetClass.getClassOpenTime());
                        result.setCompletionTime(targetClass.getCompletionTime());
                    }
                }


            }
            result.setContainCourseQuestion(CollectionUtil.isNotEmpty(questionManagementDOMap.get(result.getId())));


        });
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(null,
                page.getTotal(),
                pageReqVO,
                pageResult.size());
        for (int i = 0; i < pageResult.size(); i++) {
            pageResult.get(i).setSerialNumber(serialNumberList.get(i));
        }
        if (pageReqVO.getIsSerialDesc() == null || pageReqVO.getIsSerialDesc() == false) {
            pageResult = pageResult.parallelStream().sorted(
                    Comparator.comparing(QuestionnaireStatsRespVO::getSerialNumber)
            ).collect(Collectors.toList());
        } else {
            pageResult = pageResult.parallelStream().sorted(
                    Comparator.comparing(QuestionnaireStatsRespVO::getSerialNumber).reversed()
            ).collect(Collectors.toList());
        }
        return new PageResult<>(pageResult, page.getTotal());
    }

    @Override
    public PageResult<QuestionnaireStatsByTeacherRespVO> getQuestionnaireStatsPageByTeacher(QuestionnaireStatsPageByTeacherReqVO pageReqVO) {
        IPage<QuestionnaireStatsByTeacherRespVO> page = MyBatisUtils.buildPage(pageReqVO);


        List<QuestionnaireStatsByTeacherRespVO> pageResult = questionnaireManagementMapper.selectStatPageByPageVOByTeacher(page, pageReqVO);

        Set<Long> classIds = pageResult.stream().map(QuestionnaireStatsByTeacherRespVO::getPublishScale).filter(Objects::nonNull).map(Long::parseLong).collect(Collectors.toSet());
        List<ClassManagementDO> classManagementDOList2 = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(classIds)) {
            classManagementDOList2 = classManagementMapper.selectBatchIds(classIds);
        }
        //映射成id为key的map
        Map<Long, ClassManagementDO> classManagementDOMap = classManagementDOList2.stream().collect(Collectors.toMap(ClassManagementDO::getId, v -> v));

        //我要知道每个问卷是否包含6，7的问卷
        Set<Long> questionnaireIds = pageResult.stream().map(QuestionnaireStatsByTeacherRespVO::getId).collect(Collectors.toSet());
        List<QuestionManagementDO> classQuestionManagementDOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(questionnaireIds)) {
            classQuestionManagementDOList = questionManagementMapper.selectList(new LambdaQueryWrapperX<QuestionManagementDO>().in(QuestionManagementDO::getQuestionType, "6", "7").in(QuestionManagementDO::getQuestionnaireId, questionnaireIds).eq(QuestionManagementDO::getIsActive, true));
        }
        Map<Long, List<QuestionManagementDO>> questionManagementDOMap = classQuestionManagementDOList.stream().collect(Collectors.groupingBy(QuestionManagementDO::getQuestionnaireId));


        pageResult.forEach(result -> {
            if (!Objects.equals(result.getStatus(), "0")) {
                result.setDistributedCount(evaluationResponseMapper.countTotal(result.getId()));
                result.setEvaluatedCount(evaluationResponseMapper.countHandled(result.getId()));
                result.setUnevaluatedCount(result.getDistributedCount() - result.getEvaluatedCount());
            } else {
                // 修改默认为 0
                result.setDistributedCount(0);
                result.setEvaluatedCount(0);
                result.setUnevaluatedCount(0);
            }
            if (result.getPublisher() != null) {
                AdminUserRespDTO user = adminUserApi.getUser(result.getPublisher()).getCheckedData();
                result.setPublisherName(user.getNickname());
            }
            if (result.getPublishScale() != null) {
                if (result.getPublishScale().equals("-1")) {
                    result.setPublishScaleName("所有在校");
                } else {

                    ClassManagementDO targetClass = classManagementDOMap.get(Long.parseLong(result.getPublishScale()));
                    if (Objects.nonNull(targetClass)) {
                        result.setPublishScaleName(targetClass.getClassName());
                        //找到班主任的名字填上
                        if (Objects.nonNull(targetClass.getClassTeacherLead())) {
                            TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectById(targetClass.getClassTeacherLead());
                            if (Objects.nonNull(teacherInformationDO)) {
                                result.setClassTeacherLeadName(teacherInformationDO.getName());
                            }
                        }
                        //再把开班结业时间填上
                        result.setClassOpenTime(targetClass.getClassOpenTime());
                        result.setCompletionTime(targetClass.getCompletionTime());
                    }
                }
            }
            result.setContainCourseQuestion(CollectionUtil.isNotEmpty(questionManagementDOMap.get(result.getId())));
        });
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(null,
                page.getTotal(),
                pageReqVO,
                pageResult.size());
        for (int i = 0; i < pageResult.size(); i++) {
            pageResult.get(i).setSerialNumber(serialNumberList.get(i));
        }
        if (pageReqVO.getIsSerialDesc() == null || pageReqVO.getIsSerialDesc() == false) {
            pageResult = pageResult.parallelStream().sorted(
                    Comparator.comparing(QuestionnaireStatsByTeacherRespVO::getSerialNumber)
            ).collect(Collectors.toList());
        } else {
            pageResult = pageResult.parallelStream().sorted(
                    Comparator.comparing(QuestionnaireStatsByTeacherRespVO::getSerialNumber).reversed()
            ).collect(Collectors.toList());
        }
        return new PageResult<>(pageResult, page.getTotal());
    }

    @Override
    public Page<PublishScaleRespVO> getPublishScale(PublishScaleReqVO pageReqVO) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime oneWeek = now.minusWeeks(1);
        // 查询开班中的课程
        List<PublishScaleRespVO> openClass = classManagementMapper.getOpenClass(now, pageReqVO);
        List<Long> allTeacherIds = openClass.stream().map(PublishScaleRespVO::getClassTeacherLead).collect(Collectors.toList());
        // 获取所有老师信息，并以id为key，名字为value的map
        Map<Long, String> IdToTeacherName = new HashMap<>();
        if (!allTeacherIds.isEmpty()) {
            IdToTeacherName = teacherInformationMapper.selectDOListByIds(allTeacherIds).stream()
                    .collect(Collectors.toMap(TeacherInformationDO::getId, TeacherInformationDO::getName));
        }
        for (PublishScaleRespVO publishScaleRespVO : openClass) {
            String name = IdToTeacherName.get(publishScaleRespVO.getClassTeacherLead());
            if (StringUtils.isNotEmpty(name)) {
                publishScaleRespVO.setClassTeacherLeadName(name);
            }
            publishScaleRespVO.setClassStudentNum(traineeMapper.getStudentNum(publishScaleRespVO.getClassId()));
        }

        // 查询结业一周内的课程
        List<PublishScaleRespVO> closedClass = classManagementMapper.getClosedClass(oneWeek, now, pageReqVO);
        List<Long> allTeacherclosedIds = closedClass.stream().map(PublishScaleRespVO::getClassTeacherLead).collect(Collectors.toList());
        // 获取所有老师信息，并以id为key，名字为value的map
        Map<Long, String> IdToTeacherNameClose = new HashMap<>();
        if (!allTeacherclosedIds.isEmpty()) {
            IdToTeacherNameClose = teacherInformationMapper.selectDOListByIds(allTeacherclosedIds).stream()
                    .collect(Collectors.toMap(TeacherInformationDO::getId, TeacherInformationDO::getName));
        }
        for (PublishScaleRespVO publishScaleRespVO : closedClass) {
            String name = IdToTeacherNameClose.get(publishScaleRespVO.getClassTeacherLead());
            if (StringUtils.isNotEmpty(name)) {
                publishScaleRespVO.setClassTeacherLeadName(name);
            }
            publishScaleRespVO.setClassStudentNum(traineeMapper.getStudentNum(publishScaleRespVO.getClassId()));
        }
        List<PublishScaleRespVO> allClass = new ArrayList<>();
        if (ClassManageStatus.OPENING.getCode().toString().equals(pageReqVO.getClassStatus())) {
            allClass.addAll(openClass);
        } else {
            allClass.addAll(closedClass);
        }
        //对allClass应用分页条件
        // 分页处理
        int total = allClass.size();
        int pageNo = pageReqVO.getPageNo();
        int pageSize = pageReqVO.getPageSize();

        // 手动分页：截取当前页的数据
        List<PublishScaleRespVO> pagedList = allClass.stream()
                .skip((long) (pageNo - 1) * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());

        Page<PublishScaleRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        page.setRecords(pagedList);
        page.setTotal(total);
        return page;
    }

    @Override
    public void updateQuestionnaireManagementStatus(QuestionnaireManagementSaveReqVO updateReqVO) {
        String updateMode = updateReqVO.getUpdateMode();
        QuestionnaireUpdateModeEnum updateModeEnum = QuestionnaireUpdateModeEnum.getByStatus(updateMode);
        if (Objects.isNull(updateModeEnum)) {
            throw exception(QUESTIONNAIRE_UPDATE_MODE_NOT_EXISTS);
        }

        switch (updateModeEnum) {
            case SCHEDULED_PENDING_UPDATE:
                handleScheduledPendingUpdate(updateReqVO);
                break;
            case REMINDER_PENDING_UPDATE:
                handleReminderPendingUpdate(updateReqVO);
                break;
            case DRAFT_PENDING_UPDATE:
                handleDraftPendingUpdate(updateReqVO);
                break;
            case PUBLISHED_PENDING_UPDATE:
                handlePublishedPendingUpdate(updateReqVO);
                break;
            case PUBLISHED_DELETED_PENDING_UPDATE:
                handlePublishedDeletedPendingUpdate(updateReqVO);
                break;
            default:
                throw exception(QUESTIONNAIRE_UPDATE_MODE_NOT_SUPPORT);
        }
    }

    private void handleScheduledPendingUpdate(QuestionnaireManagementSaveReqVO updateReqVO) {
        Long questionnaireId = updateReqVO.getId();

        // 如果是问卷处于定时待发布时进入更新 则将对应的定时任务暂停
        List<PgQuestionnaireHisDO> pgQuestionnaireHisDOList = pgQuestionnaireHisMapper.selectList(new LambdaQueryWrapperX<PgQuestionnaireHisDO>()
                .eq(PgQuestionnaireHisDO::getQuestionnaireId, questionnaireId)
                .eq(PgQuestionnaireHisDO::getStatus, PgQuestionnaireHisStatusEnum.UNDO.getStatus())
                .eq(PgQuestionnaireHisDO::getType, PgQuestionnaireHisTypeEnum.PUBLISH_QUESTIONNAIRE.getStatus()));
        if (CollectionUtil.isNotEmpty(pgQuestionnaireHisDOList)) {
            pgQuestionnaireHisMapper.updateById(pgQuestionnaireHisDOList.get(0).setStatus(PgQuestionnaireHisStatusEnum.PAUSE.getStatus()));
        }

        String status = updateReqVO.getStatus();
        LambdaUpdateWrapper<QuestionnaireManagementDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(QuestionnaireManagementDO::getStatus, status);
        wrapper.eq(QuestionnaireManagementDO::getId, questionnaireId);
        questionnaireManagementMapper.update(wrapper);
    }

    private void handleReminderPendingUpdate(QuestionnaireManagementSaveReqVO updateReqVO) {
        Long questionnaireId = updateReqVO.getId();
        String status = updateReqVO.getStatus();

        LambdaUpdateWrapper<QuestionnaireManagementDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(QuestionnaireManagementDO::getStatus, status);
        wrapper.eq(QuestionnaireManagementDO::getId, questionnaireId);
        questionnaireManagementMapper.update(wrapper);
    }

    private void handleDraftPendingUpdate(QuestionnaireManagementSaveReqVO updateReqVO) {
        Long questionnaireId = updateReqVO.getId();
        String status = updateReqVO.getStatus();

        LambdaUpdateWrapper<QuestionnaireManagementDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(QuestionnaireManagementDO::getStatus, status);
        wrapper.eq(QuestionnaireManagementDO::getId, questionnaireId);
        questionnaireManagementMapper.update(wrapper);
    }

    private void handlePublishedPendingUpdate(QuestionnaireManagementSaveReqVO updateReqVO) {
        Long questionnaireId = updateReqVO.getId();
        String status = updateReqVO.getStatus();

        LambdaUpdateWrapper<QuestionnaireManagementDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(QuestionnaireManagementDO::getStatus, status);
        wrapper.eq(QuestionnaireManagementDO::getId, questionnaireId);
        questionnaireManagementMapper.update(wrapper);
    }

    private void handlePublishedDeletedPendingUpdate(QuestionnaireManagementSaveReqVO updateReqVO) {
        Long questionnaireId = updateReqVO.getId();
        String status = updateReqVO.getStatus();

        LambdaUpdateWrapper<QuestionnaireManagementDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(QuestionnaireManagementDO::getStatus, status);
        wrapper.eq(QuestionnaireManagementDO::getId, questionnaireId);
        questionnaireManagementMapper.update(wrapper);
    }

    @Override
    public QuestionnaireStatisticsRespVO getQuestionnaireStatistics(Long id) {
        QuestionnaireManagementRespVO result = getQuestionnaireManagement(id, true);
        if (result.getPublisher() != null) {
            AdminUserRespDTO user = adminUserApi.getUser(result.getPublisher()).getCheckedData();
            result.setPublisherName(user.getNickname());
        }
        QuestionnaireStatisticsRespVO statistics = new QuestionnaireStatisticsRespVO();
        statistics.setPublisherName(result.getPublisherName());
        statistics.setStatus(result.getStatus());
        statistics.setPublishTime(result.getPublishTime());
        statistics.setTitle(result.getTitle());
        //这里应该把response的Handle为0（已撤回或者未提交）的作答排除
        List<EvaluationDetailDO> evaluations = evaluationDetailMapper.getQuestionnaireStatistics(id);
        List<QuestionManagementRespVO> questions = new ArrayList<>();
        if (result.getQuestions() != null) {
            questions = result.getQuestions().stream().filter(q -> Objects.equals(q.getQuestionType(), "1") || Objects.equals(q.getQuestionType(), "2") || Objects.equals(q.getQuestionType(), "3") || Objects.equals(q.getQuestionType(), "7"))
                    .collect(Collectors.toList());
        } else {
            return statistics;
        }

        List<QuestionAnswerRespVO> questionStat = new ArrayList<>();
        questions.forEach(question -> {
            QuestionAnswerRespVO answers = QuestionManagementConvert.Instance.convertVO(question);
            // 获取问题的所有作答
            List<EvaluationDetailDO> details = evaluations.stream()
                    .filter(ratedDetail -> ratedDetail.getQuestionId().equals(question.getId()))
                    .collect(Collectors.toList());

            if (!details.isEmpty()) {
                //1.多选 2.单选 3.简答

                if (Objects.equals(question.getQuestionType(), "1")) {
                    List<OptionsTemplateRespVO> options = question.getOptions();
                    List<OptionsRespVO> optionsRespVOS = OptionsConvert.Instance.convertList3(options);
                    details.forEach(detail -> {
                        if (detail.getOptionId() != null) {
                            String optionId = detail.getOptionId();
                            List<Long> optionIds = Arrays.stream(optionId.split(","))
                                    .map(String::trim)
                                    .filter(s -> !s.isEmpty())
                                    .map(Long::parseLong) // 遇到非法格式会抛出NumberFormatException
                                    .collect(Collectors.toList());
                            optionsRespVOS.forEach(optionsRespVO -> {
                                if (optionsRespVO.getSelectCount() == null) {
                                    optionsRespVO.setSelectCount(0L);
                                }
                                if (optionIds.contains(optionsRespVO.getId())) {
                                    optionsRespVO.setSelectCount(optionsRespVO.getSelectCount() + 1);
                                }
                            });
                        }
                    });
                    //不知道上面为什么要给条件才给空值赋值0，我感觉都要
                    optionsRespVOS.forEach(optionsRespVO -> {
                        if (Objects.isNull(optionsRespVO.getSelectCount())) {
                            optionsRespVO.setSelectCount(0L);
                        }
                    });
                    Long total = optionsRespVOS.stream()
                            .mapToLong(OptionsRespVO::getSelectCount)
                            .sum();

                    // 2. 处理总和为零的情况
                    if (total == 0) {
                        optionsRespVOS.forEach(opt -> opt.setPercentage(BigDecimal.ZERO));
                    } else {
                        // 3. 转换为BigDecimal进行计算
                        BigDecimal totalBD = new BigDecimal(total);
                        final int scale = 2; // 保留2位小数
                        final RoundingMode roundingMode = RoundingMode.HALF_UP; // 四舍五入

                        optionsRespVOS.forEach(opt -> {
                            // 计算百分比公式：(selectCount / total) * 100
                            BigDecimal countBD = new BigDecimal(opt.getSelectCount());
                            BigDecimal percent = countBD.multiply(new BigDecimal(100))
                                    .divide(totalBD, scale + 2, roundingMode) // 中间计算保留更多精度
                                    .setScale(scale, roundingMode); // 最终结果保留2位小数

                            opt.setPercentage(percent);
                        });


                    }
                    answers.setOptions(optionsRespVOS);
                }
                if (Objects.equals(question.getQuestionType(), "2") || Objects.equals(question.getQuestionType(), "7")) {
                    List<OptionsTemplateRespVO> options = question.getOptions();
                    List<OptionsRespVO> optionsRespVOS = OptionsConvert.Instance.convertList3(options);
                    details.forEach(detail -> {
                        if (detail.getOptionId() != null) {
                            Long optionId = Long.parseLong(detail.getOptionId());
                            optionsRespVOS.forEach(optionsRespVO -> {
                                if (optionsRespVO.getSelectCount() == null) {
                                    optionsRespVO.setSelectCount(0L);
                                }
                                if (Objects.equals(optionId, optionsRespVO.getId())) {
                                    optionsRespVO.setSelectCount(optionsRespVO.getSelectCount() + 1);
                                }
                            });
                        }
                    });
                    //不知道上面为什么要给条件才给空值赋值0，我感觉都要
                    optionsRespVOS.forEach(optionsRespVO -> {
                        if (Objects.isNull(optionsRespVO.getSelectCount())) {
                            optionsRespVO.setSelectCount(0L);
                        }
                    });
                    Long total = optionsRespVOS.stream()
                            .mapToLong(OptionsRespVO::getSelectCount)
                            .sum();

                    // 2. 处理总和为零的情况
                    if (total == 0) {
                        optionsRespVOS.forEach(opt -> opt.setPercentage(BigDecimal.ZERO));
                    } else {
                        // 3. 转换为BigDecimal进行计算
                        BigDecimal totalBD = new BigDecimal(total);
                        final int scale = 2; // 保留2位小数
                        final RoundingMode roundingMode = RoundingMode.HALF_UP; // 四舍五入

                        optionsRespVOS.forEach(opt -> {
                            // 计算百分比公式：(selectCount / total) * 100
                            BigDecimal countBD = new BigDecimal(opt.getSelectCount());
                            BigDecimal percent = countBD.multiply(new BigDecimal(100))
                                    .divide(totalBD, scale + 2, roundingMode) // 中间计算保留更多精度
                                    .setScale(scale, roundingMode); // 最终结果保留2位小数

                            opt.setPercentage(percent);
                        });

                    }
                    answers.setOptions(optionsRespVOS);

                }

                if (Objects.equals(question.getQuestionType(), "3")) {
                    List<String> contents = new ArrayList<>();
                    details.forEach(detail -> {
                        if (detail.getContent() != null) {
                            contents.add(detail.getContent());
                        }
                    });
                    answers.setContent(contents);
                }
                questionStat.add(answers);
            } else {
                if (Objects.equals(question.getQuestionType(), "1") || Objects.equals(question.getQuestionType(), "2") || Objects.equals(question.getQuestionType(), "7")) {
                    List<OptionsRespVO> optionsRespVOS = OptionsConvert.Instance.convertList3(question.getOptions());
                    optionsRespVOS.forEach(option -> {
                        option.setSelectCount(0L);
                        option.setPercentage(new BigDecimal("0.00"));
                    });
                    answers.setOptions(optionsRespVOS);
                }

                if (Objects.equals(question.getQuestionType(), "3")) {
                    answers.setContent(new ArrayList<>());
                }
                questionStat.add(answers);
            }
        });
        statistics.setQuestions(questionStat);
        return statistics;
    }

    @Override
    public PageResult<EvaluationCountRespVO> getEvaluationCount(EvaluationCountPageReqVO pageReqVO) {
        IPage<EvaluationCountRespVO> page = MyBatisUtils.buildPage(pageReqVO);
        List<EvaluationCountRespVO> pageResult = questionnaireManagementMapper.selectEvaluationCount(page, pageReqVO);
        return new PageResult<>(pageResult, page.getTotal());
    }

    @Override
    public List<DetailRespVO> getQuestionnaireDetail(DetailPageReqVO pageReqVO) {
        List<DetailVO> result = questionnaireManagementMapper.selectDetail(pageReqVO);

        Map<Long, List<DetailVO>> groupedByStudentId = result.stream().distinct()
                .collect(Collectors.groupingBy(DetailVO::getStudentId));
        List<DetailRespVO> resp = new ArrayList<>();
        for (Long studentId : groupedByStudentId.keySet()) {
            List<DetailVO> detailList = groupedByStudentId.get(studentId).stream().filter(d ->
                            Objects.equals(d.getQuestionType(), "7") || Objects.equals(d.getQuestionType(), "1") || Objects.equals(d.getQuestionType(), "2") || Objects.equals(d.getQuestionType(), "3"))
                    .collect(Collectors.toList());
            DetailVO detailVO = detailList.get(0);

            // 业务逻辑
            DetailRespVO respVO = new DetailRespVO();
            TraineeDO traineeDO = traineeMapper.selectById(detailVO.getStudentId());
            respVO.setStudentId(detailVO.getStudentId().toString());
            respVO.setStudentName(detailVO.getStudentName());
            respVO.setStudentUserId(traineeDO.getUserId().toString());
            respVO.setClassName(detailVO.getClassName());
            respVO.setTitle(detailVO.getTitle());
            respVO.setPublishTime(detailVO.getPublishTime());
            respVO.setUpdateTime(detailVO.getUpdateTime());
            respVO.setSubmitTime(detailVO.getSubmitTime());
            List<QuestionManagementRespVO> questionAnswers = new ArrayList<>();
            detailList.forEach(detail -> {
                QuestionManagementRespVO answer = new QuestionManagementRespVO();
                if (Objects.equals(detail.getQuestionType(), "1")) {
                    QuestionManagementDO question = questionManagementMapper.selectById(detail.getQuestionId());
                    List<OptionsDO> optionsDOS = optionsMapper.selectListByQuestionId(detail.getQuestionId());
                    question.setOptions(optionsDOS);
                    answer = QuestionManagementConvert.Instance.convertDO(question);
                    if (detail.getOptionId() != null) {
                        answer.setRateMultiOptionId(Arrays.stream(detail.getOptionId().split(","))
                                .map(String::trim)
                                .filter(s -> !s.isEmpty())
                                .collect(Collectors.toList()));
                    }
                }
                if (Objects.equals(detail.getQuestionType(), "7")) {
                    QuestionManagementDO question = questionManagementMapper.selectById(detail.getQuestionId());
                    List<OptionsDO> optionsDOS = optionsMapper.selectListByQuestionId(detail.getQuestionId());
                    question.setOptions(optionsDOS);
                    answer = QuestionManagementConvert.Instance.convertDO(question);
                    if (detail.getOptionId() != null) {
                        answer.setRateMultiOptionId(Arrays.stream(detail.getOptionId().split(","))
                                .map(String::trim)
                                .filter(s -> !s.isEmpty())
                                .collect(Collectors.toList()));
                    }
                    if (detail.getContent() != null) {
                        answer.setRateContent(detail.getContent());
                    }
                }
                if (Objects.equals(detail.getQuestionType(), "2")) {
                    QuestionManagementDO question = questionManagementMapper.selectById(detail.getQuestionId());
                    List<OptionsDO> optionsDOS = optionsMapper.selectListByQuestionId(detail.getQuestionId());
                    question.setOptions(optionsDOS);
                    answer = QuestionManagementConvert.Instance.convertDO(question);
                    if (detail.getOptionId() != null) {
                        answer.setRateOptionId(detail.getOptionId());
                    }
                }
                if (Objects.equals(detail.getQuestionType(), "3")) {
                    QuestionManagementDO question = questionManagementMapper.selectById(detail.getQuestionId());
                    answer = QuestionManagementConvert.Instance.convertDO(question);
                    if (detail.getContent() != null) {
                        answer.setRateContent(detail.getContent());
                    }
                }
                questionAnswers.add(answer);
                questionAnswers.sort(Comparator.comparing(QuestionManagementRespVO::getId));
            });
            respVO.setQuestions(questionAnswers);
            resp.add(respVO);
        }
        for (int i = 0; i < resp.size(); i++) {
            DetailRespVO respVO = resp.get(i);
            // 从1开始编号（格式化为字符串，如001）
            Long serial = i + 1L; // 数字转3位字符串（001,002...）
            respVO.setSerialNumber(serial);
        }
        return resp;
    }

    public static void main(String[] args) {
        // 1. 创建工作簿
        Workbook workbook = new XSSFWorkbook();

        // 2. 创建工作表
        Sheet sheet = workbook.createSheet("XX班评估汇总表");

        // 3. 设置列宽
        sheet.setColumnWidth(0, 5000);  // A列
        sheet.setColumnWidth(1, 3000);  // B列
        sheet.setColumnWidth(2, 3000);  // C列
        sheet.setColumnWidth(3, 3000);  // D列
        sheet.setColumnWidth(4, 3000);  // E列
        sheet.setColumnWidth(5, 3000);  // F列
        sheet.setColumnWidth(6, 3000);  // G列
        sheet.setColumnWidth(7, 3000);  // H列
        sheet.setColumnWidth(8, 3000);  // I列
        sheet.setColumnWidth(9, 5000);  // J列

        // 4. 创建样式
        CellStyle headerStyle = ExcelCellUtils.createHeaderStyle(workbook);
        CellStyle dataStyle = ExcelCellUtils.createDataStyle(workbook);
        CellStyle titleStyle = ExcelCellUtils.createTitleStyle(workbook);
        CellStyle mergedStyle = ExcelCellUtils.createMergedHeaderStyle(workbook);

        // 5. 生成表格内容
        int rowNum = 0;
        // 标题行
        Row titleRow = sheet.createRow(rowNum++);
        ExcelCellUtils.createMergedCell(sheet, titleRow, 0, 9, "XX班评估汇总表", titleStyle);

        // 5.1 第一行标题信息
        Row infoRow = sheet.createRow(rowNum++);
        ExcelCellUtils.createCell(infoRow, 0, "培训时间", headerStyle);
        ExcelCellUtils.createCell(infoRow, 1, "4.6-4.12", dataStyle);
        ExcelCellUtils.createCell(infoRow, 2, "培训人数", headerStyle);
        ExcelCellUtils.createCell(infoRow, 3, "117", dataStyle);
        ExcelCellUtils.createCell(infoRow, 4, "参评人数", headerStyle);
        ExcelCellUtils.createCell(infoRow, 5, "109", dataStyle);
        ExcelCellUtils.createCell(infoRow, 6, "教务老师", headerStyle);
        ExcelCellUtils.createCell(infoRow, 7, "刘成豪", dataStyle);
        ExcelCellUtils.createCell(infoRow, 8, "带班老师", headerStyle);
        ExcelCellUtils.createCell(infoRow, 9, "黄昌、黄俊、黄昊博", dataStyle);


        // 5.4 表头行
        Row headerRow = sheet.createRow(rowNum++);
        ExcelCellUtils.createMergedCellBorder(sheet, headerRow, 0, 1, "评估项目", headerStyle);

        // 5.5 类别行
        Row categoryRow = sheet.createRow(rowNum++);
        ExcelCellUtils.createCell(categoryRow, 0, "类别", headerStyle);
        ExcelCellUtils.createCell(categoryRow, 1, "评估内容", headerStyle);

        String[] headers = {"测评人数", "满意人数", "一般人数", "不满意人数", "满意", "一般", "不满意", "整体满意率"};

        for (int i = 0; i < headers.length; i++) {

            ExcelCellUtils.mergeVertically(sheet, 2 + i, rowNum - 2, rowNum - 1, headers[i], mergedStyle);

        }


        rowNum++;


        ExcelCellUtils.mergeCells(sheet, rowNum, rowNum + 1, 0, 1, "课程", mergedStyle);

        for (int i = 0; i < headers.length; i++) {

            ExcelCellUtils.mergeVertically(sheet, 2 + i, rowNum, rowNum + 1, headers[i], mergedStyle);

        }

        String savePath = "D:\\XX班评估汇总表.xlsx"; // 指定路径

        // 写入文件
        try {
            Files.createDirectories(Paths.get(savePath).getParent()); // 确保目录存在
            try (FileOutputStream outputStream = new FileOutputStream(savePath)) {
                workbook.write(outputStream);
                System.out.println("文件已保存至: " + savePath);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public Boolean completeTodo(QuestionnaireCompleteTodoVO reqVO) {
        TodoItemsDO todoItemsDO = new TodoItemsDO();
        todoItemsDO.setId(reqVO.getId());
        todoItemsDO.setStatus(TeacherTodoStatusEnum.DONE.getStatus());
        todoItemsMapper.updateById(todoItemsDO);
        return true;
    }


}
