-- 创建序列，用于自增ID
DROP SEQUENCE IF EXISTS "edu_teaching_point_seq";
CREATE SEQUENCE IF NOT EXISTS "edu_teaching_point_seq" INCREMENT BY 1 START WITH 1 CACHE 20;

-- 教学点管理表
CREATE TABLE IF NOT EXISTS "edu_teaching_point" (
  "id" BIGINT NOT NULL DEFAULT nextval('edu_teaching_point_seq'),
  "name" VARCHAR(1024) NOT NULL,
  "contact_name" VARCHAR(1024) NOT NULL,
  "contact_phone" VARCHAR(11) NOT NULL,
  "teacher_id" BIGINT,
  "creator" VARCHAR(64) DEFAULT '',
  "create_time" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updater" VARCHAR(64) DEFAULT '',
  "update_time" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "deleted" INTEGER NOT NULL DEFAULT 0,
  "tenant_id" BIGINT NOT NULL DEFAULT 0,
  PRIMARY KEY ("id")
);
COMMENT ON TABLE "edu_teaching_point" IS '教学点管理表';
COMMENT ON COLUMN "edu_teaching_point"."id" IS '编号';
COMMENT ON COLUMN "edu_teaching_point"."name" IS '教学点名称';
COMMENT ON COLUMN "edu_teaching_point"."contact_name" IS '联络员姓名';
COMMENT ON COLUMN "edu_teaching_point"."contact_phone" IS '联络员联系方式';
COMMENT ON COLUMN "edu_teaching_point"."teacher_id" IS '绑定的校外老师ID';
COMMENT ON COLUMN "edu_teaching_point"."creator" IS '创建者';
COMMENT ON COLUMN "edu_teaching_point"."create_time" IS '创建时间';
COMMENT ON COLUMN "edu_teaching_point"."updater" IS '更新者';
COMMENT ON COLUMN "edu_teaching_point"."update_time" IS '更新时间';
COMMENT ON COLUMN "edu_teaching_point"."deleted" IS '是否删除';
COMMENT ON COLUMN "edu_teaching_point"."tenant_id" IS '租户编号';
