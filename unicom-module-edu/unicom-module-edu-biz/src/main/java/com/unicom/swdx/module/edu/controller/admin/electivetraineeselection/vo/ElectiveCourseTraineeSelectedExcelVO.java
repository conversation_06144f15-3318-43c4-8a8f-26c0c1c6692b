package com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel("管理后台 - 发布课程的已选课学员信息 导出 VO")
@Data
public class ElectiveCourseTraineeSelectedExcelVO {

    @ExcelProperty(value = "学员姓名")
    private String name;

    @ExcelProperty(value = "学员性别")
    private String sex;

    @ExcelProperty(value = "学员身份证号")
    private String cardNo;

    @ExcelProperty(value = "学员手机号")
    private String phone;

    @ExcelProperty(value = "学员所在单位")
    private String unitName;

    @ExcelProperty(value = "文化程度")
    private String educationalLevelStr;

    @ExcelProperty(value = "学员职务")
    private String position;

    @ExcelProperty(value = "政治面貌")
    private String politicalIdentityStr;

}
