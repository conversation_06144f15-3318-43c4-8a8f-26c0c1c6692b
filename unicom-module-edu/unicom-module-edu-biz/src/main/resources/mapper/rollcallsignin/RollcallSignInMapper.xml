<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.rollcallsignin.RollcallSignInMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectRollcallSignInListByReqVO"
            resultType="com.unicom.swdx.module.edu.controller.admin.rollcallsignin.vo.RollcallSignInRespVO">
        -- 查询点名签到信息列表 包含应签人数 已打卡人数
        select
            s.id,
            s.title,
            s.class_id,
            s."type",
            s.check_start_time,
            s.check_end_time,
            s.latitude,
            s.longitude,
            s.radius,
            s.address,
            s.status,
            s.create_time,
            count(1) checkTotal,
            count(err.status = 1 or null) checkedCount
        from edu_rollcall_sign_in s
            left join edu_rollcall_record err on s.id = err.rollcall_id and err.deleted = 0
        where
            s.deleted = 0
            and s."type" = 1
            and s.class_id = #{reqVO.classId}
            <if test="reqVO.status == null or reqVO.status == 0">
                and s.check_end_time &gt; #{currentTime}
                and s.ended = 0
            </if>
            <if test="reqVO.status == 1">
                and (s.check_end_time &lt; #{currentTime} or s.ended = 1)
            </if>
        group by s.id
        order by s.create_time desc
    </select>

    <select id="selectLectureAttendanceListByReqVO"
            resultType="com.unicom.swdx.module.edu.controller.admin.rollcallsignin.vo.RollcallSignInRespVO">
        SELECT
            s.id,
            s.title,
            s.class_course_id,
            s.class_id,
            s."type",
            s.check_start_time,
            s.check_end_time,
            s.latitude,
            s.longitude,
            s.radius,
            s.address,
            s.status,
            s.create_time,
            min(ecc.period) dayPeriod,
            min(ecc.begin_time) classStartTime,
            min(ecc.end_time) classEndTime,
            min(ecc."date") classDate,
            min(ec."name") courseName,
            min(ec.id) courseId,
            CASE
                WHEN min(ecc.change_time) IS NULL THEN false
                ELSE min(ecc.change_time) > s.create_time
            END AS isChange,
            count(ecii.trainee_status) checkTotal,
            count(ecii.trainee_status = 1 or null) checkedCount
        FROM
        edu_rollcall_sign_in s
        left join edu_clock_in_info ecii on s.class_course_id = ecii.class_course_id and ecii.deleted = 0
        left join edu_class_course ecc on ecc.id = s.class_course_id
        left join edu_courses ec on ec.id = ecc.course_id and ec.deleted = 0
        where
        s.deleted = 0
        and ecc.deleted = 0
        and s."type" = 0
        and s.class_id = #{reqVO.classId}
        <if test="reqVO.status == null or reqVO.status == 0">
            and #{currentTime} &lt; s.check_end_time
            and s.ended = 0
        </if>
        <if test="reqVO.status == 1">
            and (s.check_end_time &lt; #{currentTime} or s.ended = 1)
        </if>
        group by s.id
        order by s.create_time desc
    </select>

    <select id="selectListByClassIdAndClassCourseId"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.rollcallsignin.RollcallSignInDO">
        select
            ersi.id,
            ersi.title,
            ersi.class_course_id,
            ersi.class_id,
            ersi."type",
            ersi.check_start_time,
            ersi.check_end_time,
            ersi.latitude,
            ersi.longitude,
            ersi.radius,
            ersi.address,
            ersi.status,
            ersi.create_time
        from edu_rollcall_sign_in ersi
        where ersi.deleted = 0
            and ersi.class_id = #{classId}
            and ersi.class_course_id = #{classCourseId}
            and ersi.ended = 0
            and ersi.check_end_time &gt; #{currentTime}
    </select>
    <select id="selectListByCurrentTimeRule"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.rollcallsignin.RollcallSignInDO">
        select
            ersi.id,
            ersi.title,
            ersi.class_course_id,
            ersi.class_id,
            ersi."type",
            ersi.check_start_time,
            ersi.check_end_time,
            ersi.latitude,
            ersi.longitude,
            ersi.radius,
            ersi.creator,
            ersi.create_time,
            ersi.ended,
            ersi.address,
            ersi.status
        from edu_rollcall_sign_in ersi
        where ersi.deleted = 0
          and ersi.class_id = #{classId}
          and ersi.ended = 0
          and #{nowDateTime} between ersi.check_start_time and ersi.check_end_time
          and ersi."type" = #{type}
        order by ersi.check_start_time asc
    </select>
    <select id="selectListByCheckOverlap"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.rollcallsignin.RollcallSignInDO">
        select
            ersi.id,
            ersi.title,
            ersi.class_course_id,
            ersi.class_id,
            ersi."type",
            ersi.check_start_time,
            ersi.check_end_time,
            ersi.latitude,
            ersi.longitude,
            ersi.radius,
            ersi.creator,
            ersi.create_time,
            ersi.ended,
            ersi.address,
            ersi.status
        from edu_rollcall_sign_in ersi
        where ersi.deleted = 0
        and ersi.ended = 0
        and ersi."type" = 0
        and #{nowDateTime} &lt; ersi.check_end_time
        and ersi.class_id = #{classId}
        and not (#{beginTime} &gt;= ersi.check_end_time or
        #{endTime} &lt;= ersi.check_start_time )
    </select>
</mapper>
