package com.unicom.swdx.module.edu.service.questionnairedetail;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.unicom.swdx.framework.test.core.ut.BaseDbUnitTest;

import com.unicom.swdx.module.edu.controller.admin.questionnairedetail.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairedetail.QuestionnaireDetailDO;
import com.unicom.swdx.module.edu.dal.mysql.questionnairedetail.QuestionnaireDetailMapper;
import com.unicom.swdx.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.framework.test.core.util.AssertUtils.*;
import static com.unicom.swdx.framework.test.core.util.RandomUtils.*;

import static com.unicom.swdx.framework.common.util.object.ObjectUtils.*;
import static com.unicom.swdx.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link QuestionnaireDetailServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(QuestionnaireDetailServiceImpl.class)
public class QuestionnaireDetailServiceImplTest extends BaseDbUnitTest {

    @Resource
    private QuestionnaireDetailServiceImpl questionnaireDetailService;

    @Resource
    private QuestionnaireDetailMapper questionnaireDetailMapper;

    @Test
    public void testCreateQuestionnaireDetail_success() {
        // 准备参数
        QuestionnaireDetailSaveReqVO createReqVO = randomPojo(QuestionnaireDetailSaveReqVO.class).setId(null);

        // 调用
        Long questionnaireDetailId = questionnaireDetailService.createQuestionnaireDetail(createReqVO);
        // 断言
        assertNotNull(questionnaireDetailId);
        // 校验记录的属性是否正确
        QuestionnaireDetailDO questionnaireDetail = questionnaireDetailMapper.selectById(questionnaireDetailId);
        assertPojoEquals(createReqVO, questionnaireDetail, "id");
    }

    @Test
    public void testUpdateQuestionnaireDetail_success() {
        // mock 数据
        QuestionnaireDetailDO dbQuestionnaireDetail = randomPojo(QuestionnaireDetailDO.class);
        questionnaireDetailMapper.insert(dbQuestionnaireDetail);// @Sql: 先插入出一条存在的数据
        // 准备参数
        QuestionnaireDetailSaveReqVO updateReqVO = randomPojo(QuestionnaireDetailSaveReqVO.class, o -> {
            o.setId(dbQuestionnaireDetail.getId()); // 设置更新的 ID
        });

        // 调用
        questionnaireDetailService.updateQuestionnaireDetail(updateReqVO);
        // 校验是否更新正确
        QuestionnaireDetailDO questionnaireDetail = questionnaireDetailMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, questionnaireDetail);
    }

    @Test
    public void testUpdateQuestionnaireDetail_notExists() {
        // 准备参数
        QuestionnaireDetailSaveReqVO updateReqVO = randomPojo(QuestionnaireDetailSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> questionnaireDetailService.updateQuestionnaireDetail(updateReqVO), QUESTIONNAIRE_DETAIL_NOT_EXISTS);
    }

    @Test
    public void testDeleteQuestionnaireDetail_success() {
        // mock 数据
        QuestionnaireDetailDO dbQuestionnaireDetail = randomPojo(QuestionnaireDetailDO.class);
        questionnaireDetailMapper.insert(dbQuestionnaireDetail);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbQuestionnaireDetail.getId();

        // 调用
        questionnaireDetailService.deleteQuestionnaireDetail(id);
       // 校验数据不存在了
       assertNull(questionnaireDetailMapper.selectById(id));
    }

    @Test
    public void testDeleteQuestionnaireDetail_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> questionnaireDetailService.deleteQuestionnaireDetail(id), QUESTIONNAIRE_DETAIL_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetQuestionnaireDetailPage() {
       // mock 数据
       QuestionnaireDetailDO dbQuestionnaireDetail = randomPojo(QuestionnaireDetailDO.class, o -> { // 等会查询到
           o.setQuestionId(null);
           o.setQuestionnaireId(null);
           o.setCreateDept(null);
           o.setCreateTime(null);
         //  o.setCreater(null);
           o.setOneBallotVeto(null);
       });
       questionnaireDetailMapper.insert(dbQuestionnaireDetail);
       // 测试 questionId 不匹配
       questionnaireDetailMapper.insert(cloneIgnoreId(dbQuestionnaireDetail, o -> o.setQuestionId(null)));
       // 测试 questionnaireId 不匹配
       questionnaireDetailMapper.insert(cloneIgnoreId(dbQuestionnaireDetail, o -> o.setQuestionnaireId(null)));
       // 测试 createDept 不匹配
       questionnaireDetailMapper.insert(cloneIgnoreId(dbQuestionnaireDetail, o -> o.setCreateDept(null)));
       // 测试 createTime 不匹配
       questionnaireDetailMapper.insert(cloneIgnoreId(dbQuestionnaireDetail, o -> o.setCreateTime(null)));
       // 测试 creator 不匹配
      // questionnaireDetailMapper.insert(cloneIgnoreId(dbQuestionnaireDetail, o -> o.setCreater(null)));
       // 测试 oneBallotVeto 不匹配
       questionnaireDetailMapper.insert(cloneIgnoreId(dbQuestionnaireDetail, o -> o.setOneBallotVeto(null)));
       // 准备参数
       QuestionnaireDetailPageReqVO reqVO = new QuestionnaireDetailPageReqVO();
       reqVO.setQuestionId(null);
       reqVO.setQuestionnaireId(null);
       reqVO.setCreateDept(null);

       //reqVO.setCreater(null);
       reqVO.setOneBallotVeto(null);

       // 调用
       PageResult<QuestionnaireDetailDO> pageResult = questionnaireDetailService.getQuestionnaireDetailPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbQuestionnaireDetail, pageResult.getList().get(0));
    }

}
