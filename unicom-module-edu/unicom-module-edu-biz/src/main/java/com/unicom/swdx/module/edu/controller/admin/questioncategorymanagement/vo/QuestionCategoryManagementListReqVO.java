package com.unicom.swdx.module.edu.controller.admin.questioncategorymanagement.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.unicom.swdx.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 题目类别管理列表 Request VO")
@Data
public class QuestionCategoryManagementListReqVO {

    @Schema(description = "题目类别名称", example = "李四")
    private String fullName;

    @Schema(description = "创建部门")
    private Long createDept;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "父级", example = "2048")
    private Long parentId;

    @Schema(description = "是否内置", example = "false")
    private Boolean builtIn;

}