<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.schoolaccommodationattendance.SchoolAccommodationAttendanceMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="selectDateRangeList" resultType="com.unicom.swdx.module.edu.dal.dataobject.schoolaccommodationattendance.SchoolAccommodationAttendanceDO">
        select
            id,
            breakfast,
            lunch,
            dinner,
            put_up,
            clock_date,
            create_time,
            is_holiday
        from
            edu_school_accommodation_attendance
        where
            deleted = '0'
        <if test = "reqVO.endDate != null">
            and clock_date &lt;= #{reqVO.endDate}
        </if>
        <if test = "reqVO.startDate != null">
            and clock_date >= #{reqVO.startDate}
        </if>
        <if test = "reqVO.clockDate != null">
            and clock_date = #{reqVO.clockDate}
        </if>
        ORDER BY
        clock_date ASC;
    </select>

</mapper>
