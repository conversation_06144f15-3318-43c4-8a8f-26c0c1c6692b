package com.unicom.swdx.module.edu;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 启动SpringBoot会执行 @Order标识执行顺序 值越小越先执行
 *
 * <AUTHOR>
 * @Email <EMAIL>
 */
@Component
@Order(1)
    public class StartupRunner implements CommandLineRunner {
        private Logger logger = LoggerFactory.getLogger(getClass());

        @Override
        public void run(String... args) throws Exception {
            logger.info(">>服务启动执行，执行加载数据等操作1<<");


    //        ScheduledExecutorService service  =  Executors.newScheduledThreadPool(1);
    //
    //        service.scheduleAtFixedRate(new Runnable() {
    //            @Override
    //            public void run() {
    //
    //                formPosta();
    //
    //            }
    //        } , 1 , 3, TimeUnit.SECONDS);

    //        Jscode2session(openid=oDowM5vy9EzmgmFAa1SH8hW083i8, sessionKey=e7thbXQvSjUUZ1h2bImskQ==, unionid=null, errcode=null, errmsg=null)
    //        System.out.println(WxUtils.getUserWXLoginInfo(WxUtils.code));


    //        TokenResult token = WxUtils.gettoken();
    //        System.out.println(token);
    //
    //        System.out.println(WxUtils.getuserphonenumber(token.getAccess_token() ,WxUtils.code ));




        }


    //    public void formPost() {
    //        HashMap<String, Object> body = new HashMap<>();
    //        body.put("ipcId", "jytm_13");
    //        body.put("ipcType", "dhua");
    //        body.put("ptf", "test");
    //        body.put("images", "http://*************:9000/rcs/test.jpg");
    //        String result= HttpUtil.createPost("http://**************:15002/chance/build/api/v1.0/infers").form(body).execute().body();
    //
    //
    //        SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
    //        Date date = new Date(System.currentTimeMillis());
    //        System.out.println(formatter.format(date));
    //        System.out.println(result);
    //
    //    }


    //    public void formPosta() {
    //        HashMap<String, Object> body = new HashMap<>();
    //        body.put("ipcId", "111");
    //        body.put("ipcType", "");
    //        body.put("ptf", "river");
    //        body.put("images", "http://**************:10180/rcs/93284d4c-9688-4a49-b451-bbefaef68125/44010200492000000001_12000000001310434687_1683786631159.jpg");
    //        String result= HttpUtil.createPost("http://**************:15002/chance/river/api/v1.0/infers").form(body).execute().body();
    //
    //
    //        SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
    //        Date date = new Date(System.currentTimeMillis());
    //        System.out.println(formatter.format(date));
    //        System.out.println(result);
    //
    //    }
    }
