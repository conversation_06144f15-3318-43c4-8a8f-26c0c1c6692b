package com.unicom.swdx.module.edu.service.traineegroup;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.AppTraineeGroupRespVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.GroupBaseReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupRespVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupImportResultVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupImportDataVO;
import com.unicom.swdx.module.edu.dal.dataobject.traineegroup.TraineeGroupDO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 用户信息 Service 接口
 *
 * <AUTHOR>
 */
public interface TraineeGroupService extends IService<TraineeGroupDO> {

    Long addGroup(GroupBaseReqVO reqVO);

    Long editGroup(GroupBaseReqVO reqVO);

    Boolean deleteGroup(List<Long> ids);

    Page<TraineeGroupRespVO> getPage(TraineeGroupReqVO reqVO);

    List<TraineeGroupRespVO> exportGroup(TraineeGroupReqVO reqVO);

    /**
     * 移动端分组获取学员列表
     * @param
     * @return
     */
    List<AppTraineeGroupRespVO> traineeListByGroup(Long classId);

    List<Map<String, String>> getGroupList(Long classId);

    TraineeGroupDO getGroupById(Long id);

    Boolean autoCreateGroup(Long classId,Long number);

    Boolean getTrainee(Long groupId);

    Boolean hasGroup(Long classId);

    /**
     * 下载学员分组导入模板
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void downloadImportTemplate(HttpServletResponse response) throws IOException;

    /**
     * 导入学员分组
     * @param importDataList 导入数据列表
     * @param classId 班级ID
     * @return 导入结果
     */
    TraineeGroupImportResultVO importTraineeGroup(List<TraineeGroupImportDataVO> importDataList, Long classId);
}
