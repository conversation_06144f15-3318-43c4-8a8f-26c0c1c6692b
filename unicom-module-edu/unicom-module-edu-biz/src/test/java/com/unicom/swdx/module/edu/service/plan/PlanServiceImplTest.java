package com.unicom.swdx.module.edu.service.plan;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.unicom.swdx.framework.test.core.ut.BaseDbUnitTest;

import com.unicom.swdx.module.edu.controller.admin.plan.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plan.PlanDO;
import com.unicom.swdx.module.edu.dal.mysql.plan.PlanMapper;
import com.unicom.swdx.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.framework.test.core.util.AssertUtils.*;
import static com.unicom.swdx.framework.test.core.util.RandomUtils.*;
import static com.unicom.swdx.framework.common.util.object.ObjectUtils.*;
import static com.unicom.swdx.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link PlanServiceImpl} 的单元测试类

*/
@Import(PlanServiceImpl.class)
public class PlanServiceImplTest extends BaseDbUnitTest {

    @Resource
    private PlanServiceImpl planService;

    @Resource
    private PlanMapper planMapper;

    @Test
    public void testCreatePlan_success() {
        // 准备参数
        PlanCreateReqVO reqVO = randomPojo(PlanCreateReqVO.class);

        // 调用
        Long planId = planService.createPlan(reqVO);
        // 断言
        assertNotNull(planId);
        // 校验记录的属性是否正确
        PlanDO plan = planMapper.selectById(planId);
        assertPojoEquals(reqVO, plan);
    }

    @Test
    public void testUpdatePlan_success() {
        // mock 数据
        PlanDO dbPlan = randomPojo(PlanDO.class);
        planMapper.insert(dbPlan);// @Sql: 先插入出一条存在的数据
        // 准备参数
        PlanUpdateReqVO reqVO = randomPojo(PlanUpdateReqVO.class, o -> {
            o.setId(dbPlan.getId()); // 设置更新的 ID
        });

        // 调用
        planService.updatePlan(reqVO);
        // 校验是否更新正确
        PlanDO plan = planMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, plan);
    }

    @Test
    public void testUpdatePlan_notExists() {
        // 准备参数
        PlanUpdateReqVO reqVO = randomPojo(PlanUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> planService.updatePlan(reqVO), PLAN_NOT_EXISTS);
    }

    @Test
    public void testDeletePlan_success() {
        // mock 数据
        PlanDO dbPlan = randomPojo(PlanDO.class);
        planMapper.insert(dbPlan);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlan.getId();

        // 调用
        planService.deletePlan(id);
       // 校验数据不存在了
       assertNull(planMapper.selectById(id));
    }

    @Test
    public void testDeletePlan_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planService.deletePlan(id), PLAN_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanPage() {
       // mock 数据
       PlanDO dbPlan = randomPojo(PlanDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setBeginDate(null);
           o.setEndDate(null);
//           o.setPeriod(null);
//           o.setBeginTime(null);
//           o.setEndTime(null);
           o.setClassroomId(null);
           o.setClassId(null);
       });
       planMapper.insert(dbPlan);
       // 测试 name 不匹配
       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setName(null)));
       // 测试 beginDate 不匹配
       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setBeginDate(null)));
       // 测试 endDate 不匹配
       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setEndDate(null)));
       // 测试 period 不匹配
//       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setPeriod(null)));
//       // 测试 beginTime 不匹配
//       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setBeginTime(null)));
//       // 测试 endTime 不匹配
//       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setEndTime(null)));
       // 测试 classroomId 不匹配
       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setClassroomId(null)));
       // 测试 classId 不匹配
       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setClassId(null)));
       // 准备参数
       PlanPageReqVO reqVO = new PlanPageReqVO();
       reqVO.setName(null);
//       reqVO.setBeginDate((new LocalDateTime[]{}));
//       reqVO.setEndDate((new LocalDateTime[]{}));
//       reqVO.setPeriod(null);
//       reqVO.setBeginTime((new LocalDateTime[]{}));
//       reqVO.setEndTime((new LocalDateTime[]{}));
       reqVO.setClassroomId(null);
       reqVO.setClassId(null);

       // 调用
       PageResult<PlanDO> pageResult = planService.getPlanPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlan, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanList() {
       // mock 数据
       PlanDO dbPlan = randomPojo(PlanDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setBeginDate(null);
           o.setEndDate(null);
//           o.setPeriod(null);
//           o.setBeginTime(null);
//           o.setEndTime(null);
           o.setClassroomId(null);
           o.setClassId(null);
       });
       planMapper.insert(dbPlan);
       // 测试 name 不匹配
       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setName(null)));
       // 测试 beginDate 不匹配
       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setBeginDate(null)));
       // 测试 endDate 不匹配
       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setEndDate(null)));
       // 测试 period 不匹配
//       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setPeriod(null)));
//       // 测试 beginTime 不匹配
//       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setBeginTime(null)));
//       // 测试 endTime 不匹配
//       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setEndTime(null)));
       // 测试 classroomId 不匹配
       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setClassroomId(null)));
       // 测试 classId 不匹配
       planMapper.insert(cloneIgnoreId(dbPlan, o -> o.setClassId(null)));
       // 准备参数
       PlanExportReqVO reqVO = new PlanExportReqVO();
       reqVO.setName(null);
//       reqVO.setBeginDate((new LocalDateTime[]{}));
//       reqVO.setEndDate((new LocalDateTime[]{}));
//       reqVO.setPeriod(null);
//       reqVO.setBeginTime((new LocalDateTime[]{}));
//       reqVO.setEndTime((new LocalDateTime[]{}));
       reqVO.setClassroomId(null);
       reqVO.setClassId(null);

       // 调用
       List<PlanDO> list = planService.getPlanList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlan, list.get(0));
    }

}
