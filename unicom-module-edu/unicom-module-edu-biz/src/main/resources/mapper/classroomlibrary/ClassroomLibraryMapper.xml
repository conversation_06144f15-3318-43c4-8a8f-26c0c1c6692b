<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.classroomlibrary.ClassroomLibraryMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getClassroomLibraryDataById" resultType="com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo.ClassroomLibraryExcelVO">
        SELECT
            ecl."id",
            ecl."class_name",
            ecl."building_name",
            ecl."capacity",
            ecl."creator",
            ecl."updater",
            ecl."update_time",
            ecl."deleted",
            ecl."tenant_id",
            ecl."create_time",
            ecl."dict_data_id",
            sdd."label" as campus_name
        FROM
            edu_classroom_library ecl left join system_dict_data sdd
                on ecl."dict_data_id" = sdd."id"
                       and sdd."deleted" = '0'
        WHERE
            ecl."id" = #{id}
        and
            ecl."deleted" = '0'
    </select>

    <select id="getClassroomLibraryDataAll" resultType="com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo.ClassroomLibraryExcelVO">
        SELECT
            ecl."id",
            ecl."class_name",
            ecl."building_name",
            ecl."capacity",
            ecl."creator",
            ecl."updater",
            ecl."update_time",
            ecl."deleted",
            ecl."tenant_id",
            ecl."create_time",
            ecl."dict_data_id",
            sdd."label" as campus_name
        FROM
            edu_classroom_library ecl left join system_dict_data sdd
                on ecl."dict_data_id" = sdd."id"
                       and sdd."deleted" = '0'
        WHERE
            ecl."deleted" = '0'
        <if test="params.className != null and params.className != ''">
            AND ecl."class_name" LIKE CONCAT('%', #{params.className}, '%')
        </if>
        <if test="params.buildingName != null and params.buildingName != ''">
            AND ecl."building_name" LIKE CONCAT('%', #{params.buildingName}, '%')
        </if>
        <!-- 如果 change 不为空，则进行排序 -->
        <if test="params.change != null">
            ORDER BY
            ecl."class_name"
            <if test="params.change == 0">
                ASC
            </if>
            <if test="params.change == 1">
                DESC
            </if>
        </if>
    </select>
    <select id="listForElectiveRelease"
            resultType="com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo.ClassroomLibrarySimpleRespVO">
        select
            ecl.id id,
            ecl.class_name name
        from
            edu_classroom_library ecl
        where
            ecl.deleted =0
            and not exists  (
                select
                    1
                from
                    edu_elective_release_courses eerc
                    left join edu_elective_release eer on eerc.release_id = eer.id
                where
                    eerc.deleted = 0
                    and eer.deleted = 0
                    and
                    (eer.class_start_time between #{classStartDateTime} and #{classEndDateTime}
                    or
                    eer.class_end_time  between #{classStartDateTime} and #{classEndDateTime}
                    or (eer.class_start_time &lt;= #{classStartDateTime} and eer.class_end_time &gt;= #{classEndDateTime}))
                    and ecl.id = eerc.classroom_id
                    <if test="excludeReleaseId != null">
                        and eer.id != #{excludeReleaseId}
                    </if>
                )
            and not exists (
                select 1
                    from edu_class_course ecc
                    left join edu_courses ec on ec.id = ecc.course_id and ec.deleted = 0
                    left join edu_plan ep on ecc.plan_id = ep.id and ep.deleted = 0
                where ecc.deleted = 0
--                     and ep.status = '1'
                    and ec.courses_type in (1,3)
                    and ecc.is_temporary = 0
                    and  (
                        ecc.begin_time between #{classStartDateTime} and #{classEndDateTime}
                        or
                        ecc.end_time  between #{classStartDateTime} and  #{classEndDateTime}
                        or (ecc.begin_time &lt;= #{classStartDateTime} and ecc.end_time &gt;=  #{classEndDateTime}))
                    and ecl.id = ecc.classroom_id
                    <if test="excludeClassCourseId != null">
                        and ecc.id != #{excludeClassCourseId}
                    </if>
            )
        order by ecl.create_time desc
    </select>

    <select id="getClassCourseByClassRoomId" resultType="java.lang.Integer">
        select
            count(1)
        from
            edu_class_course ecc
        where
            ecc.classroom_id = #{classRoomId}
        and
            ecc.deleted = '0'
    </select>

</mapper>
