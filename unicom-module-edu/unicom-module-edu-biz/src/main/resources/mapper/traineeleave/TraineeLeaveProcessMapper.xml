<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.traineeleave.TraineeLeaveProcessMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="selectTeacherList"
            resultType="com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeaveRespVO">
        SELECT etl.id,
            et.name as traineeName,
            etl.status,
            etl.apply_time applyTimeStr,
            etl.leave_type,
            etl.days
        FROM edu_trainee_leave etl
            LEFT JOIN edu_trainee_leave_process etlp ON etlp.leave_id = etl.id
            LEFT JOIN edu_trainee et ON etl.trainee_id = et.id
        WHERE etl.deleted = 0 AND etlp.deleted = 0
        <if test="classId != null">
            AND etl.class_id = #{classId}
        </if>
        <if test="userId != null">
            AND etlp.user_id = #{userId}
        </if>
        <if test="status == 0">
            AND etlp.task_status = 2
        </if>
        <if test="status == 1">
            AND etlp.task_status = 4
        </if>
        <if test="status == 2">
            AND etlp.task_status = 5
        </if>
        order by etl.apply_time DESC
    </select>
</mapper>
