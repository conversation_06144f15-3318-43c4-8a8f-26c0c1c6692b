package com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* EduClassroomLibrary Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ClassroomLibraryBaseVO {

    @ApiModelProperty(value = "教室名称")
    private String className;

//    @ApiModelProperty(value = "校区名称")
//    private String campusName;

    @ApiModelProperty(value = "建筑名称")
    private String buildingName;

    @ApiModelProperty(value = "容纳人数")
    private Integer capacity;

    @ApiModelProperty(value = "字典数据id")
    private Integer dictDataId;

    @ApiModelProperty(value = "是否现场教学点")
    private Boolean teachingPoint;

}
