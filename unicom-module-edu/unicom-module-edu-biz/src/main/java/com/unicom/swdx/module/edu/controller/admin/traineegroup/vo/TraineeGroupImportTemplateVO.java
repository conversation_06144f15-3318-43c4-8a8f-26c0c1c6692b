package com.unicom.swdx.module.edu.controller.admin.traineegroup.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 学员分组导入模板 VO
 */
@Data
public class TraineeGroupImportTemplateVO {

    @ExcelProperty(value = "序号（黄色为必填项）", index = 0)
    private String index;

    @ExcelProperty(value = "姓名", index = 1)
    private String name;

    @ExcelProperty(value = "联系方式", index = 2)
    private String phone;

    @ExcelProperty(value = "所在小组", index = 3)
    private String groupName;
}
