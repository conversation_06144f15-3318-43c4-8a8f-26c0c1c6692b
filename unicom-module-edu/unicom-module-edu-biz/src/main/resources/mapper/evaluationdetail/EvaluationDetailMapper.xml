<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.evaluationdetail.EvaluationDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <update id="submitEvaluation">
        update pg_evaluation_detail
        set
        option_id = #{submitVO.optionId},
        content = #{submitVO.content}
        where
            question_id = #{submitVO.questionId}
                and student_id = #{userId}
                and questionnaire_id  = #{submitVO.questionnaireId}
    </update>
    <update id="revoke">
        update pg_evaluation_detail
        set
            option_id = null,
            score = null,
            content = null
        where
            questionnaire_id = #{questionnaireId}
          and publish_scale = #{classId}
          and student_id = #{studentId}
    </update>
    <select id="selectCommentPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.CommentRespVO">
        select content from pg_evaluation_detail where class_course_id = #{reqVO.classCourseId} and content is not null and deleted = false order by update_time asc
    </select>
    <select id="selectComment" resultType="java.lang.String">
        select content from pg_evaluation_detail where class_course_id = #{classCourseId} and student_id = #{studentId} and content is not null and deleted = false order by update_time asc
    </select>
    <select id="selectCommentByClassCourseIdList"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.EvaluationCommentRespVO">
        select content as comment,
               class_course_id as classCourseId,
               student_id as studentId
        from pg_evaluation_detail
        where
            content is not null
            and deleted = false
            and class_course_id in
        <foreach collection="classCourseIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        order by update_time asc
    </select>
    <select id="getQuestionnaireStatistics"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.evaluationdetail.EvaluationDetailDO">
        select * from pg_evaluation_detail where questionnaire_id = #{id}
        and  deleted = 0 and  response_id in (select id from pg_evaluation_response where id = pg_evaluation_detail.response_id and handle = 1 and deleted = 0)
    </select>
</mapper>
