package com.unicom.swdx.module.edu.controller.admin.schoolaccommodationattendance;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.schoolaccommodationattendance.vo.*;
import com.unicom.swdx.module.edu.convert.schoolaccommodationattendance.SchoolAccommodationAttendanceConvert;
import com.unicom.swdx.module.edu.dal.dataobject.schoolaccommodationattendance.SchoolAccommodationAttendanceDO;
import com.unicom.swdx.module.edu.service.schoolaccommodationattendance.SchoolAccommodationAttendanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 全校就餐住宿考勤")
@RestController
@RequestMapping("/edu/school-accommodation-attendance")
@Validated
public class SchoolAccommodationAttendanceController {

    @Resource
    private SchoolAccommodationAttendanceService schoolAccommodationAttendanceService;

    @PostMapping("/create")
    @ApiOperation("创建一年的考勤数据")
    @PreAuthorize("@ss.hasPermission('edu:school-accommodation-attendance:create')")
    public CommonResult<Integer> createSchoolAccommodationAttendance(@Valid @RequestBody SchoolAccommodationAttendanceCreateReqVO createReqVO) {
        return success(schoolAccommodationAttendanceService.createSchoolAccommodationAttendance(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("单个更新")
    @PreAuthorize("@ss.hasPermission('edu:school-accommodation-attendance:update')")
    public CommonResult<Boolean> updateSchoolAccommodationAttendance(@Valid @RequestBody SchoolAccommodationAttendanceUpdateReqVO updateReqVO) {
        schoolAccommodationAttendanceService.updateSchoolAccommodationAttendance(updateReqVO);
        return success(true);
    }

    @PostMapping("/batch-update")
    @ApiOperation("批量更新")
    @PreAuthorize("@ss.hasPermission('edu:school-accommodation-attendance:update')")
    public CommonResult<Boolean> updateSchoolAccommodationAttendanceBatch(@Valid @RequestBody SchoolAccommodationAttendanceParamsVO updateReqVO) {
        schoolAccommodationAttendanceService.updateSchoolAccommodationAttendanceBatch(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除考勤")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:school-accommodation-attendance:delete')")
    public CommonResult<Boolean> deleteSchoolAccommodationAttendance(@RequestParam("id") Integer id) {
        schoolAccommodationAttendanceService.deleteSchoolAccommodationAttendance(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得单个考勤数据")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:school-accommodation-attendance:query')")
    public CommonResult<SchoolAccommodationAttendanceRespVO> getSchoolAccommodationAttendance(@RequestParam("id") Integer id) {
        SchoolAccommodationAttendanceDO schoolAccommodationAttendance = schoolAccommodationAttendanceService.getSchoolAccommodationAttendance(id);
        return success(SchoolAccommodationAttendanceConvert.INSTANCE.convert(schoolAccommodationAttendance));
    }

    @GetMapping("/list")
    @ApiOperation("获得考勤列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:school-accommodation-attendance:query')")
    public CommonResult<List<SchoolAccommodationAttendanceRespVO>> getSchoolAccommodationAttendanceList(@RequestParam("ids") Collection<Integer> ids) {
        List<SchoolAccommodationAttendanceDO> list = schoolAccommodationAttendanceService.getSchoolAccommodationAttendanceList(ids);
        return success(SchoolAccommodationAttendanceConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/date-range-list")
    @ApiOperation("按日期范围获取日历表数据")
    @PreAuthorize("@ss.hasPermission('edu:school-accommodation-attendance:query')")
    public CommonResult<List<SchoolAccommodationAttendanceRespVO>> getSchoolAccommodationAttendancePage(@Valid SchoolAccommodationAttendancePageReqVO pageVO) {
        List<SchoolAccommodationAttendanceDO> pageResult = schoolAccommodationAttendanceService.getSchoolAccommodationAttendancePage(pageVO);
        return success(SchoolAccommodationAttendanceConvert.INSTANCE.convertList(pageResult));
    }


}
