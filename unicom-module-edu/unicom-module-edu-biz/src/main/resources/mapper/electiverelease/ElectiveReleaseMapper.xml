<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.electiverelease.ElectiveReleaseMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPageByReqVO"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.electiverelease.ElectiveReleaseDO">
        select
        distinct eer.id,
        eer."name",
        eer.selection_start_time,
        eer.selection_end_time,
        eer.class_date,
        eer.day_period,
        eer.class_start_time,
        eer.class_end_time,
        eer.create_time
        from
        edu_elective_release eer
        <if test="reqVO.classIdList != null and reqVO.classIdList.size() > 0">
            left join edu_elective_release_classes eerc on eer.id = eerc.release_id
            left join edu_class_management ecm on ecm.id = eerc.class_id
        </if>
        where
        eer.deleted = 0

        <if test="reqVO.ids != null and reqVO.ids.size() > 0">
            and eer.id in
            <foreach collection="reqVO.ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>

        <if test="reqVO.ids == null or reqVO.ids.size() == 0">
            <if test="reqVO.name != null and reqVO.name != ''">
                AND eer."name" LIKE CONCAT('%',#{reqVO.name},'%')
            </if>
        </if>
        <if test="reqVO.classIdList != null and reqVO.classIdList.size() > 0">
            and eerc.deleted = 0
            and ecm.deleted = 0
            and ecm.id in
            <foreach collection="reqVO.classIdList" item="classId" separator="," open="(" close=")">
                #{classId}
            </foreach>
        </if>
        ORDER BY
        <if test="reqVO.sortField == null or reqVO.sortField == 0">
            eer.class_start_time
        </if>
        <if test="reqVO.sortField != null and reqVO.sortField == 1">
            eer.class_date
        </if>
        <if test="reqVO.sortField != null and reqVO.sortField == 2">
            eer.create_time
        </if>
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>
    </select>

    <select id="getElectiveReleaseListByClassId"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.electiverelease.ElectiveReleaseDO">
        select
            distinct eer.id,
                     eer."name",
                     eer.selection_start_time,
                     eer.selection_end_time,
                     eer.class_date,
                     eer.day_period,
                     eer.class_start_time,
                     eer.class_end_time,
                     eer.create_time
        from
            edu_elective_release eer
                left join edu_elective_release_classes eerc on eer.id = eerc.release_id
                left join edu_class_management ecm on ecm.id = eerc.class_id
        where
            eer.deleted = 0
          and eerc.deleted = 0
          and ecm.deleted = 0
          and ecm.id = #{classId}
        ORDER BY eer.class_start_time DESC
    </select>

    <select id="getSelectionNumInfo"
            resultType="com.unicom.swdx.module.edu.controller.admin.electiverelease.dto.ReleaseAndClassSelectionInfoDTO">
        select
            eerc.release_id,
            eerc.class_id,
            count(1) selectionNum
        from edu_elective_release_classes eerc
            join edu_class_management ecm on eerc.class_id = ecm.id
            join edu_trainee et on eerc.class_id = et.class_id
        where eerc.deleted = 0
        and ecm.deleted = 0
        and et.deleted = 0
        <if test="statusList != null and statusList.size() > 0">
            and et.status in
            <foreach collection="statusList" item="status" separator="," open="(" close=")">
                #{status}
            </foreach>
        </if>
        <if test="releaseId != null">
            and eerc.release_id = #{releaseId}
        </if>
        <if test="classIdList != null and classIdList.size() > 0">
            and eerc.class_id in
            <foreach collection="classIdList" item="classId" separator="," open="(" close=")">
                #{classId}
            </foreach>
        </if>
        group by eerc.release_id, eerc.class_id
    </select>
    <select id="getSelectedNumInfo"
            resultType="com.unicom.swdx.module.edu.controller.admin.electiverelease.dto.ReleaseAndClassSelectedInfoDTO">
        select
            eets.release_id,
            et.class_id,
            count(1) selectedNum
        from edu_elective_trainee_selection eets
            left join edu_trainee et on eets.trainee_id  = et.id
        where
            eets.deleted = 0
            and et.deleted = 0
        <if test="releaseId != null">
            and eets.release_id = #{releaseId}
        </if>
        <if test="classIdList != null and classIdList.size() > 0">
            and et.class_id in
            <foreach collection="classIdList" item="classId" separator="," open="(" close=")">
                #{classId}
            </foreach>
        </if>
        group by eets.release_id, et.class_id
    </select>
    <select id="getSelectedInfoPageByReqVO"
            resultType="com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.ElectiveReleaseSelectedInfoRespVO">
        select
            eets.release_id releaseId,
            ec.id courseId,
            ec."name" courseName,
            eti.id teacherId,
            eti."name" teacherName,
            et.class_id classId,
            ecm.class_name className,
            et.id traineeId,
            et."name" traineeName,
            et.sex traineeSex,
            et."position"
        from edu_elective_trainee_selection eets
            left join edu_trainee et on eets.trainee_id  = et.id
            left join edu_class_management ecm on ecm.id = et.class_id
            left join edu_elective_release_courses eerc on eerc.id = eets.release_course_id
            left join edu_teacher_information eti on eti.id = eerc.teacher_id
            left join edu_courses ec on ec.id = eerc.course_id
        where eets.deleted = 0
            and et.deleted = 0
            and ecm.deleted = 0
            and eerc.deleted = 0
            and eti.deleted = 0
            and ec.deleted = 0
            and eets.release_id = #{reqVO.releaseId}
        <if test="reqVO.classIdList != null and reqVO.classIdList.size() > 0">
            and et.class_id in
            <foreach collection="reqVO.classIdList" item="classId" separator="," open="(" close=")">
                #{classId}
            </foreach>
        </if>
        <if test="reqVO.courseName != null and reqVO.courseName != ''">
            and ec."name" like concat('%',#{reqVO.courseName},'%')
        </if>
        <if test="reqVO.courseId != null">
            and ec.id = #{reqVO.courseId}
        </if>
        <if test="reqVO.className != null and reqVO.className != ''">
            and ecm.class_name like concat('%',#{reqVO.className},'%')
        </if>
        <if test="reqVO.classId != null">
            and ecm.id = #{reqVO.classId}
        </if>
        <if test="reqVO.traineeName != null and reqVO.traineeName != ''">
            and et."name" like concat('%',#{reqVO.traineeName},'%')
        </if>
        order by ec."name", ecm.class_name, eti."name"
    </select>

    <select id="getReleaseCoursePage"
            resultType="com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesRespVO">
        select
            eerc.release_id releaseId,
            eerc.id id,
            ec.id courseId,
            ec."name" courseName,
            eti.id teacherId,
            eti."name" teacherName,
            ecl.id classroomId,
            ecl.class_name classroomName
        from edu_elective_release_courses eerc
                 left join edu_courses ec on ec.id = eerc.course_id and ec.deleted = 0
                 left join edu_teacher_information eti on eti.id = eerc.teacher_id and eti.deleted = 0
                 left join edu_classroom_library ecl on eerc.classroom_id = ecl.id and ecl.deleted = 0
        where eerc.deleted = 0
            and eerc.release_id = #{reqVO.releaseId}
            <if test="reqVO.courseName != null and reqVO.courseName != ''">
            and ec."name" like concat('%',#{reqVO.courseName},'%')
            </if>
        order by eerc.create_time
        <if test="reqVO.isSerialDesc != null and reqVO.isSerialDesc == true">
            desc
        </if>
    </select>
    <select id="getUnselectElectiveReleaseAndCoursesListByTraineeId"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.electiverelease.ElectiveReleaseDO">
        select
            distinct eer.id,
                     eer."name",
                     eer.selection_start_time,
                     eer.selection_end_time,
                     eer.class_date,
                     eer.day_period,
                     eer.class_start_time,
                     eer.class_end_time,
                     eer.create_time
        from edu_elective_release eer
                 left join edu_elective_release_classes eerc on eerc.release_id = eer.id and eerc.deleted = 0
                 left join edu_trainee et on et.class_id = eerc.class_id and et.deleted = 0
                 left join edu_elective_trainee_selection eets on eets.trainee_id = et.id
                and eets.release_id = eer.id
                and eets.deleted = 0
        where eer.deleted = 0
          and eets.release_id is null
          and #{currentDateTime} between eer.selection_start_time and eer.selection_end_time
          and et.id = #{traineeId}
        order by eer.class_start_time desc
    </select>
    <select id="getSimpleClassesInfoList"
            resultType="com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.classInfo.ClassInfoVO"
            parameterType="java.lang.Long">
        select
            ecm.id,
            ecm.class_name className
        from edu_class_management ecm
                 join edu_elective_release_classes eerc on ecm.id = eerc.class_id and eerc.deleted = 0
        where ecm.deleted = 0
          and eerc.release_id = #{releaseId}
        order by ecm.create_time desc
    </select>
    <select id="getSimpleCoursesInfoList"
            resultType="com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.courseinfo.CourseInfoVO"
            parameterType="java.lang.Long">
        select
            ec.id ,
            ec."name" courseName
        from edu_courses ec
                 join edu_elective_release_courses eerc on ec.id = eerc.course_id and eerc.deleted = 0
        where ec.deleted = 0
        and eerc.release_id = #{releaseId}
        order by ec.create_time desc
    </select>
</mapper>















