package com.unicom.swdx.module.edu.controller.admin.graduationcertificatenumbers.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@ApiModel("管理后台-调训系统-学员结业证书分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GraduationCertificateNumbersPageReqVO extends PageParam {

    @ApiModelProperty(value = "班级ID", required = true, example = "1")
    @NotNull(message = "班级ID不能为空")
    private Long classId;

    @ApiModelProperty(value = "学员姓名 （用于学员评估详情）")
    private String traineeName;
}