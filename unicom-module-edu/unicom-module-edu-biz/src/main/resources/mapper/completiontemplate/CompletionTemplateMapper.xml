<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.completiontemplate.CompletionTemplateMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="selectByTemplateName" resultType="com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO">
        select
            id,
            serial_number,
            column_name,
            conversion_announcement,
            max_score,
            initial_score,
            acquisition_mode,
            data_source,
            assessment_name,
            campus,
            default_rule,
            template_name,
            module_name,
            id_code
        from
            edu_completion_template
        WHERE
            "deleted" = 0
          AND
            template_name LIKE #{templateName}
    </select>

    <select id="selectByDefaultRule" resultType="com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO">
        select
            id,
            serial_number,
            column_name,
            conversion_announcement,
            max_score,
            initial_score,
            acquisition_mode,
            data_source,
            assessment_name,
            campus,
            default_rule,
            template_name,
            module_name,
            id_code
        from
            edu_completion_template
        WHERE
            "deleted" = 0
          AND
            "default_rule" = #{defaultRule}
          AND
            "campus" = #{campus}
    </select>
    <update id="deleteCompletionTemplateByName">
        UPDATE
            edu_completion_template
        SET
            "deleted" = '1'
        WHERE
            id_code = #{idCode}
        and
            "deleted" = 0
    </update>

    <select id="selectByTemplateNameEdit" resultType="com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO">
        select
            id,
            serial_number,
            column_name,
            conversion_announcement,
            max_score,
            initial_score,
            acquisition_mode,
            data_source,
            assessment_name,
            campus,
            default_rule,
            template_name,
            module_name,
            id_code
        from
            edu_completion_template
        WHERE
            "deleted" = 0
          AND
            template_name LIKE #{templateName}
          AND
            id_code != #{idCode}
    </select>
    <select id="selectByDefaultRuleEdit" resultType="com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO">
        select
            id,
            serial_number,
            column_name,
            conversion_announcement,
            max_score,
            initial_score,
            acquisition_mode,
            data_source,
            assessment_name,
            campus,
            default_rule,
            template_name,
            module_name,
            id_code
        from
            edu_completion_template
        WHERE
            "deleted" = 0
          AND
            campus = #{campus}
          AND
            "default_rule" = #{defaultRule}
          AND
            "id_code" != #{idCode}
    </select>

    <select id="selectOneByIdCode" resultType="com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO">
        select
            id,
            serial_number,
            column_name,
            conversion_announcement,
            max_score,
            initial_score,
            acquisition_mode,
            data_source,
            assessment_name,
            campus,
            default_rule,
            template_name,
            module_name,
            id_code,
            create_time,
            builtin_template
        from
            edu_completion_template
        WHERE
            "deleted" = 0
          AND
            id_code = #{idCode}
        ORDER BY
        CASE
        WHEN serial_number LIKE 'ST%' THEN 2  -- ST 开头的排在最后
        ELSE 1  -- 非 ST 开头的排在前
        END ASC,  -- 先按是否为 ST 开头排序
        CASE
        WHEN serial_number LIKE 'ST%' THEN ''  -- ST 开头的不参与字母排序
        ELSE LEFT(serial_number, 1)  -- 非 ST 开头的按前缀字母排序
        END ASC,  -- 按字母顺序排序
        CASE
        WHEN serial_number LIKE 'ST%' THEN CAST(SUBSTRING(serial_number, 3) AS INTEGER)  -- ST 开头的按数字排序
        ELSE CAST(SUBSTRING(serial_number, 2) AS INTEGER)  -- 非 ST 开头的按数字排序
        END ASC  -- 按数字部分排序
    </select>

    <select id="selectPageListAll" resultType="com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO">
        SELECT
            *
        FROM
            (
                SELECT
                    DISTINCT ON
                    ("template_name") "id",
                    "serial_number",
                    "column_name",
                    "conversion_announcement",
                    "max_score",
                    "initial_score",
                    "acquisition_mode",
                    "data_source",
                    "assessment_name",
                    "campus",
                    "default_rule",
                    "template_name",
                    "create_time",
                    "module_name",
                    "id_code"
                FROM
                    "edu_completion_template"
                WHERE
                    "deleted" = '0'
                <if test="reqVO.templateName != '' and reqVO.templateName != null">
                    AND "template_name" LIKE CONCAT('%', #{reqVO.templateName}, '%')
                </if>
                ORDER BY
                    "template_name",
                    "create_time"
            ) AS t
        ORDER BY
            t ."create_time" DESC
    </select>

    <select id="selectPageList" resultType="com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO">
        SELECT
        *
        FROM
        (
        SELECT
        DISTINCT ON
        ("template_name", "builtin_template") "id",
        "serial_number",
        "column_name",
        "conversion_announcement",
        "max_score",
        "initial_score",
        "acquisition_mode",
        "data_source",
        "assessment_name",
        "campus",
        "default_rule",
        "template_name",
        "create_time",
        "module_name",
        "id_code",
        "builtin_template"
        FROM
        "edu_completion_template"
        WHERE
        "deleted" = '0'
        <if test="reqVO.templateName != '' and reqVO.templateName != null">
            AND "template_name" LIKE CONCAT('%', #{reqVO.templateName}, '%')
        </if>
        ORDER BY
        "builtin_template" desc,
        "template_name",
        "create_time"
        ) AS t
        ORDER BY
        t ."create_time" DESC
    </select>
    <select id="selectListAll" resultType="com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO">
        SELECT
        *
        FROM
        (
        SELECT
        DISTINCT ON
        ("template_name") "id",
        "serial_number",
        "column_name",
        "conversion_announcement",
        "max_score",
        "initial_score",
        "acquisition_mode",
        "data_source",
        "assessment_name",
        "campus",
        "default_rule",
        "template_name",
        "create_time",
        "module_name",
        "id_code"
        FROM
        "edu_completion_template"
        WHERE
        "deleted" = '0'
        <if test="reqVO.templateName != '' and reqVO.templateName != null">
            AND "template_name" LIKE CONCAT('%', #{reqVO.templateName}, '%')
        </if>
        ORDER BY
        "template_name",
        "create_time"
        ) AS t
        ORDER BY
        t ."create_time" DESC
    </select>

    <select id="selectCountByIdCode" resultType="java.lang.Integer">
        select
            count(1)
        from
            edu_class_management
        where
            "deleted" = '0'
        and
            "id_code" = #{idCode}
    </select>

    <select id="selectClassDefaultRule"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.classcompletiontemplate.ClassCompletionTemplateDO">
        select
            *
        from
            edu_completion_template
        where
            "deleted" = 0
          and
            campus = #{campus}
          and
            "default_rule" = #{defaultRule}
        ORDER BY (REGEXP_REPLACE(serial_number, '[^0-9]', ''))::INTEGER, serial_number
    </select>

    <select id="getDefaultTemplateByIdCode"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO">
        select
        id,
        serial_number,
        column_name,
        conversion_announcement,
        max_score,
        initial_score,
        acquisition_mode,
        data_source,
        assessment_name,
        campus,
        default_rule,
        template_name,
        create_time,
        update_time,
        deleted,
        tenant_id,
        creator,
        updater,
        module_name,
        id_code
        from
        edu_completion_template
        where
        deleted = 0
        and id_code = #{idCode}
        <if test="rule != null">
            and default_rule = #{rule}
        </if>
<!--        and (assessment_name = '考试成绩 (原分)'-->
<!--        or assessment_name = '结业论文成绩 (原分)'-->
<!--        or assessment_name = '学习心得 (原分)'-->
<!--        or column_name = '量化得分')-->
    </select>
    
    <select id="getDefaultTemplateByCampus" resultType="java.lang.String">
        select distinct id_code
        from edu_completion_template
        where deleted = 0
            and default_rule = 0
            and campus = #{campus}
    </select>
    
    <select id="getMainCampus" resultType="java.lang.Long">
        select
            id
        from
            system_dict_data sdd
        where
            dict_type like 'edu_classroom_campus'
          and "label" like '韶山校区'
          and deleted = 0
          and tenant_id = #{tenantId}
    </select>
</mapper>
