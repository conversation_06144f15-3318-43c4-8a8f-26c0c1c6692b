<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.signupunit.SignUpUnitMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <update id="updateSignUpUnitRestrictById">
        update
            edu_sign_up_unit
        set
            "is_restrict" = #{restrict}
        where
            id = #{id}
    </update>

    <select id="selectByUnitName" resultType="com.unicom.swdx.module.edu.dal.dataobject.signupunit.SignUpUnitDO">
        SELECT
            "id",
            "unit_name",
            "unit_classification",
            "unit_charge_people",
            "phone",
            "office_phone"
            "status",
            "sort",
            "is_restrict"
        FROM
            edu_sign_up_unit
        WHERE
            "deleted" = '0'
          and
             template = '1'
          and
            "unit_name" = #{unitName}
    </select>

    <select id="getSignUpUnitDOList" resultType="com.unicom.swdx.module.edu.dal.dataobject.signupunit.SignUpUnitDO">
        SELECT
            "id",
            "unit_name",
            "unit_classification",
            "unit_charge_people",
            "phone",
            "status",
            "sort",
            "is_restrict"
        FROM
            edu_sign_up_unit
        WHERE
            "deleted" = '0'
        and
            template = '1'
    </select>

    <delete id="deleteByClassId">
        DELETE FROM edu_sign_up_unit
        WHERE class_id = #{id}
    </delete>

    <select id="selectByClassIdAndUnitName" resultType="com.unicom.swdx.module.edu.dal.dataobject.signupunit.SignUpUnitDO">
        SELECT
            *
        FROM
            edu_sign_up_unit
        WHERE
            "deleted" = '0'
          and
            template = '0'
          and
            "unit_name" like #{unitName}
          and
            "class_id" = #{classId}
    </select>
    <select id="selectByClassIdAndParentId" resultType="com.unicom.swdx.module.edu.dal.dataobject.signupunit.SignUpUnitDO">
        SELECT
        *
        FROM
        edu_sign_up_unit
        WHERE
        "deleted" = '0'
        and
        template = '0'
        and
        parent_id = #{unitId}
        and
        "class_id" = #{classId}
    </select>
    <select id="getUnitType"
            resultType="com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoImportDictLableExcelVO">
        SELECT
            sdd."label"
        FROM
            system_dict_data sdd
        WHERE
            sdd ."deleted" = '0'
          and "dict_type" like 'edu_class_unitType'
    </select>
    <select id="getSignUnitClassification" resultType="java.util.Map">
        SELECT
            sdd."label",sdd.id
        FROM
            system_dict_data sdd
        WHERE
            sdd ."deleted" = '0'  and "dict_type" like 'edu_class_unitType'
    </select>
    <select id="countByPhone" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(*)  from edu_sign_up_unit WHERE "deleted" = '0' and phone = #{phone} and template = '1'
    </select>
    <select id="getPhoneList" resultType="java.lang.String">
        select distinct phone from edu_sign_up_unit where deleted = '0' and template = '1'
    </select>
</mapper>
