<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.questionmanagement.QuestionManagementMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <sql id="selectQuestionManagementRespVO">
        SELECT
        pqm.id id,
        pqm.title title,
        pqm.stem stem,
        pqm.question_type questionType,
        pqm.score score
        FROM
        pg_question_management pqm

        <where>
            pqm.deleted = 0
            <if test="reqVO.title != null and reqVO.title != ''">
                AND pqm.title like CONCAT('%', #{reqVO.title}, '%')
            </if>
            <if test="reqVO.questionType != null and reqVO.questionType != ''">
                AND pqm.question_type = #{reqVO.questionType}
            </if>
            <if test="reqVO.categoryId != null">
                AND pqm.category_id in
                (SELECT id FROM
                (select * from pg_question_category_management START WITH Id = #{reqVO.categoryId}
                CONNECT BY  PRIOR ID = PARENT_ID)
                where deleted = 0)
            </if>
        </where>
        ORDER BY
        <if test="reqVO.sortField == null or reqVO.sortField == 0">
            pqm.id
        </if>
        <if test="reqVO.sortField != null and reqVO.sortField == 1">
            pqm.question_type
        </if>
        <if test="reqVO.isDesc != null and reqVO.isDesc == false">
            ASC
        </if>
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>

    </sql>

    <update id="moveToDefaultCategory">
        update pg_question_management
        set category_id = #{defaultId}
        where category_id in
            (select id from
            (SELECT * FROM pg_question_category_management START WITH Id = #{currentId}
             CONNECT BY  PRIOR ID = PARENT_ID)
                              where DELETED = 0)
    </update>
    <select id="selectPageByPageVO"
            resultType="com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.QuestionManagementRespVO">
        <include refid="selectQuestionManagementRespVO"/>
    </select>
    <select id="selectQuestionList"
            resultType="com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.QuestionRespVO">
        select
        id questionId,
        title,
        stem,
        question_type,
        score as questionScore,
        category_id as parentId
        from pg_question_management
        where deleted = 0
    </select>
    <select id="selectBuiltInQuestionId" resultType="java.lang.Long" parameterType="java.lang.Long">
        select
        id
        from pg_question_management
        where deleted = 0
        and category_id = #{categoryId}
        order by id asc
        limit 5
    </select>
</mapper>